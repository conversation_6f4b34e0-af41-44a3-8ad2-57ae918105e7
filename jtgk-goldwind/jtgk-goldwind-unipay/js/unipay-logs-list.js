/**
 * 待付池-列表
 * /apps/jtgk/jfkj/extend/unipay-logs-list.js
 */

var isInit = true;
var isFromPlanBill = false;

idp.event.bind("domReady", function() {
	idp.event.register("grid_main","beforeGridFilter",function(e,filter){
		isFromPlanBill = idp.utils.getQuery("isFromPlanBill");
		if (isInit && !isFromPlanBill) {
			// 日期过滤
			if (filter.length > 0) {
				filter[filter.length - 1].Logic = "and";
			}
			filter.push({
				"Left": "",
				"Field": "JTGKPAYMENTINFO.EXPECTPAYDATE",
				"Operate": ">=",
				"IsExpress": false,
				"Value": getCurrentMonthFirstDay(),
				"Right": "",
				"Logic": "and"
			});
			filter.push({
				"Left": "",
				"Field": "JTGKPAYMENTINFO.EXPECTPAYDATE",
				"Operate": "<=",
				"IsExpress": false,
				"Value": getCurrentMonthLastDay(),
				"Right": "",
				"Logic": "and"
			});
			// "Value": idp.utils.jse("0, 1, 2, -2, -3, 3"),
			// filter.push({
			// 	"Left": "",
			// 	"Field": "JTGKPAYMENTINFO.DOCSTATUS",
			// 	"Operate": "in",
			// 	"IsExpress": false,
			// 	"Value": idp.utils.jse("1"),
			// 	"Right": "",
			// 	"Logic": "and"
			// });
		}

		if (filter.length > 0) {
			filter[filter.length - 1].Logic = "and";
		}

		// 根据第一维度（来源系统）过滤
		var fdim = idp.utils.getQuery("fdim");
		if (fdim === null || fdim === "" ) {
			if (isFromPlanBill) {
				$('td[columnname="SUMMARY"] .lee-grid-hd-cell-text').text('采购付款申请备注').attr('title', '采购付款申请备注');
				$('#lbl_SUMMARY').text('采购付款申请备注').attr('title', '采购付款申请备注');

				// 显示下面的内容
				$(idp.control.get("RECEIVINGUNITCODE").element).closest('div.table-item').show();
				$(idp.control.get("EXTPURCHASECODE").element).closest('div.table-item').show();
				$(idp.control.get("TXT01").element).closest('div.table-item').show();
				$(idp.control.get("SUMMARY").element).closest('div.table-item').show();

				// 隐藏下面内容
				$(idp.control.get("SRCBIZSYS").element).closest('div.table-item').hide();
				$(idp.control.get("PAYACCOUNTNO").element).closest('div.table-item').hide();
				$(idp.control.get("EXPECTPAYDATE").element).closest('div.table-item').hide();
				$(idp.control.get("REQUESTAMOUNT").element).closest('div.table-item').hide();
				$(idp.control.get("DOCSTATUS").element).closest('div.table-item').hide();
				document.getElementById("toolbar1").style.display = "none";

				// 只要sfs排程的单子
				filter.push({
					"Left": "",
					"Field": "JTGKPAYMENTINFO.SRCBIZSYS",
					"Operate": "=",
					"IsExpress": false,
					"Value": 'SFS-PAY',
					"Right": "",
					"Logic": "and"
				});

				// 剩余金额大于0
				filter.push({
					"Left": "",
					"Field": "JTGKPAYMENTINFO.UNPAYAMOUNT",
					"Operate": ">",
					"IsExpress": false,
					"Value": 0,
				});
			}
			
			return filter;
		} else{
			// 来源系统标识
			$("#SRCBIZSYS").parent().parent().parent().css("display", "none");
		}

        filter.push({
            "Left": "",
            "Field": "JTGKPAYMENTINFO.SRCBIZSYS",
            "Operate": "=",
            "IsExpress": false,
            "Value": fdim,
            "Right": "",
            "Logic": "and"
        });
        return filter;
    });
    //注册列表选中行后事件
    idp.event.register('grid_main', 'selectRow', function (e, data) {
        calculateTotalAmountSeleted();
	});
    idp.event.register('grid_main', 'unSelectRow', function () {
        calculateTotalAmountSeleted();
    });
});

idp.event.bind("viewReady", function() {
	isFromPlanBill = idp.utils.getQuery("isFromPlanBill");
	setTimeout(function() {
		isInit = false;
		if (!isFromPlanBill) {
			// 期望付款日期
			var firstDay = getCurrentMonthFirstDay();
			idp.control.get("EXPECTPAYDATE_1").setValue(firstDay);
			var lastDay = getCurrentMonthLastDay();
			idp.control.get("EXPECTPAYDATE_2").setValue(lastDay);
			// idp.control.get("DOCSTATUS").setValue('0;1;2;-2;-3;3');
			// idp.control.get("DOCSTATUS").setValue('1');
		}else {
			$('td[columnname="SUMMARY"] .lee-grid-hd-cell-text').text('采购付款申请备注').attr('title', '采购付款申请备注');
			$('#lbl_SUMMARY').text('采购付款申请备注').attr('title', '采购付款申请备注');
		}
	}, 500);
});
// 获取当月第一天
function getCurrentMonthFirstDay() {
	var today = new Date();
	var year = today.getFullYear();
	var month = today.getMonth() + 1;
	var day = today.getDate();
	var hour = today.getHours();
	var minute = today.getMinutes();
	var second = today.getSeconds();
	var millisecond = today.getMilliseconds();
	return year + "-" + (month < 10 ? "0" + month : month) + "-" + "01" + " " + (hour < 10 ? "0" + hour : hour) + ":" + (minute < 10 ? "0" + minute : minute) + ":" + (second < 10 ? "0" + second : second) + "." + millisecond;
}
// 获取当月最后一天
function getCurrentMonthLastDay() {
	var today = new Date();
	var year = today.getFullYear();
	var month = today.getMonth();
	var lastDate = new Date(year, month+1, 0);
	var lastDay = lastDate.getDate();
	var lastHour = lastDate.getHours();
	var lastMinute = lastDate.getMinutes();
	var lastSecond = lastDate.getSeconds();
	var lastMillisecond = lastDate.getMilliseconds();
	return year + "-" + (month+1 < 10 ? "0" + (month+1) : (month+1)) + "-" + (lastDay < 10 ? "0" + lastDay : lastDay) + " " + (lastHour < 10 ? "0" + lastHour : lastHour) + ":" + (lastMinute < 10 ? "0" + lastMinute : lastMinute) + ":" + (lastSecond < 10 ? "0" + lastSecond : lastSecond) + "." + lastMillisecond;
}

/** 勾选付款申请后显示行数、合计金额 */
function calculateTotalAmountSeleted(){
    var selectedRows = idp.control.get("grid_main").selected;
    //var amountArrary = [];
    if (selectedRows === null || selectedRows.length === 0) {
		return;
	}
    var totalAmount = 0.00;
    var totalCount = selectedRows.length;
	for(var i = 0; i < selectedRows.length; i++){
		totalAmount += selectedRows[i].UNPAYAMOUNT;
	}
    var showMessage = "已选{0}笔，合计待付款金额：{1}".replace('{0}', totalCount).replace('{1}', idp.utils.currency(totalAmount, 2, true));
    var totalObj = $("#grid_main .lee-panel-footer #div_total");
    if(totalObj && totalObj.length > 0){
        $("#grid_main .lee-panel-footer #div_total")[0].innerText = showMessage;
    }else{
        $('<div id=\"div_total\" style=\"font-size:14px;margin-top:6px;\">' + showMessage + '</div>').appendTo($("#grid_main .lee-panel-footer"));
    }
}

/* 勾选付款申请发起付款安排单 */
function process() {
    var rows = idp.control.get("grid_main").getSelecteds();
    if (rows.length === 0) {
		idp.error("请先选择要操作的付款申请！");
        return;
    }
	var docIds = "";
	var billPayWay = rows[0].BILLPAYWAY;
	for (var i=0; i<rows.length; i++) {
        if (rows[i].SRCBIZSYS !== "SFS-PAY") {
            idp.error("第" + (i+1) + "行来源系统不允许进行付款安排");
            return false;
        }
        if (rows[i].ISDIRECTPAY !== "0") {
            idp.error("第" + (i+1) + "行付款申请不允许进行付款安排");
            return false;
        }
        if (rows[i].BILLPAYWAY !== billPayWay) {
            idp.error("第" + (i+1) + "行付款申请票据支付方式不一致");
            return false;
        }
        if (rows[i].DOCSTATUS !== 1) {
            idp.error("第" + (i+1) + "行付款申请当前状态不能付款安排");
            return false;
        }
		if (rows[i].UNPAYAMOUNT <= 0) {
            idp.error("第" + (i+1) + "行剩余待付款金额为0");
            return false;
		}
		if (i > 0) {
			docIds = docIds + ",";
		}
		docIds = docIds + rows[i].ID;
	}
	// SFS做付款安排
	var newUrl = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=44bb6838-9b25-8e1a-c885-b5c7b66f83e4&status=add&runtime=true&DATASRC=1"
		+ "&LOGINFOID=" + docIds;
	idp.utils.openurl("44bb6838-9b25-8e1a-c885-b5c7b66f83e4", "付款安排详情", newUrl);
}

/** 勾选付款申请退回到业务系统 */
function cancel() {
    var rows = idp.control.get("grid_main").getSelecteds();
    if (rows.length === 0) {
		idp.error("请先选择要操作的付款申请！");
        return false;
    }
	for (var i=0; i<rows.length; i++) {
		if (rows[i].SRCBIZSYS !== "SFS-PAY"){
			idp.error("第" + (i+1) + "来源系统不是SFS，请在结算办理操作退回");
			return false;
		}
		if (rows[i].UNPAYAMOUNT !== rows[i].REQUESTAMOUNT ) {
			idp.error("第" + (i+1) + "申请金额不等于剩余待付款金额，不允许退回到业务系统");
			return false;
		}
		if (rows[i].DOCSTATUS !== 1 && rows[i].DOCSTATUS !== -2) {
			idp.error("第" + (i+1) + "行付款安排单当前状态不允许退回到来源系统");
			return false;
		}
	}
	const dg = $.leeDialog.open({
		title: "退回业务系统",
		name: 'cancelPayDialog',
		width: 500,
		height: 200,
		url: "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=fe2aa304-c6e1-1d96-f7c9-7a33969f9204&status=add",
		buttons: [
			{
				id: "cancelPayDialog_cancel",
				text: "取消",
				cls: 'lee-dialog-btn-cancel ',
				onclick: (item, dialog) => {
					dialog.close();
				}
			},
			{
				id: "cancelDealDialog_register",
				text: "确定",
				cls: 'lee-btn-primary lee-dialog-btn-ok',
				onclick: (item, dialog) => {
					var dialogThis = dialog.jiframe[0].contentWindow;
					var thyy = dialog.jiframe[0].contentWindow.$("#input_REASON").val();
					if (thyy.length === 0) {
						idp.info("退回原因不能为空！");
						return;
					}
					idp.loading();
					for (var i=0; i<rows.length; i++) {
						var params = {
							ID: rows[i].ID,
							REASON: thyy
						};
						var cancelResponse = idp.service.fetch("/api/jtgk/goldwind/settlement/v1.0/backStatus", params, false, "POST");
						if (cancelResponse.status !== 200) {
							idp.loaded();
							idp.error("请求后端接口失败");
							break;
						}
						var cancelResult = JSON.parse(cancelResponse.responseText);
						if (!cancelResult.result) {
							idp.loaded();
							idp.error(cancelResult.message);
							break;
						}
					}
					idp.loaded();
					dialog.close();
					// 重新刷新列表
					idp.control.get("grid_main").loadData();
					return true;
				}
			},
		]
	});
	return true;
}

// 联查共享单据
function checkFSSCBill() {
	console.info("打开共享系统单据");

	let splitURL = "";

	var rows = idp.control.get("grid_main").getSelecteds();
	if (rows.length > 1) {
		idp.error("不支持多选联查")
		return false;
	}else if (rows[0].SRCBIZSYS == "FSSC") {
		let obtianedSplitURL = idp.service.fetch('/api/jtgk/goldwind/settlement/v1.0/obtainFSSCURL', {
			ACCOUNT: idp.context.get("UserId"),
			linkURL: rows[0].LINKURL
		}, false, 'POST');
		if (obtianedSplitURL.status !== 200) {
			idp.error("请求服务端接口失败");
			return false;
		}
		var obtianedSplitResult = JSON.parse(obtianedSplitURL.responseText);
		if (!obtianedSplitResult.result) {
			idp.error(saveCheckResultMsgs.message);
			return false;
		}
		splitURL = obtianedSplitResult.data;
	}else if (rows[0].SRCBIZSYS == "SFS-XD") {
		// 在新标签中打开
		window.open(rows[0].LINKURL, '_blank');
		return true;
	}else if (rows[0].SRCBIZSYS == "CES") {
		// 在新标签中打开
		var params = {}
        params.SRCDOCID = rows[0].SRCDOCID;
        params.USERID= idp.context.get("UserId");
        params.USERCODE=idp.context.get("UserCode");
        let url = "/api/jtgk/goldwind/ckgl/v1.0/getCeslcdz";
        let result = idp.service.fetch(url, params, false, "POST")
        if (result) {
			if (result.responseJSON.result) {
				splitURL = result.responseJSON.lcurl;//返回CES联查地址
				window.open(splitURL, '_blank'); 
				return true;
			}  
		}
		if (!splitURL) {
			idp.error("获取CES联查地址失败");
			return false;
		}
	}else if (rows[0].SRCBIZSYS == "HLY") {
		// 在新标签中打开
		let obtianedSplitURL = idp.service.fetch('/api/jtgk/goldwind/settlement/v1.0/obtainHLYURL', {
			ACCOUNT: idp.context.get("UserId"),
			linkURL: rows[0].LINKURL
		}, false, 'POST');
		if (obtianedSplitURL.status !== 200) {
			idp.error("请求服务端接口失败");
			return false;
		}
		var obtianedSplitResult = JSON.parse(obtianedSplitURL.responseText);
		if (!obtianedSplitResult.result) {
			idp.error(saveCheckResultMsgs.message);
			return false;
		}
		splitURL = obtianedSplitResult.data;
	}else{
		idp.error("暂不支持该来源系统单据联查")
		return false;
	}

	// 打开局部窗口
	$.leeDialog.open({
		title: "来源单据联查",
		name: 'FSSCBillQuery',
		width: Math.max(window.innerWidth * 0.6, 900),
		height: Math.max(window.innerHeight * 0.6, 500),
		url: splitURL,
		buttons: [{
			id: "dialog_lookup_cancel",
			text: "取消",
			cls: 'lee-dialog-btn-cancel ',
			onclick: function (item, dialog) {
				dialog.close()
			}
		}, {
			id: "dialog_lookup_register",
			//确认
			text: "确认",
			cls: 'lee-btn-primary lee-dialog-btn-ok',
			onclick: function (item, dialog) {
				dialog.close()
			}
		},
		]
	});

}

/** 拆分 */
function split() {
	console.info("打开共享系统拆分页面");
	var rows = idp.control.get("grid_main").getSelecteds();
	if (rows.length > 1) {
		idp.error("拆分不支持多条待付池单据")
		return false;
	}else if (rows[0].SRCBIZSYS !== "FSSC") {
		idp.error("拆分不支持非共享单据")
		return false;
	}

	let obtianedSplitURL = idp.service.fetch('/api/jtgk/goldwind/settlement/v1.0/obtainSplitURL', {
		ACCOUNT: idp.context.get("UserId"),
		SRC_DOC_ID: rows[0].SRCDOCID
	}, false, 'POST');
	if (obtianedSplitURL.status !== 200) {
		idp.error("请求服务端接口失败");
		return false;
	}
	var obtianedSplitResult = JSON.parse(obtianedSplitURL.responseText);
	if (!obtianedSplitResult.result) {
		idp.error(saveCheckResultMsgs.message);
		return false;
	}
	let splitURL = obtianedSplitResult.data;
	// 打开局部窗口
	$.leeDialog.open({
		title: "共享单据拆分",
		name: 'FSSCSplit',
		width: Math.max(window.innerWidth * 0.6, 900),
		height: Math.max(window.innerHeight * 0.6, 500),
		url: splitURL,
		buttons: [{
			id: "dialog_lookup_cancel",
			text: "取消",
			cls: 'lee-dialog-btn-cancel ',
			onclick: function (item, dialog) {
				dialog.close()
			}
		}, {
				id: "dialog_lookup_register",
				//确认
				text: "确认",
				cls: 'lee-btn-primary lee-dialog-btn-ok',
				onclick: function (item, dialog) {
					dialog.close()
				}
			},
		]
	});
}

/** 查看明细 */
function viewCard() {
	var row = idp.control.get('grid_main').getSelected();
	if (!row) {
		idp.warn("请先选择要操作的付款申请");
		return false;
	}
	var srcBizSys = row.SRCBIZSYS;
	var url = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=ee9d8ca8-a1ca-0330-1cf9-6b4fa212cb6c&status=view"
		+ "&runtime=true&dataid=" + row.ID + "&fdim=" + srcBizSys;
	idp.utils.openurl(row.ID, '付款申请详情', url);
}

/** 查看付款安排记录 */
function viewPlan() {
	var row = idp.control.get('grid_main').getSelected();
	if (!row) {
		idp.warn("请先选择要操作的付款申请");
		return false;
	}
	var url = "/apps/fastdweb/views/runtime/page/query/querypreview.html?styleid=a5d96a9e-b0e1-6c21-0ff0-67052fb5ac52&LOGINFOID=" + row.ID;
	idp.utils.openurl(row.ID, '付款安排记录', url);
}

/** 查看执行记录 */
function viewDetail() {
	var row = idp.control.get('grid_main').getSelected();
	if (!row) {
		idp.warn("请先选择要操作的付款申请");
		return false;
	}
	var url = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=e0ac1e92-7f88-17bf-0770-42fb070205cd&status=view&dataid=" + row.ID;
	idp.utils.openurl(row.ID, '付款申请执行记录', url);
}

/** 电子影像 */
function viewImage() {
	var gridMain = idp.control.get("grid_main");
	var checkedRows = gridMain.getCheckedRows();
	if (checkedRows.length == 0) {
		idp.warn("请先选择要操作的付款申请");
		return false;
	}
    var currentData = checkedRows[0];
    if (currentData.ID == 'null' || currentData.ID == '' || currentData.ID == 'undefined') {
        return false;
    }
	var docStatus = String(currentData.DOCSTATUS);
	var billCateGory = 'jtgkPaymentInfo';
	var billywtype = 'jtgkPaymentInfo';
	var billtype = 'jtgkPaymentInfo';
	var imageOperation = 'view';
	var billstate = 'SAVE';
	var billId = currentData.ID;
	var billCode = currentData.SRCDOCNO;
	var orgId = currentData.PAYUNITID;
	var tabId = idp.utils.getQuery('styleid');
    const param = {
        SourceSys: 'IDP', // 来源系统
        BillCATEGORY: billCateGory, // 业务大类
        BillType: billtype, // 单据类型（编号）
        BillTypeID: billtype, // 单据类型ID
        BillNM: billId, // 单据ID
        BillCODE: billCode, // 单据编号
        DWBH: orgId, // 行政组织ID
        OPERATION: imageOperation,
        USERCODE: idp.context.get("UserCode"),
        BILLSTATE: null,
        TabID: tabId,
        IsInvoice: null,
        IsShowInvoice: null,
        autoocr: null,
    };
    idp.service.fetch('/api/BP/EIS/v1.0/imageapi/getyxurlmap', param, true, '').then(rtnInfo => {
        if (rtnInfo.code == "ok") {
            url = rtnInfo.data;
            idp.openurl(billId, "电子影像", url, '', '');
        } else {
            idp.error(rtnInfo.msg);
            return false;
        }
    }).fail(result => {
        idp.error(result.message);
    });
}

/** 导出 */
function importExcel() {
	return idp.func.export();
}


function pause(){
	var rows = idp.control.get("grid_main").getSelecteds();
    if (rows.length === 0) {
		idp.error("请先选择要操作的付款申请！");
        return false;
    }

	let billIDs = [];

	for (var i=0; i<rows.length; i++) {
		if (rows[i].SRCBIZSYS !== "SFS-PAY") {
			idp.error("第" + (i+1) + "行来源系统不允许进行挂起");
            return false;
		}
		if (rows[i].DOCSTATUS !== 1) {
			idp.error("第" + (i+1) + "行付款申请当前状态不允许挂起");
			return false;
		}
		if (rows[i].UNPAYAMOUNT <= 0) {
			idp.error("第" + (i+1) + "行剩余待付款金额为0，不允许挂起");
			return false;
		}
		if (rows[i].ISDIRECTPAY !== "0") {
			idp.error("第" + (i+1) + "行付款申请不允许挂起");
			return false;
		}

		billIDs.push(rows[i].ID);
	}

	// 
	let pauseResponse = idp.service.fetch('/api/jtgk/goldwind/settlement/v1.0/pause', {
		billIDs: billIDs
	}, false, 'POST');
	if (pauseResponse.status !== 200) {
		idp.error("请求服务端接口失败");
		return false;
	}
	var pauseResult = JSON.parse(pauseResponse.responseText);
	if (!pauseResult.result) {
		idp.error(pauseResult.message);
		return false;
	}

	idp.info("挂起成功");
	idp.func.refresh('grid_main')

	return true;
}

function cancelpause(){
	var rows = idp.control.get("grid_main").getSelecteds();
    if (rows.length === 0) {
		idp.error("请先选择要操作的付款申请！");
		return false;
	}

	let billIDs = [];

	for (var i=0; i<rows.length; i++) {
		if (rows[i].SRCBIZSYS !== "SFS-PAY") {
			idp.error("第" + (i+1) + "行来源系统不允许取消挂起");
			return false;
		}

		if (rows[i].DOCSTATUS !== -4) {
			idp.error("第" + (i+1) + "行付款申请当前状态不允许取消挂起");
			return false;
		}

		if (rows[i].ISDIRECTPAY !== "0") {
			idp.error("第" + (i+1) + "行付款申请不允许取消挂起");
			return false;
		}
		billIDs.push(rows[i].ID);
	}

	let cancelpauseResponse = idp.service.fetch('/api/jtgk/goldwind/settlement/v1.0/cancelpause', {
		billIDs: billIDs
	}, false, 'POST');
	if (cancelpauseResponse.status !== 200) {
		idp.error("请求服务端接口失败");
		return false;
	}
	var cancelpauseResult = JSON.parse(cancelpauseResponse.responseText);
	if (!cancelpauseResult.result) {
		idp.error(cancelpauseResult.message);
		return false;
	}

	idp.info("取消挂起成功");
	idp.func.refresh('grid_main')

	return true;
}