/**
 * 待付池-付款安排复核-列表
 * /apps/jtgk/jfkj/extend/unipay-plancheck-list.js
 */
idp.event.bind("domReady", function() {
    idp.event.register("grid_main", "selectRow", setToolbarButtonsEnabled);
});

function setToolbarButtonsEnabled(e, data) {

	let different = false;
	let preStatus = "";
	let status = "";

	var data = idp.control.get('grid_main').selected;
	for (var i = 0; i < data.length; i++) {
		status = data[i].DOCSTATUS
		if (preStatus !== "" && status !== preStatus){
			different = true
		}
		preStatus = status
	}

	if (status === 2 || different){
		idp.control.get("toolbar1").setDisabled("baritem_modify");
		idp.control.get("toolbar1").setDisabled("baritem_submit");
		idp.control.get("toolbar1").setDisabled("baritem_cancelSubmit");
		idp.control.get("toolbar1").setDisabled("baritem_modify");
		idp.control.get("toolbar1").setDisabled("baritem_submit_new");
		idp.control.get("toolbar1").setDisabled("baritem_retract_new");
	}else if(status === 0 || status === 4){
		idp.control.get("toolbar1").setEnabled("baritem_modify");
		idp.control.get("toolbar1").setEnabled("baritem_submit");
		idp.control.get("toolbar1").setEnabled("baritem_cancelSubmit");
		idp.control.get("toolbar1").setEnabled("baritem_modify");
		idp.control.get("toolbar1").setEnabled("baritem_submit_new");
		idp.control.get("toolbar1").setEnabled("baritem_retract_new");
	}
}

/** 查看 */
function onView() {
	var data = idp.control.get('grid_main').selected;
	for (var i = 0; i < data.length; i++) {
		var newUrl = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=ad2b927e-689f-d432-c8e6-bb6b642beb76&status=view&runtime=true"
			+ "&dataid=" + data[i].ID;
		idp.utils.openurl(data[i].ID, "付款安排复核详情", newUrl);
	}
	return true;
}

/** 编辑 */
function onEditList() {
	var data = idp.control.get('grid_main').selected;
	for (var i = 0; i < data.length; i++) {
		var getDocStatusResponse = idp.service.fetch("/api/jtgk/goldwind/unipaybill/v1.0/detailStatus?DOCID=" + data[i].ID, null, false, "GET");
		if (getDocStatusResponse.status !== 200) {
			idp.error("请求服务端接口失败");
			return false;
		}
		var getDocStatusResult = JSON.parse(getDocStatusResponse.responseText);
		if (!getDocStatusResult.result) {
			idp.error(getDocStatusResult.message);
			return false;
		}
		var docStatus = getDocStatusResult.data[i];
		if (docStatus == null || docStatus == "" || docStatus != "0") {
			idp.alert('当前状态不允许编辑');
			return false;
		}
		var newUrl = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=ad2b927e-689f-d432-c8e6-bb6b642beb76&status=edit&runtime=true"
			+ "&dataid=" + data[i].ID;
		idp.utils.openurl(data[i].ID, "付款安排复核详情", newUrl);
	}
	return true;
}

/** 提交 */
function onSubmitList() {
	let data = idp.control.get('grid_main').getSelected();
	idp.confirm("确定要复核完成当前的付款计划单吗？", function () {
		idp.loading("处理中...");
		let url = "/api/jtgk/goldwind/unipaybill/v1.0/checkPass?DOCID=" + data.ID + "&DOCNO=" + data.DOCNO + "&ONLYCHECK=false";
		return idp.service.fetch(url, null, true, "GET")
			.then(function (res) {
				idp.loaded();
				if (!res.result) {
					idp.warn(res.message);
					return false;
				}else{
					idp.info("提交成功");
					idp.control.get("grid_main").loadData();
					return true;
				}
			}).fail(function () {
				idp.loaded();
				return false;
			});
		},function() {});
	return true;
}


/** 提交新 */
function onSubmitCardNew() {
	idp.confirm("确定要复核当前选中付款安排单？", function () {
		var curDatas = idp.control.get('grid_main').selected;
		if (!curDatas || curDatas.length === 0) {
			idp.warn("请至少选择一条数据进行操作。");
			return;
		}

		let IDs = [];
		for (var i = 0; i < curDatas.length; i++) {
			IDs.push(curDatas[i].ID);
		}

		let managerUrl = "/api/jtgk/goldwind/settlement/v1.0/managerImageSize";
		let managerParams = { IDs: IDs };

		idp.service.fetch(managerUrl, managerParams, true, "POST") // Async fetch
			.then(function(result) {
				if (!result.result) {
					idp.error(result.message);
					return;
				}

				let flagAlert = false;
				for (var i = 0; i < curDatas.length; i++) {
					var curData = curDatas[i];
					let BILLPAYWAY = curData.BILLPAYWAY;
					let PAYUNITID_CODE = curData.PAYUNITID_CODE;
					if (PAYUNITID_CODE != "1001" && BILLPAYWAY == 1){
						flagAlert = true;
						break; 
					}
				}
				
				const processSubmissions = function() {
					idp.loading("处理中...");
					
					// 构建批量处理请求参数
					let billInfos = [];
					for (var i = 0; i < curDatas.length; i++) {
						var curData = curDatas[i];
						billInfos.push({
							docId: curData.ID,
							docNo: curData.DOCNO,
							billPayWay: curData.BILLPAYWAY,
							payUnitId: curData.PAYUNITID
						});
					}

					let batchRequest = {
						billInfos: billInfos
					};

					// 调用新的批量处理接口
					let batchUrl = "/api/jtgk/goldwind/unipaybill/v1.0/batchCheckPass";
					idp.service.fetch(batchUrl, batchRequest, true, "POST")
						.then(function(batchResult) {
							if (!batchResult.result) {
								idp.error("批量处理失败：" + batchResult.message);
								idp.loaded();
								return;
							}

							let results = batchResult.data;
							let successfulSubmissions = 0;
							let successDocNos = [];
							let failDocNos = [];
							let refreshGrid = false;

							results.forEach(function(result) {
								if (result.success) {
									successfulSubmissions++;
									successDocNos.push(result.docNo + "-" + result.payUnitId);
									refreshGrid = true;
								} else {
									failDocNos.push(result.docNo + "-" + result.payUnitId + "(" + result.message + ")");
								}
							});

							if (results.length > 0) {
								let message = "";
								if (successfulSubmissions === results.length) {
									message = "所有 " + results.length + " 条单据处理成功。\n成功单据：" + successDocNos.join(", ");
									idp.info(message);
								} else if (successfulSubmissions > 0) {
									message = "部分单据处理完成。成功 " + successfulSubmissions + " 条，失败 " + (results.length - successfulSubmissions) + " 条。\n";
									message += "成功单据：" + successDocNos.join(", ") + "\n";
									message += "失败单据：" + failDocNos.join(", ");
									idp.warn(message);
								} else {
									message = "所有 " + results.length + " 条单据处理失败。\n失败单据：" + failDocNos.join(", ");
									idp.error(message);
								}
							}

							if (refreshGrid) {
								idp.control.get("grid_main").loadData();
							}
							
							idp.loaded();
						})
						.fail(function(errMsg) {
							idp.error("批量处理接口调用失败：" + (errMsg.message || "未知错误"));
							idp.loaded();
						});
				};

				if (flagAlert){
					idp.confirm("非常规主体开承，是否继续?", processSubmissions, function() {
					});
				} else {
					processSubmissions();
				}

			}).fail(function(errMsg) {
				idp.error(errMsg.message || "检查影像大小接口调用失败");
			});
		},function() {
			// User cancelled the initial confirmation for submitting cards
		});
	return true;
}

function submitOldOne(data){
	return new Promise(function(resolve, reject) {
		let url = "/api/jtgk/goldwind/unipaybill/v1.0/checkPass?DOCID=" + data.ID + "&DOCNO=" + data.DOCNO + "&ONLYCHECK=false";
		idp.service.fetch(url, null, true, "GET") // Async
			.then(function (res) {
				if (res.result) {
					resolve({ success: true, docNo: data.DOCNO, message: "提交成功" });
				} else {
					reject({ success: false, docNo: data.DOCNO, message: res.message || "提交失败" });
				}
			}).fail(function (err) {
				reject({ success: false, docNo: data.DOCNO, message: (err && err.message) || "请求服务端接口失败" });
			});
	});
}

function submitNewOne(curData){
	return new Promise(function(resolve, reject) {
		var djbh = curData.DOCNO;
		var dwid = curData.PAYUNITID;
		var id = curData.ID;

		var params = {
			data: {
				DJBH: djbh,
				BILLID: id,
				FORMTYPE: "JFPAYPLAN2",
				DWID: dwid
			},
			BILLID: id,
			PFPROCINTERACTINFO: null
		};

		let getURL = "/api/jtgk/goldwind/unipaybill/v1.0/checkPass?DOCID=" + id + "&DOCNO=" + djbh + "&ONLYCHECK=true";
		idp.service.fetch(getURL, null, true, "GET") // Async
			.then(function (checkResult) {
				if (!checkResult.result) {
					reject({ success: false, docNo: djbh, message: checkResult.message || "预校验失败" });
				} else {
					var url = "/api/jtgk/goldwind/unipaybill/v1.0/submit2New";
					return idp.service.fetch(url, params, true, "POST"); // Return promise for chaining
				}
			})
			.then(function (submitResult) { // This .then is for the submit2New fetch
				if (submitResult) { // Ensure submitResult is not undefined
					if (submitResult.result) {
						resolve({ success: true, docNo: djbh, message: "提交审批成功！" });
					} else {
						reject({ success: false, docNo: djbh, message: "提交审批失败！" + (submitResult.message || "") });
					}
				} else {
					reject({ success: false, docNo: djbh, message: "提交审批接口未返回有效结果" });
				}
			})
			.fail(function (err) { // Catches errors from either fetch call if not caught by a preceding .then's reject
				reject({ success: false, docNo: djbh, message: (err && err.message) || "处理过程中发生错误" });
			});
	});
}


/** 撤回新 */
function onRetractCardNew() {
	var rows = idp.control.get('grid_main').selected;
	for (var i = 0; i < rows.length; i++) {
		var row = rows[i];
		var docstatus = row.DOCSTATUS;
		if (docstatus != "1") {
			idp.alert("当前单据状态无法撤回!");
        	return false;
    	}

		var params = {};
		var data = {};
		data.BILLID = row.ID;
		data.FORMTYPE = "JFPAYPLAN2";
		data.DWID = row.PAYUNITID;
		data.DJBH = row.DOCNO;
		data.PFTASKID = row.APPROVALID;
		data.DQHJBH = "STARTNODE";
		params.BILLID = row.ID;
		params.data = data;

		params.PFPROCINTERACTINFO = {};
		var url = "/api/jtgk/goldwind/unipaybill/v1.0/retract2New";

		idp.loading("加载中...");

		let response = idp.service.fetch(url, params, false, "POST")
		if (response.status !== 200) {
			idp.error("请求服务端接口失败");
			return false;
		}
		let resInfo = JSON.parse(response.responseText);

		if (resInfo.result) {
			if (resInfo.code == 0) {
				idp.alert("撤回成功！");
				idp.control.get("grid_main").loadData();
			}
		} else {
			//弹窗提醒
			idp.alert("撤回失败！" + resInfo.message);
		}
	}

	idp.loaded();

}

/** 查看新 */
function onViewCardNew() {
	let url = "/api/jtgk/goldwind/unipaybill/v1.0/view2New";
    var curDatas = idp.control.get('grid_main').selected;
	for (var i = 0; i < curDatas.length; i++) {
		var curData = curDatas[i];

		idp.loading("打开中...");
		var params = {}
		params.BILLID = curData.ID;
		params.FORMTYPE = "JFPAYPLAN2";
		idp.service.fetch(url, params, false, "POST")
			.then(function(result) {
				idp.loaded();
				if (result) {
					if (result.result) {
						url = encodeURI(encodeURI(result.value.toString()));
						let mtitile = "查看流程";
						idp.utils.openurl(curData.ID, mtitile, url, true);
					} else {
						idp.error(result.message);
					}
				}
			}).always(function() {
				idp.loaded();
			});
	}
}

/** 退回 */
function onCancelSubmitList() {
	var rows = idp.control.get('grid_main').selected;
	idp.confirm("确定要退回当前的付款计划单吗？", function () {
		for (var i = 0; i < rows.length; i++) {
			var data = rows[i];
			var state = data.DOCSTATUS;
			if (state == '1') {
				idp.alert("当前单据状态不允许退回");
				return false;
			}
			var data = idp.control.get('grid_main').getSelected();
			var checkResponse = idp.service.fetch("/api/jtgk/goldwind/unipaybill/v1.0/checkBack?DOCID=" + data.ID + "&DOCNO=" + data.DOCNO, null, false, "GET");
			if (checkResponse.status !== 200) {
				idp.error("请求服务端接口失败");
				return false;
			}
			var checkResult = JSON.parse(checkResponse.responseText);
			if (!checkResult.result) {
				idp.error(checkResult.message);
				return false;
			}
			idp.control.get("grid_main").loadData();
		}
	},function() {});
	return true;
}

function managerImage(){
	let IDs = [];
	var data = idp.control.get('grid_main').selected;
	for (var i = 0; i < data.length; i++) {
		IDs.push(data[i].ID);
	}
	let url = "/api/jtgk/goldwind/settlement/v1.0/managerImage";
	let params = {};
	params.IDs = IDs;
	params.BILLTYPE = "JFPAYPLAN2";
	idp.service.fetch(url, params, false, "POST")
		.then(function(result) {
			if (result.result) {
				let imageId = result.data;
				var dzyxName = "付款安排附件";
				var status = "edit";
				var billtype="";
				const param = {
					BILLSTATE: 'SAVE',
					BillNM: imageId,
					BillCODE: "付款安排附件",
					BillCATEGORY: '',
					BillType: billtype,
					BillTypeID: billtype,
					OPERATION: status,
					USERCODE: idp.context.get("UserCode"),
					SourceSys: 'SYS',
					MKID: 'CM',
					DWBH: '',
					ISAPP: '0',
					TabID: idp.styleId,
					IsInvoice: '1',
					IsShowInvoice: '1'
				};
				idp.service.fetch('/api/BP/EIS/v1.0/imageapi/getyxurlmap', param)
					.then(rtnInfo => {
						if (rtnInfo.code == "ok") {
							urlstr = rtnInfo.data;
							var opts = {
								title: dzyxName,
								name: 'customWindow',
								isHidden: false,
								showMax: false,
								width: 1000,
								slide: false,
								height: 800,
								url: urlstr,
								cls: "",
								content: "", //内容
								onclose: function() {
								},
							};
							var dg = $.leeDialog.open(opts).max();
						} else {
							idp.error(rtnInfo.msg);
							return false;
						}
					}).fail(function (result) {
					idp.error(result.message);
				});

			} else {
				idp.alert(result.message);
			}
		})
		.fail(function(errMsg) {
			console.log(errMsg);
		});
}


function checkDetailInfo(){
	let url = "/apps/fastdweb/views/runtime/page/query/querypreview.html?v=17502&modid=&styleid=acca96b3-a95d-f2c7-66ac-7b766f1e6bbb"
	idp.utils.openurl("", "付款安排明细", url);
}
