/**
 * 待付池-付款安排表-卡片
 * /apps/jtgk/jfkj/extend/unipay-planbill-card.js
 */

var initInfos = {};
var addedRows = [];


idp.event.bind("loadData", function (e, data) {
	setToolbarState(e, data);
	setControlState(e, data);
	// 获取金额
	let rows = idp.control.get('grid_JTGKPAYPLANDETAIL').rows;
	for (var i=0; i<rows.length; i++) {
		initInfos[rows[i].LOGINFOID] = rows[i].AMOUNT;
	}
});



idp.event.bind("viewReady", function (e) {
	loadDetailFromDataSrc(e);

	idp.event.register('grid_JTGKPAYPLANEXCEL',"afterRefreshGridData",function(e,row,obj){
		setTimeout(function(){
			let settlementWayId = idp.uiview.modelController.getValue("TXT01"); // 例如 3
			if (settlementWayId == "9cbd6bf9-66f1-0609-b36a-e4d7c676a418") {

				$(idp.control.get("TXT02").element).closest('div.table-item').show();
				$(idp.control.get("DATE01").element).closest('div.table-item').show();

				let valueinfo = idp.uiview.modelController.getValue("TXT02");
				if (valueinfo == null || valueinfo == "") {
					valueinfo = '6';
					idp.control.get('TXT02').setValue(valueinfo)
				}
				modifyPaydate(valueinfo)
			}
		}, 1000);
	});

	idp.event.register("grid_JTGKPAYPLANDETAIL","afterEndEdit",function(e,opts){ 
		// 列数据 
		var column=opts.column;
		if (column.colid == "AMOUNT") {
			// 检查金额是否大于申请金额
			let amount = opts.value;
			let applyAmount = opts.record.REQUESTAMOUNT;
			if (amount > applyAmount) {
				idp.error("本次付款金额不能大于申请金额");
				return false;
			}
		}
		return true;
	});

	idp.event.register('grid_JTGKPAYPLANEXCEL',"afterImportGrid",function(e,row,obj){
		// 根据付款方式，设置表头的付款方式
		let rows = idp.control.get('grid_JTGKPAYPLANEXCEL').rows;
		let payMethod = rows[0].TXT02;
		// 设置view的值
		idp.control.get("TXT01").setValue(payMethod, payMethod);
		// 设置model的值
		idp.uiview.modelController.setValue("TXT01", payMethod);

		var getHelpResponse = idp.service.fetch('/api/jtgk/goldwind/unipaybill/v1.0/getHelpInfos?NAME=' + payMethod, null, false, 'GET');
		if (getHelpResponse.status !== 200) {
			idp.error("请求服务端接口失败");
			return false;
		}
		var getHelpResult = JSON.parse(getHelpResponse.responseText);
		if (!getHelpResult.result) {
			idp.error(getHelpResult.message);
			return false;
		}
		console.log("已从数据库中相关帮助信息");
		let mainObj = idp.uiview.modelController.getMainRowObj()
		mainObj.TXT01 = getHelpResult.data.TXT01;
		mainObj.TXT01_NAME = getHelpResult.data.TXT01_NAME;
		mainObj.TXT01_TXT01 = getHelpResult.data.TXT01_TXT01;
		mainObj.SETTLEMENTWAYID = getHelpResult.data.SETTLEMENTWAYID;
		mainObj.SETTLEMENTWAYID_CODE = getHelpResult.data.SETTLEMENTWAYID_CODE;
		mainObj.SETTLEMENTWAYID_NAME$LANGUAGE$ = getHelpResult.data.SETTLEMENTWAYID_NAME$LANGUAGE$;
		mainObj.BILLPAYWAY = getHelpResult.data.BILLPAYWAY;
		mainObj.IFACCEPTANCEBILL = getHelpResult.data.IFACCEPTANCEBILL;

		// 设置view的值
		idp.control.get("SETTLEMENTWAYID").setValue(getHelpResult.data.SETTLEMENTWAYID);
		idp.control.get("SETTLEMENTWAYID_NAME").setValue(getHelpResult.data.SETTLEMENTWAYID_NAME$LANGUAGE$, getHelpResult.data.SETTLEMENTWAYID_NAME$LANGUAGE$);
		idp.control.get("SETTLEMENTWAYID_CODE").setValue(getHelpResult.data.SETTLEMENTWAYID_CODE);
		idp.control.get("BILLPAYWAY").setValue(getHelpResult.data.BILLPAYWAY);
		idp.control.get("IFACCEPTANCEBILL").setValue(getHelpResult.data.IFACCEPTANCEBILL);
		// 设置model的值
		idp.uiview.modelController.setValue("SETTLEMENTWAYID", getHelpResult.data.SETTLEMENTWAYID);
		idp.uiview.modelController.setValue("SETTLEMENTWAYID_NAME", getHelpResult.data.SETTLEMENTWAYID_NAME$LANGUAGE$);
		idp.uiview.modelController.setValue("SETTLEMENTWAYID_CODE", getHelpResult.data.SETTLEMENTWAYID_CODE);
		idp.uiview.modelController.setValue("BILLPAYWAY", getHelpResult.data.BILLPAYWAY);
		idp.uiview.modelController.setValue("IFACCEPTANCEBILL", getHelpResult.data.IFACCEPTANCEBILL);

		return true;
	});

	// 隐藏内容
	$(idp.control.get("SETTLEMENTWAYID_CODE").element).closest('div.table-item').hide();
});

function modifyPaydate(value){
	// 金风云信结算方式的，要根据期望付款日期，+兑付期限（月数），自动计算出承诺付款日
	// 比如期望付款日期是今天5月7日，兑付周期3个月，承诺付款日就是8月28（承诺付款日根据期望付款日期+兑付周期计算出承诺付款月份，然后取承诺付款月份的到数第2个工作日）
	let expectPayDate = idp.uiview.modelController.getValue("EXPPAYDATE"); // 例如：2025-04-21 00:00:00
	let settlementWayId = idp.uiview.modelController.getValue("TXT01"); // 例如 3
	if (settlementWayId == "9cbd6bf9-66f1-0609-b36a-e4d7c676a418") {
		let payPeriod = parseInt(value, 10); // 兑付期限（月数）
		if (isNaN(payPeriod)) {
			// 如果兑付期限不是一个有效的数字，可以给出提示或者不进行计算
			console.warn("无效的兑付期限:", value);
			return true; // 或者根据实际需求决定是否阻止编辑
		}
		let targetMonthDate = new Date(expectPayDate);
		targetMonthDate.setMonth(targetMonthDate.getMonth() + payPeriod);

		let targetYear = targetMonthDate.getFullYear();
		let targetJsMonth = targetMonthDate.getMonth(); // JavaScript 月份 (0-11)

		let lastDayOfTargetMonth = new Date(targetYear, targetJsMonth + 1, 0);
		let workdayCount = 0;
		let promisePayDateObj = null;

		for (let day = lastDayOfTargetMonth.getDate(); day >= 1; day--) {
			const tempDate = new Date(targetYear, targetJsMonth, day);
			const dayOfWeek = tempDate.getDay(); // 0 for Sunday, 6 for Saturday
			if (dayOfWeek !== 0 && dayOfWeek !== 6) {
				workdayCount++;
				if (workdayCount === 2) {
					promisePayDateObj = tempDate;
					break;
				}
			}
		}

		if (promisePayDateObj) {
			let year = promisePayDateObj.getFullYear();
			let month = promisePayDateObj.getMonth() + 1; // 转换为 1-12 月
			let day = promisePayDateObj.getDate();

			// 格式化为 yyyy-MM-dd 00:00:00
			let promisePayDateStr = year + "-" +
				(month < 10 ? '0' + month : month) + "-" +
				(day < 10 ? '0' + day : day);
			idp.control.get('DATE01').setValue(promisePayDateStr)

			// 支付明细表
			let rows = idp.control.get('grid_JTGKPAYPLANDETAIL').rows;
			for (var i=0; i<rows.length; i++) {
				idp.uiview.setFieldValueWithTrigger('JTGKPAYPLANDETAIL', 'TXT03', i, value, 'grid_JTGKPAYPLANDETAIL');
				idp.uiview.setFieldValueWithTrigger('JTGKPAYPLANDETAIL', 'DATE01', i, promisePayDateStr, 'grid_JTGKPAYPLANDETAIL');
			}
		} else {
			// 如果没有找到承诺付款日（例如月份太短或全是周末），可以选择如何处理
			// 这里暂时保持原样或提示，具体行为根据业务需求
			console.warn("未能计算出承诺付款日 for:", expectPayDate, "with period:", payPeriod);
			// 可以选择将 opts.record.TXT03 设为某个默认值或清空
			// opts.record.TXT03 = ""; // 清空示例
		}
	}
}

idp.event.bind("domReady", function (e) {
	// 主表值变化
	idp.event.register("TXT01","selected",function(e,value,name,obj){
		let valueinfo = idp.uiview.modelController.getValue("TXT02");
		if (valueinfo == null || valueinfo == "") {
			valueinfo = '6';
			idp.control.get('TXT02').setValue(valueinfo)
		}
		modifyPaydate(valueinfo)
	});
	idp.event.register("TXT02","modify",function(e,value){
		modifyPaydate(value)
    });
	idp.event.register("DATE01","modify",function(e,value){
		let result = idp.control.get('DATE01').getValue();
		let rows = idp.control.get('grid_JTGKPAYPLANDETAIL').rows;
		for (var i=0; i<rows.length; i++) {
			idp.uiview.setFieldValueWithTrigger('JTGKPAYPLANDETAIL', 'DATE01', i, result, 'grid_JTGKPAYPLANDETAIL');
		}
    });

	// 付款账号过滤 - 根据付款单位
    idp.event.register("grid_JTGKPAYPLANDETAIL", "beforeHelpFilter", function (e,g,field,index,row) {
		
		let data = idp.control.get('grid_JTGKPAYPLANDETAIL').rows[row]

		if(field=="PAYACCOUNTNO"){
			 // 付款单位
			 return [{
				"Left": "",
				"Field": "OPENACCOUNTUNITID",
				"Operate": "=",
				"IsExpress": false,
				"Value": data.PAYUNITID,
				"Right": "",
				"Logic": ""
			}];
		}

		return [];
    })

	// 隐藏内容
	$(idp.control.get("SETTLEMENTWAYID_CODE").element).closest('div.table-item').hide();
});

idp.event.bind("beforeSave", beforeSaveCard);

// 工具栏按钮
function setToolbarState(e, data) {
    var state = idp.uiview.modelController.getValue("DOCSTATUS");
    if (state == '2') {
		// 已审批时不允许修改
        idp.control.get("toolbar1").setDisabled("baritem_modify");
        idp.control.get("toolbar1").setDisabled("baritem_submit");
        idp.control.get("toolbar1").setDisabled("baritem_cancelSubmit");
        idp.control.get("toolbar1").setDisabled("baritem_delete");

		idp.control.get("toolbar1").setDisabled("baritem_submit_new");
        idp.control.get("toolbar1").setDisabled("baritem_retract_new");
    }
    if (state == '1') {
		// 审批中不允许修改
        idp.control.get("toolbar1").setDisabled("baritem_modify");
        idp.control.get("toolbar1").setDisabled("baritem_submit");
        idp.control.get("toolbar1").setDisabled("baritem_delete");
        idp.control.get("toolbar1").setEnabled("baritem_cancelSubmit");

		idp.control.get("toolbar1").setDisabled("baritem_submit_new");
        idp.control.get("toolbar1").setEnabled("baritem_retract_new");
    }
    if (state == '0' || state == '3') {
		let status = idp.utils.getQuery("status");
		if (status == "add") {
			idp.control.get("toolbar1").setDisabled("baritem_modify");
			idp.control.get("toolbar1").setEnabled("baritem_submit");
			idp.control.get("toolbar1").setDisabled("baritem_cancelSubmit");
			idp.control.get("toolbar1").setDisabled("baritem_delete");

			idp.control.get("toolbar1").setEnabled("baritem_submit_new");
			idp.control.get("toolbar1").setDisabled("baritem_retract_new");
		}else if (status == "edit") {
			idp.control.get("toolbar1").setDisabled("baritem_modify");
			idp.control.get("toolbar1").setEnabled("baritem_submit");
			idp.control.get("toolbar1").setDisabled("baritem_cancelSubmit");
			idp.control.get("toolbar1").setDisabled("baritem_delete");

			idp.control.get("toolbar1").setEnabled("baritem_submit_new");
			idp.control.get("toolbar1").setDisabled("baritem_retract_new");
		}
    }
	// 允许查看流程
	idp.control.get("toolbar1").setEnabled("baritem_viewProcess");
	idp.control.get("toolbar1").setEnabled("baritem_view_new");
	if (idp.utils.getQuery("workItemId") !== '') {
		// 单据审批时按钮隐藏
		idp.control.get("toolbar1").toggleBtns(["baritem_close","baritem_add","baritem_cancel","baritem_modify","baritem_save","baritem_submit","baritem_cancelSubmit","baritem_delete","baritem_submit_new","baritem_retract_new"], false);
	} else {
		idp.control.get("toolbar1").toggleBtns(["baritem_close","baritem_add","baritem_cancel","baritem_modify","baritem_save","baritem_submit","baritem_cancelSubmit","baritem_delete","baritem_submit_new","baritem_retract_new"], true);
	}
}

// 表单控件
function setControlState(e, data) {
	var status = idp.utils.getQuery("status");
	var dataSrc = null;
	var ifAcceptanceBill = null;
	if (data !== null) {
		if (status == "add") {
			// 新增时默认值
			dataSrc = data.DATASRC;
		} else if (data[0] !== null && data[0].data !== null && data[0].data[0] !== null) {
			// 查看、编辑
			dataSrc = data[0].data[0].DATASRC;
			ifAcceptanceBill = data[0].data[0].IFACCEPTANCEBILL;
		}
	}
	var pamDataSrc = idp.utils.getQuery("DATASRC");
	if (typeof(pamDataSrc) != "undefined" && pamDataSrc != null && pamDataSrc != "") {
		dataSrc = pamDataSrc;
	}
	if (dataSrc == "1") {
		// 待付池发起：不显示Excel导入
		document.getElementById("layout_701492").style.display = "none";
		document.getElementById("layout_288462_toolbar_0").style.display = "block";
		document.getElementById("grid_JTGKPAYPLANDETAIL_toolbar").style.display = "none";
	} else if (dataSrc == "2") {
		// 手工制单：不显示Excel导入
		document.getElementById("layout_701492").style.display = "none";
		document.getElementById("layout_288462_toolbar_0").style.display = "block";
		
		// 显示新增删除按钮
		document.getElementById("grid_JTGKPAYPLANDETAIL_toolbar").style.display = "block";
		document.querySelector('a[toolbarid="baritem_addRow"]').style.display = "block";
		document.querySelector('a[toolbarid="baritem_deleteRow"]').style.display = "block";
	} else {
		// 手工制单：允许导入excel
		document.getElementById("layout_701492").style.display = "block";
		document.getElementById("layout_288462_toolbar_0").style.display = "none";
		document.getElementById("grid_JTGKPAYPLANDETAIL_toolbar").style.display = "none";
	}
}

// 支付明细数据初始化
function loadDetailFromDataSrc(e) {
	var status = idp.utils.getQuery("status");
	var dataSrc = idp.utils.getQuery("DATASRC");
	if (dataSrc == "1") {
		if (status == "add") {
			// 从待付池发起时，自动带入选择的付款申请
			var refIds = idp.utils.getQuery("LOGINFOID");
			if (typeof(refIds) == "undefined" || refIds == null || refIds == "") {
				return;
			}
			var loadUrl = "/api/jtgk/goldwind/settlement/v1.0/getUnpay?ID=" + refIds;
			console.log(loadUrl);
			idp.service.fetch(loadUrl, null, false, "GET").done(function(result){
				console.log(result);
				if (result.result) {
					var details = result.data;
					var gridBizId = 'grid_JTGKPAYPLANDETAIL';

					let valueinfo = idp.uiview.modelController.getValue("TXT02");
					let expectPayDate = idp.uiview.modelController.getValue("DATE01");
					if (valueinfo != null && valueinfo != "") {
						for (var i=0; i<details.length; i++) {
							details[i].TXT03 = valueinfo;
						}
					}
					if (expectPayDate != null && expectPayDate != "") {
						for (var i=0; i<details.length; i++) {
							details[i].DATE01 = expectPayDate;
						}
					}

					for (var i = 0; i < details.length; i++) {
						var rowNum = idp.control.get(gridBizId).rows.length;
						idp.uiview.addRow(gridBizId);
						idp.control.get(gridBizId).updateRow(rowNum, {
							"LOGINFOID": details[i].ID, // 关联日志表ID
							"PAYUNITID": details[i].PAYUNITID, // 付款单位ID
							"PAYUNITID_CODE": details[i].PAYUNITCODE, // 付款单位编码
							"PAYUNITNAME": details[i].PAYUNITID_NAME, // 付款单位名称
							"REQUESTDEPTID": details[i].REQUESTDEPTID, // 申请部门ID
							"REQUESTDEPTNAME": details[i].REQUESTDEPTID_NAME, // 申请部门名称
							"PAYACCOUNTID": details[i].PAYACCOUNTID, // 付款账户ID
							"PAYACCOUNTNO": details[i].PAYACCOUNTID_NO, // 付款账号
							"PAYACCOUNTNAME": details[i].PAYACCOUNTID_NAME, // 付款账户名
							"EXPECTPAYDATE": details[i].EXPECTPAYDATE, // 期望付款日期
							"CURRENCYID": details[i].CURRENCYID, // 币种ID
							"CURRENCYID_CODE": details[i].CURRENCYCODE, // 币种
							"REQUESTAMOUNT": details[i].REQUESTAMOUNT, // 申请金额
							"PAIDAMOUNT": details[i].PAIDAMOUNT, // 已付款金额
							"PAYINGAMOUNT": details[i].PAYINGAMOUNT, // 付款中金额
							"UNPAYAMOUNT": details[i].UNPLANAMOUNT, // 剩余待安排金额
							"AMOUNT": details[i].UNPLANAMOUNT, // 本次付款金额
							"TRANSCURRENCYID": details[i].TRANSCURRENCYID, // 交易币种ID
							"TRANSCURRENCYID_CODE": details[i].TRANSCURRENCYCODE,
							"TRANSAMOUNT": details[i].TRANSAMOUNT, // 交易币种金额
							"TRANSEXCHANGERATE": details[i].TRANSEXCHANGERATE, // 交易币种汇率
							"RECEIVINGUNITID": details[i].RECEIVINGUNITID, // 往来单位ID
							"RECEIVINGUNITCODE": details[i].RECEIVINGUNITCODE, // 往来单位编号
							"RECEIVINGUNITNAME": details[i].RECEIVINGUNITNAME, // 往来单位名称
							"RECEIVINGBANKACCOUNTID": details[i].RECEIVINGBANKACCOUNTID, // 往来单位银行账户ID
							"RECEIVINGBANKACCOUNTNO": details[i].RECEIVINGBANKACCOUNTID_NO, // 收款账号
							"RECEIVINGBANKACCOUNTNAME": details[i].RECEIVINGBANKACCOUNTID_NAME, // 收款账户名
							"RECEIVINGBANKID": details[i].RECEIVINGBANKID, // 收款银行ID
							"RECEIVINGBANKNO": details[i].RECEIVINGBANKID_CODE, // 收款银行联行号
							"RECEIVINGBANKNAME": details[i].RECEIVINGBANKID_NAME, // 收款银行名称
							"RECIPROCALCOUNTRY": null, // 对方国家
							"RECIPROCALPROVINCE": null, // 对方省份
							"RECIPROCALCITY": null, // 对方城市
							"PRIVATEFLAG": null, // 对方性质
							"ISPRIVATEACCOUNT": null, // 是否对私账户
							"SUMMARY": details[i].SUMMARY, // 摘要
							"DESCRIPTION": details[i].DESCRIPTION, // 详细说明
							"EXTPURCHASECODE": details[i].EXTPURCHASECODE,
							"EXTPROJECTCODE": details[i].EXTPROJECTCODE,
							"EXTCOSTCENTER": details[i].EXTCOSTCENTER,
							"EXTINNERORDER": details[i].EXTINNERORDER,
							"EXTPROFITCENTER": details[i].EXTPROFITCENTER,
							"SETTLEMENTWAYID": details[i].SETTLEMENTWAYID, // 结算方式ID
							"SETTLEMENTWAYID_NAME$LANGUAGE$": details[i].SETTLEMENTWAYID_NAME,
							"FUNDNATUREID": details[i].FUNDNATUREID, // 款项性质ID
							"FUNDNATUREID_NAME$LANGUAGE$": details[i].FUNDNATUREID_NAME,
							"CASHFLOWITEMID": null, // 现金流量项目ID
							"SRCBIZSYS": details[i].SRCBIZSYS, // 来源系统标识
							"SRCDOCTYPE": details[i].SRCDOCTYPE, // 来源单据类型
							"SRCDOCID": details[i].SRCDOCID, // 来源单据ID
							"SRCDOCNO": details[i].SRCDOCNO, // 来源单据编号
							"TXT01": details[i].TXT01,
							"TXT02": details[i].TXT02,
							"TXT03": details[i].TXT03,
							"TXT04": details[i].TXT04,
							"TXT05": details[i].TXT05,
							"TXT06": details[i].TXT06,
							"TXT07": details[i].TXT07,
							"TXT08": details[i].TXT08,
							"TXT09": details[i].TXT09,
							"TXT10": details[i].TXT10,
							"TXT11": details[i].TXT11,
							"TXT12": details[i].TXT12,
							"TXT13": details[i].TXT13,
							"TXT14": details[i].TXT14,
							"TXT15": details[i].TXT15,
							"TXT16": details[i].TXT16,
							"TXT17": details[i].TXT17,
							"TXT18": details[i].TXT18,
							"TXT19": details[i].TXT19,
							"TXT20": details[i].TXT20,
							"AMT01": details[i].AMT01,
							"AMT02": details[i].AMT02,
							"AMT03": details[i].AMT03,
							"AMT04": details[i].AMT04,
							"AMT05": details[i].AMT05,
							"AMT06": details[i].AMT06,
							"AMT07": details[i].AMT07,
							"AMT08": details[i].AMT08,
							"AMT09": details[i].AMT09,
							"AMT10": details[i].AMT10,
							"DATE01": details[i].DATE01,
							"DATE02": details[i].DATE02,
							"DATE03": details[i].DATE03,
							"DATE04": details[i].DATE04,
							"DATE05": details[i].DATE05,
							"DATE06": details[i].DATE06,
							"DATE07": details[i].DATE07,
							"DATE08": details[i].DATE08,
							"DATE09": details[i].DATE09,
							"DATE10": details[i].DATE10,
							"DOCSTATUS": 0, // 办理状态(0制单,1待复核,2待办理,3已完成,-1退回)
							"TIMESTAMPS_LASTCHANGEDBY": idp.context.get("UserId"),
							"TIMESTAMPS_LASTCHANGEDON": idp.context.get("LoginDate"),
							"TIMESTAMPS_CREATEDBY": idp.context.get("UserId"),
							"TIMESTAMPS_CREATEDON": idp.context.get("LoginDate")
						});
					}
				} else {
					idp.error(result.message);
				}
			});
		}
	}
}

/** 业务明细：从Excel文件导入业务明细 */
function importExcel() {
	var details = idp.control.get("grid_JTGKPAYPLANEXCEL").rows;
	if (details == null || details.length == 0) {
		idp.uiview.import("JTGKPAYPLANEXCEL");
		return false;
	}
	idp.confirm("是否清空已有数据并重新导入？", function () {
		$("#grid_JTGKPAYPLANEXCEL").leeUI().loadData({
			Rows: []
		});
		$("#grid_JTGKPAYPLANDETAIL").leeUI().loadData({
			Rows: []
		});
		idp.uiview.import("JTGKPAYPLANEXCEL");
	},function() {});
}

/** 业务明细：导入模板文件下载 */
function downloadTemplete() {
	return idp.downLoadExcel("44bb6838-9b25-8e1a-c885-b5c7b66f83e4", "付款安排计划", "JTGKPAYPLANEXCEL");
};

/** 支付明细：增加行 */
function addRow() {
	// return idp.uiview.addRow('grid_JTGKPAYPLANDETAIL');

	// 用户选择待付池单据
	$.leeDialog.open({
		title: "待付池单据选择",
		name: 'UnipayLogSelect',
		width: Math.max(window.innerWidth * 0.6, 900),
		height: Math.max(window.innerHeight * 0.5, 500),
		url: `/apps/fastdweb/views/runtime/page/query/querypreview.html?${$.param({
			'styleid': 'ee9d8ca8-a1ca-0330-1cf9-6b4fa212cb6c',
			'isFromPlanBill': true
		})}`,
		buttons: [{
			id: "dialog_lookup_cancel",
			text: "取消",
			cls: 'lee-dialog-btn-cancel ',
			onclick: function (item, dialog) {
				dialog.close()
			}
		}, {
			id: "dialog_lookup_register",
			//确认
			text: "确认",
			cls: 'lee-btn-primary lee-dialog-btn-ok',
			onclick: function (item, dialog) {
				// 获取选择的内容
				let grid = dialog.jiframe[0].contentWindow.idp.control.get('grid_main');
				let rows = grid.selected;

				if (rows.length === 0) {
					idp.error("请先选择要操作的付款申请！");
					return;
				}

				var refIds = "";
				var billPayWay = rows[0].BILLPAYWAY;
				for (var i=0; i<rows.length; i++) {
					if (rows[i].SRCBIZSYS !== "SFS-PAY") {
						idp.error("第" + (i+1) + "行来源系统不允许进行付款安排");
						return false;
					}
					if (rows[i].ISDIRECTPAY !== "0") {
						idp.error("第" + (i+1) + "行付款申请不允许进行付款安排");
						return false;
					}
					if (rows[i].BILLPAYWAY !== billPayWay) {
						idp.error("第" + (i+1) + "行付款申请票据支付方式不一致");
						return false;
					}
					if (rows[i].DOCSTATUS !== 1) {
						idp.error("第" + (i+1) + "行付款申请当前状态不能付款安排");
						return false;
					}
					if (rows[i].UNPAYAMOUNT <= 0) {
						idp.error("第" + (i+1) + "行剩余待付款金额为0");
						return false;
					}
					if (i > 0) {
						refIds = refIds + ",";
					}
					if (addedRows.indexOf(rows[i].ID) > -1) {
						idp.info("第" + (i+1) + "行付款申请已添加过, 跳过");
						continue;
					}
					addedRows.push(rows[i].ID);
					refIds = refIds + rows[i].ID;
				}

				var loadUrl = "/api/jtgk/goldwind/settlement/v1.0/getUnpay?ID=" + refIds;
				console.log(loadUrl);
				idp.service.fetch(loadUrl, null, false, "GET").done(function(result){
					console.log(result);
					if (result.result) {
						var details = result.data;
						var gridBizId = 'grid_JTGKPAYPLANDETAIL';

						let valueinfo = idp.uiview.modelController.getValue("TXT02");
						let expectPayDate = idp.uiview.modelController.getValue("DATE01");
						if (valueinfo != null && valueinfo != "") {
							for (var i=0; i<details.length; i++) {
								details[i].TXT03 = valueinfo;
							}
						}
						if (expectPayDate != null && expectPayDate != "") {
							for (var i=0; i<details.length; i++) {
								details[i].DATE01 = expectPayDate;
							}
						}

						for (var i = 0; i < details.length; i++) {
							var rowNum = idp.control.get(gridBizId).rows.length;
							idp.uiview.addRow(gridBizId);
							idp.control.get(gridBizId).updateRow(rowNum, {
								"LOGINFOID": details[i].ID, // 关联日志表ID
								"PAYUNITID": details[i].PAYUNITID, // 付款单位ID
								"PAYUNITID_CODE": details[i].PAYUNITCODE, // 付款单位编码
								"PAYUNITNAME": details[i].PAYUNITID_NAME, // 付款单位名称
								"REQUESTDEPTID": details[i].REQUESTDEPTID, // 申请部门ID
								"REQUESTDEPTNAME": details[i].REQUESTDEPTID_NAME, // 申请部门名称
								"PAYACCOUNTID": details[i].PAYACCOUNTID, // 付款账户ID
								"PAYACCOUNTNO": details[i].PAYACCOUNTID_NO, // 付款账号
								"PAYACCOUNTNAME": details[i].PAYACCOUNTID_NAME, // 付款账户名
								"EXPECTPAYDATE": details[i].EXPECTPAYDATE, // 期望付款日期
								"CURRENCYID": details[i].CURRENCYID, // 币种ID
								"CURRENCYID_CODE": details[i].CURRENCYCODE, // 币种
								"REQUESTAMOUNT": details[i].REQUESTAMOUNT, // 申请金额
								"PAIDAMOUNT": details[i].PAIDAMOUNT, // 已付款金额
								"PAYINGAMOUNT": details[i].PAYINGAMOUNT, // 付款中金额
								"UNPAYAMOUNT": details[i].UNPLANAMOUNT, // 剩余待安排金额
								"AMOUNT": details[i].UNPLANAMOUNT, // 本次付款金额
								"TRANSCURRENCYID": details[i].TRANSCURRENCYID, // 交易币种ID
								"TRANSCURRENCYID_CODE": details[i].TRANSCURRENCYCODE,
								"TRANSAMOUNT": details[i].TRANSAMOUNT, // 交易币种金额
								"TRANSEXCHANGERATE": details[i].TRANSEXCHANGERATE, // 交易币种汇率
								"RECEIVINGUNITID": details[i].RECEIVINGUNITID, // 往来单位ID
								"RECEIVINGUNITCODE": details[i].RECEIVINGUNITCODE, // 往来单位编号
								"RECEIVINGUNITNAME": details[i].RECEIVINGUNITNAME, // 往来单位名称
								"RECEIVINGBANKACCOUNTID": details[i].RECEIVINGBANKACCOUNTID, // 往来单位银行账户ID
								"RECEIVINGBANKACCOUNTNO": details[i].RECEIVINGBANKACCOUNTID_NO, // 收款账号
								"RECEIVINGBANKACCOUNTNAME": details[i].RECEIVINGBANKACCOUNTID_NAME, // 收款账户名
								"RECEIVINGBANKID": details[i].RECEIVINGBANKID, // 收款银行ID
								"RECEIVINGBANKNO": details[i].RECEIVINGBANKID_CODE, // 收款银行联行号
								"RECEIVINGBANKNAME": details[i].RECEIVINGBANKID_NAME, // 收款银行名称
								"RECIPROCALCOUNTRY": null, // 对方国家
								"RECIPROCALPROVINCE": null, // 对方省份
								"RECIPROCALCITY": null, // 对方城市
								"PRIVATEFLAG": null, // 对方性质
								"ISPRIVATEACCOUNT": null, // 是否对私账户
								"SUMMARY": details[i].SUMMARY, // 摘要
								"DESCRIPTION": details[i].DESCRIPTION, // 详细说明
								"EXTPURCHASECODE": details[i].EXTPURCHASECODE,
								"EXTPROJECTCODE": details[i].EXTPROJECTCODE,
								"EXTCOSTCENTER": details[i].EXTCOSTCENTER,
								"EXTINNERORDER": details[i].EXTINNERORDER,
								"EXTPROFITCENTER": details[i].EXTPROFITCENTER,
								"SETTLEMENTWAYID": details[i].SETTLEMENTWAYID, // 结算方式ID
								"SETTLEMENTWAYID_NAME$LANGUAGE$": details[i].SETTLEMENTWAYID_NAME,
								"FUNDNATUREID": details[i].FUNDNATUREID, // 款项性质ID
								"FUNDNATUREID_NAME$LANGUAGE$": details[i].FUNDNATUREID_NAME,
								"CASHFLOWITEMID": null, // 现金流量项目ID
								"SRCBIZSYS": details[i].SRCBIZSYS, // 来源系统标识
								"SRCDOCTYPE": details[i].SRCDOCTYPE, // 来源单据类型
								"SRCDOCID": details[i].SRCDOCID, // 来源单据ID
								"SRCDOCNO": details[i].SRCDOCNO, // 来源单据编号
								"TXT01": details[i].TXT01,
								"TXT02": details[i].TXT02,
								"TXT03": details[i].TXT03,
								"TXT04": details[i].TXT04,
								"TXT05": details[i].TXT05,
								"TXT06": details[i].TXT06,
								"TXT07": details[i].TXT07,
								"TXT08": details[i].TXT08,
								"TXT09": details[i].TXT09,
								"TXT10": details[i].TXT10,
								"TXT11": details[i].TXT11,
								"TXT12": details[i].TXT12,
								"TXT13": details[i].TXT13,
								"TXT14": details[i].TXT14,
								"TXT15": details[i].TXT15,
								"TXT16": details[i].TXT16,
								"TXT17": details[i].TXT17,
								"TXT18": details[i].TXT18,
								"TXT19": details[i].TXT19,
								"TXT20": details[i].TXT20,
								"AMT01": details[i].AMT01,
								"AMT02": details[i].AMT02,
								"AMT03": details[i].AMT03,
								"AMT04": details[i].AMT04,
								"AMT05": details[i].AMT05,
								"AMT06": details[i].AMT06,
								"AMT07": details[i].AMT07,
								"AMT08": details[i].AMT08,
								"AMT09": details[i].AMT09,
								"AMT10": details[i].AMT10,
								"DATE01": details[i].DATE01,
								"DATE02": details[i].DATE02,
								"DATE03": details[i].DATE03,
								"DATE04": details[i].DATE04,
								"DATE05": details[i].DATE05,
								"DATE06": details[i].DATE06,
								"DATE07": details[i].DATE07,
								"DATE08": details[i].DATE08,
								"DATE09": details[i].DATE09,
								"DATE10": details[i].DATE10,
								"DOCSTATUS": 0, // 办理状态(0制单,1待复核,2待办理,3已完成,-1退回)
								"TIMESTAMPS_LASTCHANGEDBY": idp.context.get("UserId"),
								"TIMESTAMPS_LASTCHANGEDON": idp.context.get("LoginDate"),
								"TIMESTAMPS_CREATEDBY": idp.context.get("UserId"),
								"TIMESTAMPS_CREATEDON": idp.context.get("LoginDate")
							});
						}
					} else {
						idp.error(result.message);
					}
				});
				
				dialog.close();
			}
		},
		]
	});
}

function deleteRow() {
	let grid = idp.control.get('grid_JTGKPAYPLANDETAIL');
	let rows = grid.selected;
	if (rows.length === 0) {
		idp.error("请先选择要删除的行！");
		return;	
	}

	for (var i=0; i<rows.length; i++) {
		if (addedRows.indexOf(rows[i].LOGINFOID) > -1) {
			addedRows.splice(addedRows.indexOf(rows[i].LOGINFOID), 1);
		}
	}
	
	idp.confirm("确定要删除选中的行吗？", function () {
		idp.uiview.deleteRow('grid_JTGKPAYPLANDETAIL');
	}, function() {});
}

/** 编辑 */
function onEditCard() {
	var data = idp.uiview.modelController.getMainRowObj();
	var getDocStatusResponse = idp.service.fetch("/api/jtgk/goldwind/unipaybill/v1.0/docStatus?ID=" + data.ID, null, false, "GET");
	if (getDocStatusResponse.status !== 200) {
		idp.error("请求服务端接口失败");
		return false;
	}
	var getDocStatusResult = JSON.parse(getDocStatusResponse.responseText);
	if (!getDocStatusResult.result) {
		idp.error(getDocStatusResult.message);
		return false;
	}
    var docStatus = getDocStatusResult.data;
	if (docStatus == null || docStatus == "" || (docStatus != "0" && docStatus != "3")) {
		idp.alert('当前状态不允许修改');
		idp.uiview.reloadData();
		return false;
	}
	return idp.uiview.edit();
}

function beforeSaveCard(e, data) {
	var status = idp.utils.getQuery("status");
	if (status === "add") {
		// 待付池发起
		var dataSrc = idp.utils.getQuery("DATASRC");
		if (typeof(dataSrc) != "undefined" && dataSrc != null && dataSrc != "") {
			data[0].data[0].DATASRC = dataSrc;
		}
	}
	// 汇总本次付款金额
	var totalAmount = 0.0;
	var details = data[1].data;
	if (details != null && details.length > 0) {
		data[0].data[0].SRCBIZSYS = details[0].SRCBIZSYS;
		for (var i=0; i<details.length; i++) {
			totalAmount += parseFloat(details[i].AMOUNT);
		}
	}
	data[0].data[0].TOTALAMOUNT = totalAmount;
	return data;
}

/** 保存 */
function onSaveCard() {
	var data = idp.uiview.modelController.getMainRowObj();
	if (data.TXT01 == null || data.TXT01 == "") {
		idp.error("SFS支付方式不能为空");
		return false;
	}
	if (data.SETTLEMENTWAYID == null || data.SETTLEMENTWAYID == "") {
		idp.error("SFS支付方式未映射司库结算方式");
		return false;
	}
	var details = idp.control.get("grid_JTGKPAYPLANDETAIL").rows;
	if (details != null && details.length > 0) {
		const defaultAccounts = new Map();
		for (var i=0; i<details.length; i++) {
			let settlementWayId = details[i].SETTLEMENTWAYID;
			if (settlementWayId == "9cbd6bf9-66f1-0609-b36a-e4d7c676a418") {
				let valueinfo = details[i].TXT03;
				let expectPayDate = details[i].DATE01;

				if (valueinfo == null || valueinfo == "") {
					idp.error("云信兑付期限（月）不能为空");
					return false;
				}
				if (expectPayDate == null || expectPayDate == "") {
					idp.error("云信承诺付款日不能为空");
					return false;
				}
			}

			details[i].SRCPAYMETHODCODE = data.TXT01_NAME;
			details[i].SRCPAYMETHODNAME = data.TXT01_TXT01;
			details[i].SETTLEMENTWAYID = data.SETTLEMENTWAYID;
			details[i].BILLPAYWAY = data.BILLPAYWAY;
			if (details[i].PAYACCOUNTID === null || details[i].PAYACCOUNTID === "") {
				var key = details[i].SRCBIZSYS + "-" + details[i].PAYUNITID_CODE + "-" + data.TXT01_TXT01 + "-" + details[i].TXT02;
				if (defaultAccounts.has(key)) {
					var account = defaultAccounts.get(key);
					console.log("已从缓存中获取到默认付款账户");
					details[i].PAYACCOUNTID = account.PAYACCOUNTID;
					details[i].PAYACCOUNTNO = account.PAYACCOUNTNO;
					details[i].PAYACCOUNTNAME = account.PAYACCOUNTNAME;
				} else {
					// 获取默认付款账户
					var params = {
						"SRCBIZSYS": details[i].SRCBIZSYS,
						"SRCDOCNO": details[i].SRCDOCNO,
						"PAYUNITCODE": details[i].PAYUNITID_CODE,
						"EXTPURCHASECODE": details[i].EXTPURCHASECODE,
						"SRCPAYMETHOD": data.TXT01_TXT01
					};
					var getAccountResponse = idp.service.fetch('/api/jtgk/goldwind/unipaybill/v1.0/defaultPayAccount', params, false, 'POST');
					if (getAccountResponse.status !== 200) {
						idp.error("请求服务端接口失败");
						return false;
					}
					var getAccountResult = JSON.parse(getAccountResponse.responseText);
					if (!getAccountResult.result) {
						idp.error(getAccountResult.message);
						return false;
					}
					console.log("已从数据库中获取到默认付款账户");
					details[i].PAYACCOUNTID = getAccountResult.data.PAYACCOUNTID;
					details[i].PAYACCOUNTNO = getAccountResult.data.PAYACCOUNTNO;
					details[i].PAYACCOUNTNAME = getAccountResult.data.PAYACCOUNTNAME;
					defaultAccounts.set(key, getAccountResult.data);
				}
			}
		}
	}

	let checkInfos = [];
	for (var i=0; i<details.length; i++) {
		checkInfos.push({
			LOGINFOID: details[i].LOGINFOID,
			AMOUNT: details[i].AMOUNT
		});
	}

	let rows = idp.control.get('grid_JTGKPAYPLANDETAIL').rows;
	if (rows.length > 0) {
		// 保存前再次校验
		let saveCheckResult = idp.service.fetch('/api/jtgk/goldwind/unipaybill/v1.0/save', {
			checkInfos: checkInfos,
			initInfos: initInfos,
		}, false, 'POST');
		if (saveCheckResult.status !== 200) {
			idp.error("请求服务端接口失败");
			return false;
		}
		var saveCheckResultMsgs = JSON.parse(saveCheckResult.responseText);
		if (!saveCheckResultMsgs.result) {
			idp.error(saveCheckResultMsgs.message);
			return false;
		}
	}

	return idp.uiview.saveData();
}

/** 删除 */
function onDeleteCard() {
	var data = idp.uiview.modelController.getMainRowObj();
	var getDocStatusResponse = idp.service.fetch("/api/jtgk/goldwind/unipaybill/v1.0/docStatus?ID=" + data.ID, null, false, "GET");
	if (getDocStatusResponse.status !== 200) {
		idp.error("请求服务端接口失败");
		return false;
	}
	var getDocStatusResult = JSON.parse(getDocStatusResponse.responseText);
	if (!getDocStatusResult.result) {
		idp.error(getDocStatusResult.message);
		return false;
	}
    var docStatus = getDocStatusResult.data;
	if (docStatus == null || docStatus == "" || (docStatus != "0" && docStatus != "3")) {
		idp.alert('当前状态不允许删除');
		idp.uiview.reloadData();
		return false;
	}
	return idp.uiview.deleteData();
}

/** 提交 */
function onSubmitCard() {
	var cannotSave = idp.uiview.fsmController.cannot('save');
	if (false == cannotSave) {
		idp.alert('请先保存再提交审批');
		return false;
	}
	var data = idp.uiview.modelController.getMainRowObj();
	if (data.TXT01 == null || data.TXT01 == "") {
		idp.error("SFS支付方式不能为空");
		return false;
	}
	if (data.SETTLEMENTWAYID == null || data.SETTLEMENTWAYID == "") {
		idp.error("SFS支付方式未映射司库结算方式");
		return false;
	}
	var details = idp.control.get("grid_JTGKPAYPLANDETAIL").rows;
	if (details == null || details.length == 0) {
		idp.error("支付明细不能为空");
		return false;
	}
	var excels = idp.control.get("grid_JTGKPAYPLANEXCEL").rows;
	if (excels != null && excels.length > 0) {
		for (var i=0; i<excels.length; i++) {
			if (excels[i].AMT03 > 0) {
				idp.error("存在超出付款申请金额的付款安排单，不允许提交");
				return false;
			}
		}
	}

	idp.confirm("确定要提交当前付款安排单？", function () {
		var url = "/api/jtgk/goldwind/unipaybill/v1.0/submit?ID=" + data.ID;
		idp.loading("处理中...");
		idp.service.fetch(url, null, true, 'GET').then(res => {
			idp.loaded();
			if (!res.result) {
				idp.warn(res.message);
				return false;
			}else{
				idp.uiview.reloadData();

				idp.info("提交成功");
				setTimeout(() => {
					idp.uiview.close();
				}, 1000);
				return true;
			}
		}).fail(result => {
			idp.error(result.message);
		});
	},function() {});
}


// /** 电子影像 */
// function onImageViewCard() {
//     var currentData = idp.uiview.modelController.getMainRowObj();
//     if (currentData.ID == 'null' || currentData.ID == '' || currentData.ID == 'undefined') {
//         //idp.warn("当前没有数据");
//         return false;
//     }
// 	var docStatus = String(currentData.DOCSTATUS);
// 	var billCateGory = 'jtgkPayPlanBill';
// 	var billywtype = 'jtgkPayPlanBill';
// 	var billtype = 'jtgkPayPlanBill';
// 	var imageOperation = (docStatus == '0' || docStatus == '3') ? 'edit' : 'view';
// 	var billstate = (docStatus == "0" || docStatus == "3") ? 'MAKE' : 'SAVE';
// 	var billId = currentData.ID;
// 	var billCode = currentData.DOCNO;
// 	var orgId = currentData.SRCDOCID;
// 	var tabId = idp.utils.getQuery('styleid');
//     const param = {
//         SourceSys: 'IDP', // 来源系统
//         BillCATEGORY: billCateGory, // 业务大类
//         BillType: billtype, // 单据类型（编号）
//         BillTypeID: billtype, // 单据类型ID
//         BillNM: billId, // 单据ID
//         BillCODE: billCode, // 单据编号
//         DWBH: orgId, // 行政组织ID
//         OPERATION: imageOperation,
//         USERCODE: idp.context.get("UserCode"),
//         BILLSTATE: null,
//         TabID: tabId,
//         IsInvoice: null,
//         IsShowInvoice: null,
//         autoocr: null,
//     };
//     idp.service.fetch('/api/BP/EIS/v1.0/imageapi/getyxurlmap', param, true, '').then(rtnInfo => {
//         if (rtnInfo.code == "ok") {
//             url = rtnInfo.data;
//             idp.openurl(billId, "电子影像", url, '', '');
//         } else {
//             idp.error(rtnInfo.msg);
//             return false;
//         }
//     }).fail(result => {
//         idp.error(result.message);
//     });
// }
