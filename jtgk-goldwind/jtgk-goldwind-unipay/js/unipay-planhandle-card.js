/**
 * 待付池-票据补录-卡片
 * /apps/jtgk/jfkj/extend/unipay-planhandle-card.js
 */
/** 界面控件初始化 */
idp.event.bind("loadData", function (e, data) {
	setToolbarState(e, data);
});

// 工具栏按钮
function setToolbarState(e, data) {
	var state = idp.uiview.modelController.getValue("DOCSTATUS");

    if (state == '2') {
		// 已审批时不允许修改
        idp.control.get("toolbar1").setDisabled("baritem_modify");
        idp.control.get("toolbar1").setDisabled("baritem_submit");
        idp.control.get("toolbar1").setDisabled("baritem_cancelSubmit");
        idp.control.get("toolbar1").setDisabled("baritem_delete");

		idp.control.get("toolbar1").setDisabled("baritem_submit_new");
        idp.control.get("toolbar1").setDisabled("baritem_retract_new");
    }
    if (state == '1') {
		// 审批中不允许修改
        idp.control.get("toolbar1").setDisabled("baritem_modify");
        idp.control.get("toolbar1").setDisabled("baritem_submit");
        idp.control.get("toolbar1").setDisabled("baritem_delete");
        idp.control.get("toolbar1").setEnabled("baritem_cancelSubmit");

		idp.control.get("toolbar1").setDisabled("baritem_submit_new");
        idp.control.get("toolbar1").setEnabled("baritem_retract_new");
    }
    if (state == '0' || state == '3') {
		let status = idp.utils.getQuery("status");
		if (status == "add") {
			idp.control.get("toolbar1").setDisabled("baritem_modify");
			idp.control.get("toolbar1").setEnabled("baritem_submit");
			idp.control.get("toolbar1").setDisabled("baritem_cancelSubmit");
			idp.control.get("toolbar1").setDisabled("baritem_delete");

			idp.control.get("toolbar1").setEnabled("baritem_submit_new");
			idp.control.get("toolbar1").setDisabled("baritem_retract_new");
		}else if (status == "edit") {
			idp.control.get("toolbar1").setDisabled("baritem_modify");
			idp.control.get("toolbar1").setEnabled("baritem_submit");
			idp.control.get("toolbar1").setDisabled("baritem_cancelSubmit");
			idp.control.get("toolbar1").setDisabled("baritem_delete");

			idp.control.get("toolbar1").setEnabled("baritem_submit_new");
			idp.control.get("toolbar1").setDisabled("baritem_retract_new");
		}
    }
	// 允许查看流程
	idp.control.get("toolbar1").setEnabled("baritem_viewProcess");
	idp.control.get("toolbar1").setEnabled("baritem_view_new");
	if (idp.utils.getQuery("workItemId") !== '') {
		// 单据审批时按钮隐藏
		// idp.control.get("toolbar1").toggleBtns(["baritem_close","baritem_add","baritem_cancel","baritem_modify","baritem_save","baritem_submit","baritem_cancelSubmit","baritem_delete","baritem_submit_new","baritem_retract_new"], false);
		idp.control.get("toolbar1").toggleBtns(["baritem_close","baritem_add","baritem_cancel","baritem_modify","baritem_save","baritem_cancelSubmit","baritem_delete","baritem_submit_new","baritem_retract_new"], false);
	} else {
		// idp.control.get("toolbar1").toggleBtns(["baritem_close","baritem_add","baritem_cancel","baritem_modify","baritem_save","baritem_submit","baritem_cancelSubmit","baritem_delete","baritem_submit_new","baritem_retract_new"], true);
		idp.control.get("toolbar1").toggleBtns(["baritem_close","baritem_add","baritem_cancel","baritem_modify","baritem_save","baritem_cancelSubmit","baritem_delete","baritem_submit_new","baritem_retract_new"], true);
	}
}

/** 票据明细：导出excel */
function onExportCard() {
	var data = idp.uiview.modelController.getMainRowObj();
	var gridId = "grid_EXPORTEXCEL";
    var exportOpt = idp.uiview.gridController.getExportOptions(gridId);
    var enumData = {
        "DOCID": data.ID,
		"ISEBC": "true",
		"TYPE": "BILL"
    };
    var extendObj = {
        "fields": exportOpt.filter,
        "enumData": enumData,
        "orders": exportOpt.orders
    }
    return idp.exportExcel(gridId,
        "",
        "票据明细清单",
        null,
        "com.inspur.cloud.jtgk.goldwind.unipay.controller.JfskPayPlanBillImportEvent",
        extendObj,
        null);
}

/** 非直联票据：导出excel */
function onExportCardFilterEBC() {
	var data = idp.uiview.modelController.getMainRowObj();
	var gridId = "grid_EXPORTEXCEL";
	var exportOpt = idp.uiview.gridController.getExportOptions(gridId);
	var enumData = {
		"DOCID": data.ID,
		"ISEBC": "false",
		"TYPE": "BILL"
	};
	var extendObj = {
		"fields": exportOpt.filter,
		"enumData": enumData,
		"orders": exportOpt.orders
	}
	return idp.exportExcel(gridId,
		"",
		"非直联票据清单",
		null,
		"com.inspur.cloud.jtgk.goldwind.unipay.controller.JfskPayPlanBillImportEvent",
		extendObj,
		null);
}

/** 支付明细：导出excel */
function onExportCardPayDetail() {
	var data = idp.uiview.modelController.getMainRowObj();
	var gridId = "grid_JTGKPAYPLANDETAIL";
	var exportOpt = idp.uiview.gridController.getExportOptions(gridId);
	var enumData = {
		"DOCID": data.ID,
		"TYPE": "PAYDETAIL"
	};
	var extendObj = {
		"fields": exportOpt.filter,
		"enumData": enumData,
		"orders": exportOpt.orders
	}
	return idp.exportExcel(gridId,
		"",
		"支付明细清单",
		null,
		"com.inspur.cloud.jtgk.goldwind.unipay.controller.JfskPayPlanBillImportEvent",
		extendObj,
		null);
}

/** 票据明细：选择票据 */
function onChoiceBill() {
	var data = idp.uiview.modelController.getMainRowObj();
	var details = idp.control.get("grid_JTGKPAYPLANDETAIL").rows;
	var totalAmount = 0;
	for (var i=0; i<details.length; i++) {
		totalAmount += details[i].AMOUNT;
	}
    $.leeDialog.open({
        title: '应收票据库存帮助',
        name: 'PayBillDialog',
        width: Math.max(window.innerWidth * 0.6, 900),
        height: Math.max(window.innerHeight * 0.6, 500),
        url: `/apps/fastdweb/views/runtime/page/query/querypreview.html?${$.param({
            'styleid': 'cc242782-2e8b-e135-3038-b1b7b226bb9e',
            'PAYUNITID': data.PAYUNITID,
            'REQUESTAMOUNT': totalAmount
        })}`,
        buttons: [
            {
                id: "dialog_lookup_cancel",
                text: "取消",
                cls: 'lee-dialog-btn-cancel ',
                onclick: function (item, dialog) {
                    dialog.close()
                }
            },
            {
                id: "dialog_lookup_register",
                text: "确认",
                cls: 'lee-btn-primary lee-dialog-btn-ok',
                onclick: function (item, dialog) {
                    var helpGrid = dialog.jiframe[0].contentWindow.idp.control.get('grid_main');
                    var selectBills = helpGrid.allSelected;
                    if (selectBills.length == 0) {
                        return idp.info('请至少选择一条票据');
                    }
					var requestAmount = parseFloat(dialog.jiframe[0].contentWindow.idp.utils.getQuery("REQUESTAMOUNT"));
					var billAmount = 0;
					for (var i=0; i<selectBills.length; i++) {
						billAmount += selectBills[i].SYJE;
					}
					if (billAmount < requestAmount) {
						return idp.info("所选票据剩余金额小于各供应商应付总金额");
					}
					onPayBillDialogOk(selectBills);
                    dialog.close();
                }
            },
        ]
    });
}

/** 调用后端接口进行票据分配 */
function onPayBillDialogOk(selectBills) {
	var data = idp.uiview.modelController.getMainRowObj();
	var billIds = [];
	for (var i=0; i<selectBills.length; i++) {
		billIds.push(selectBills[i].ID);
	}
	var params = {
		docId : data.ID,
		docNo : data.DOCNO,
		billIds : billIds
	};
	idp.loading("处理中...");
	var url = "/api/jtgk/goldwind/unipaybill/v1.0/assignBills";
	var callResponse = idp.service.fetch(url, params, false, "POST");
	idp.loaded();
	if (callResponse.status !== 200) {
		idp.error("背书票据分配后端访问失败");
		return false;
	}
	var callResult = JSON.parse(callResponse.responseText);
	if (!callResult.result) {
		idp.error(callResult.message);
		return false;
	}
    $("#grid_JTGKPAYPLANBILLS").leeUI().loadData({
        Rows: callResult.data.bills
    });
    $("#grid_JTGKPAYPLANDETAIL").leeUI().loadData({
        Rows: callResult.data.details
    });
	return true;
}

/** 编辑 */
function onEditCard() {
	var data = idp.uiview.modelController.getMainRowObj();
	var getDocStatusResponse = idp.service.fetch("/api/jtgk/goldwind/unipaybill/v1.0/handleStatus?DOCID=" + data.ID, null, false, "GET");
	if (getDocStatusResponse.status !== 200) {
		idp.error("请求服务端接口失败");
		return false;
	}
	var getDocStatusResult = JSON.parse(getDocStatusResponse.responseText);
	if (!getDocStatusResult.result) {
		idp.error(getDocStatusResult.message);
		return false;
	}
    var docStatus = getDocStatusResult.data;
	if (docStatus == null || docStatus == "" || docStatus != "0") {
		idp.alert('当前状态不允许编辑');
		return false;
	}
	return idp.uiview.edit();
}

/** 保存 */
function onSaveCard() {
	var details = idp.control.get("grid_JTGKPAYPLANDETAIL").rows;
	if (details == null || details.length == 0) {
		idp.error("支付明细不能为空");
		return false;
	}
	return idp.uiview.saveData();
}

/** 提交 */
function onSubmitCard() {
	var cannotSave = idp.uiview.fsmController.cannot('save');
	if (false == cannotSave) {
		idp.alert('请先保存当前单据');
		return false;
	}
	var data = idp.uiview.modelController.getMainRowObj();
	idp.confirm("确定要完成当前单据的票据补录吗？", function () {
		idp.loading("处理中...");
		var url = "/api/jtgk/goldwind/unipaybill/v1.0/handlePass?DOCID=" + data.ID + "&DOCNO=" + data.DOCNO;
		return idp.service.fetch(url, null, true, "GET")
			.then(function (res) {
				idp.loaded();
				if (!res.result) {
					idp.warn(res.message);
					return false;
				}else{
					idp.uiview.reloadData();
					idp.control.get("toolbar1").setDisabled("baritem_cancel");
					idp.control.get("toolbar1").setDisabled("baritem_modify");
					idp.control.get("toolbar1").setDisabled("baritem_save");
					idp.control.get("toolbar1").setDisabled("baritem_submit");
					idp.control.get("toolbar1").setDisabled("baritem_cancelSubmit");

					idp.info("提交成功");
					setTimeout(() => {
						idp.uiview.close();
					}, 1000);
					return true;
				}
			}).fail(function () {
				idp.loaded();
				return false;
			});
		},function() {});
	return true;
}


/** 提交新 */
function onSubmitCard3New() {
	var cannotSave = idp.uiview.fsmController.cannot('save');
	if (false == cannotSave) {
		idp.alert('请先保存当前单据');
		return false;
	}

	idp.confirm("确定要提交当前票据补录？", function () {
		// 使用共享提交
		var curData = idp.uiview.modelController.deafaultData[0].data[0];

		var djbh = curData.DOCNO;
		var dwid = curData.PAYUNITID;
		var id = curData.ID;
	
		var params = {};
		var data = {};
		data.DJBH = djbh;
		data.BILLID = id;
		data.FORMTYPE = "JFPAYPLAN3";
		data.DWID = dwid;
	
		params.data = data;
		params.BILLID = id;
		params.PFPROCINTERACTINFO = null;
	
		idp.loading("加载中...");
		var url = "/api/jtgk/goldwind/unipaybill/v1.0/submit3New";
		setTimeout(function() {
			idp.service.fetch(url, params, false, "POST")
				.then(function(result) {
					if (result) {
						if (result.result) {
								idp.info("提交审批成功！");
								idp.loaded();

								setTimeout(() => {
									idp.uiview.close();
								}, 1000);

								idp.control.get("toolbar1").setDisabled("baritem_modify");
								idp.control.get("toolbar1").setDisabled("baritem_submit");
								idp.control.get("toolbar1").setDisabled("baritem_delete");
								idp.control.get("toolbar1").setEnabled("baritem_cancelSubmit");
						
								idp.control.get("toolbar1").setDisabled("baritem_submit_new");
								idp.control.get("toolbar1").setEnabled("baritem_retract_new");

								idp.uiview.reloadData();
								return true;
						} else {
							idp.error("提交审批失败！" + result.message);
							return false;
						}
					}
				}).always(function() {
					idp.loaded();
				});
		}, 300);

	},function() {});
}

/** 撤回新 */
function onRetractCard3New() {
	var row = idp.uiview.modelController.deafaultData[0].data[0];
    var docstatus = row.DOCSTATUS;
    if (docstatus != "1") {
        idp.alert("当前单据状态无法撤回!");
        return false;
    }

    var params = {};
    var data = {};
    data.BILLID = row.ID;
    data.FORMTYPE = "JFPAYPLAN3";
    data.DWID = row.PAYUNITID;
    data.DJBH = row.DOCNO;
    data.PFTASKID = row.APPROVALID;
    data.DQHJBH = "STARTNODE";
    params.BILLID = row.ID;
    params.data = data;

    params.PFPROCINTERACTINFO = {};
    var url = "/api/jtgk/goldwind/unipaybill/v1.0/retract3New";

    idp.loading("加载中...");

    setTimeout(function() {
        idp.service.fetch(url, params, false, "POST")
            .then(function(resInfo) {

                idp.loaded();
                if (resInfo.result) {
                    if (resInfo.code == 0) {
                        idp.alert("撤回成功！");

						idp.control.get("toolbar1").setEnabled("baritem_modify");
						idp.control.get("toolbar1").setEnabled("baritem_submit");
						idp.control.get("toolbar1").setEnabled("baritem_delete");
						idp.control.get("toolbar1").setDisabled("baritem_cancelSubmit");
				
						idp.control.get("toolbar1").setEnabled("baritem_submit_new");
						idp.control.get("toolbar1").setDisabled("baritem_retract_new");

                        idp.uiview.reloadData();

                    }
                } else {
                    //弹窗提醒
                    idp.alert("撤回失败！" + resInfo.message);
                }
            }).always(function() {
                idp.loaded();
            });
    }, 300);
}

/** 查看新 */
function onViewCard3New() {
	let url = "/api/jtgk/goldwind/unipaybill/v1.0/view3New";
    var curData = idp.uiview.modelController.deafaultData[0].data[0];

    idp.loading("打开中...");
    var params = {}
    params.BILLID = curData.ID;
    params.FORMTYPE = "JFPAYPLAN2";
    idp.service.fetch(url, params, false, "POST")
        .then(function(result) {
            idp.loaded();
            if (result) {
                if (result.result) {
                    url = encodeURI(encodeURI(result.value.toString()));
                    let mtitile = "查看流程";
                    idp.utils.openurl(curData.ID, mtitile, url, true);
                } else {
                    idp.error(result.message);
                }
            }
        }).always(function() {
            idp.loaded();
        });
}


/** 退回 */
function onCancelSubmitCard() {
	var data = idp.uiview.modelController.getMainRowObj();
	var state = idp.uiview.modelController.getValue("DOCSTATUS");
	if (state == '1') {
		idp.alert("当前单据状态不允许退回");
		return false;
	}

	idp.confirm("确定要退回当前的票据补录吗？", function () {
		var checkResponse = idp.service.fetch("/api/jtgk/goldwind/unipaybill/v1.0/checkBillBack?DOCID=" + data.ID + "&DOCNO=" + data.DOCNO, null, false, "GET");
		if (checkResponse.status !== 200) {
			idp.error("请求服务端接口失败");
			return false;
		}
		var checkResult = JSON.parse(checkResponse.responseText);
		if (!checkResult.result) {
			idp.error(checkResult.message);
			return false;
		}
		idp.uiview.reloadData();
	},function() {});
	return true;
}

function managerImage(){
	let IDs = [];
	var data = idp.uiview.modelController.deafaultData[0].data[0];
	IDs.push(data.ID);
	let url = "/api/jtgk/goldwind/settlement/v1.0/managerImage";
	let params = {};
	params.IDs = IDs;
	params.BILLTYPE = "JFPAYPLAN3";
	idp.service.fetch(url, params, false, "POST")
		.then(function(result) {
			if (result.result) {
				let imageId = result.data;
				var dzyxName = "付款安排附件";
				var status = "edit";
				var billtype="";
				const param = {
					BILLSTATE: 'SAVE',
					BillNM: imageId,
					BillCODE: "付款安排附件",
					BillCATEGORY: '',
					BillType: billtype,
					BillTypeID: billtype,
					OPERATION: status,
					USERCODE: idp.context.get("UserCode"),
					SourceSys: 'SYS',
					MKID: 'CM',
					DWBH: '',
					ISAPP: '0',
					TabID: idp.styleId,
					IsInvoice: '1',
					IsShowInvoice: '1'
				};
				idp.service.fetch('/api/BP/EIS/v1.0/imageapi/getyxurlmap', param)
					.then(rtnInfo => {
						if (rtnInfo.code == "ok") {
							urlstr = rtnInfo.data;
							var opts = {
								title: dzyxName,
								name: 'customWindow',
								isHidden: false,
								showMax: false,
								width: 1000,
								slide: false,
								height: 800,
								url: urlstr,
								cls: "",
								content: "", //内容
								onclose: function() {
								},
							};
							var dg = $.leeDialog.open(opts).max();
						} else {
							idp.error(rtnInfo.msg);
							return false;
						}
					}).fail(function (result) {
					idp.error(result.message);
				});

			} else {
				idp.alert(result.message);
			}
		})
		.fail(function(errMsg) {
			console.log(errMsg);
		});
}