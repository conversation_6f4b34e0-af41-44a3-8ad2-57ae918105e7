package com.inspur.cloud.jtgk.goldwind.unipay.service.impl;

import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.AssignBillsResult;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.RD;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPayPlanDetailEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JtgkBillReceivableInventoryEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.repository.JfskPayPlanDetailRepository;
import com.inspur.cloud.jtgk.goldwind.unipay.repository.JtgkBillReceivableInventoryRepository;
import com.inspur.cloud.jtgk.goldwind.unipay.service.JfskUnipayAssignBillsService;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 背书票据分配
 */
@Service
@Slf4j
public class JfskUnipayAssignBillsServiceImpl implements JfskUnipayAssignBillsService {
    @Autowired
    private JtgkBillReceivableInventoryRepository billReceivableInventoryRepository;
    @Autowired
    private JfskPayPlanDetailRepository payPlanDetailRepository;

    /**
     * 背书票据自动分配
     * @param docId 票据补录表ID
     * @param docNo 单据编号
     * @param billIds 应收票据台账ID
     * @return 分配结果
     */
    @Override
    public RD<AssignBillsResult> assignBills(String docId, String docNo, List<String> billIds) {
        log.info("准备背书票据自动分配处理：docId=" + docId + ", docNo=" + docNo + ", billIds=" + billIds);
        List<JfskPayPlanDetailEntity> details = payPlanDetailRepository.findAllByParentId3(docId);
        if (details == null || details.isEmpty()) {
            log.error("支付明细不能为空：parentId=" + docId);
            return RD.error("支付明细不能为空");
        }
        List<JtgkBillReceivableInventoryEntity> bills = billReceivableInventoryRepository.findAllByIds(billIds);
        if (bills == null || bills.isEmpty()) {
            log.error("背书票据不能为空");
            return RD.error("背书票据不能为空");
        }
        try {
            AssignBillsResult result = assignBills(details, bills);
            return RD.ok(null, result);
        } catch (Throwable ex) {
            log.error(ex.toString());
            return RD.error(ex.toString());
        }
    }

    /**
     * 背书票据自动分配
     * @param details 支付明细
     * @param bills 单据明细
     * @return 分配结果
     */
    public static AssignBillsResult assignBills(List<JfskPayPlanDetailEntity> details, List<JtgkBillReceivableInventoryEntity> bills) {
        log.info("支付明细：" + JSON.toJSON(details));
        log.info("背书票据：" + JSON.toJSON(bills));
        // AssignAmount：剩余待分配金额
        details.forEach(o -> o.setAssignAmount(o.getAmount()));
        Map<String, List<JfskPayPlanDetailEntity>> groupedDetails = details.stream().collect(Collectors.groupingBy(JfskPayPlanDetailEntity::getReceivingUnitId));
        Comparator<Map.Entry<String, List<JfskPayPlanDetailEntity>>> payPlanDetailComparator = new PayPlanDetailMapComparator();
        List<Map.Entry<String, List<JfskPayPlanDetailEntity>>> sortedGroupedDetails = groupedDetails.entrySet().stream().sorted(payPlanDetailComparator).collect(Collectors.toList());
        log.info("分组排序后的支付明细(共" + sortedGroupedDetails.size() + "组)：" + JSON.toJSON(sortedGroupedDetails));
        List<JtgkBillReceivableInventoryEntity> sortedBills = new ArrayList<>(bills);
        Comparator<JtgkBillReceivableInventoryEntity> billComparator = new BillMapDescComparator();
        sortedBills.sort(billComparator);
        log.info("排序后的票据(共" + sortedBills.size() + "张)：" + JSON.toJSON(sortedBills));
        List<Map<String, Object>> assignedDetails = new ArrayList<>();
        List<Map<String, Object>> assignedBills = new ArrayList<>();
        AssignBillsResult results = AssignBillsResult.builder().bills(assignedBills).details(assignedDetails).build();
        /*
         * 供应商应付金额小的优先匹配
         * A：300
         * B：400
         * C：500
         * 票据金额大的优先匹配
         * 票1 1000：300/400/300(C-500)
         * 票2 400：200(C-500)
         */
        int rowIndexOfBill = 1;
        int rowIndexOfDetail = 1;
        for (int i=0; i<sortedBills.size(); i++) {
            JtgkBillReceivableInventoryEntity bill = sortedBills.get(i);
            log.info((i+1) + ".准备分配票据：billNo=" + bill.getBillNo() + ", syje(剩余金额)=" + bill.getSyje());
            // 票据待分配金额
            BigDecimal unassignAmount = bill.getSyje();
            for (int j=0; j<sortedGroupedDetails.size(); j++) {
                String billId = UUID.randomUUID().toString();
                BigDecimal assignedAmount = BigDecimal.ZERO;
                Map.Entry<String, List<JfskPayPlanDetailEntity>> groupedDetail = sortedGroupedDetails.get(j);
                log.info((i+1) + "." + (j+1) + ".准备供应商分组：receivingUnitId=" + groupedDetail.getKey());
                List<JfskPayPlanDetailEntity> sortedDetails = groupedDetail.getValue();
                Comparator<JfskPayPlanDetailEntity> planDetailComparator = new PayPlanDetailComparator();
                sortedDetails.sort(planDetailComparator);
                for (int k=0; k<sortedDetails.size(); k++) {
                    JfskPayPlanDetailEntity detail = sortedDetails.get(k);
                    log.info((i+1) + "." + (j+1) + "." + (k+1) + ".准备检查支付明细：id=" + detail.getId() + ", AssignAmount(待支付金额)=" + detail.getAssignAmount());
                    if (BigDecimal.ZERO.compareTo(detail.getAssignAmount()) >= 0) {
                        log.info("支付明细已分配、不处理");
                        continue;
                    }
                    // 分配金额=min(票据待分配金额,供应商待支付金额)
                    BigDecimal useAmount = unassignAmount;
                    if (useAmount.compareTo(detail.getAssignAmount()) > 0) {
                        useAmount = detail.getAssignAmount();
                    }
                    assignedAmount = assignedAmount.add(useAmount);
                    log.info("票据分配金额=" + assignedAmount + ", 支付明细分配金额=" + useAmount);
                    Map<String, Object> assignedDetail = createAssignedDetail(rowIndexOfDetail, billId, bill, detail, useAmount);
                    rowIndexOfDetail++;
                    log.info("支付明细(已分配)：" + JSON.toJSONString(assignedDetail));
                    assignedDetails.add(assignedDetail);
                    detail.setAssignAmount(detail.getAssignAmount().subtract(useAmount));
                    unassignAmount = unassignAmount.subtract(useAmount);
                    if (BigDecimal.ZERO.compareTo(unassignAmount) >= 0) {
                        log.info("票据金额已分配完");
                        break;
                    }
                }
                if (BigDecimal.ZERO.compareTo(assignedAmount) >= 0) {
                    log.info("供应商已分配、不处理");
                    continue;
                }
                Map<String, Object> assignedBill = createAssignedBill(rowIndexOfBill, billId, bill, sortedDetails.get(0), assignedAmount);
                log.info("票据明细(已分配)：" + JSON.toJSONString(assignedBill));
                assignedBills.add(assignedBill);
                rowIndexOfBill++;
                if (BigDecimal.ZERO.compareTo(unassignAmount) >= 0) {
                    log.info("票据金额已分配完");
                    break;
                }
            }
        }
        log.info("背书票据自动分配完成：" + JSON.toJSON(results));
        return results;
    }

    /** 分配后的支付明细 */
    private static Map<String, Object> createAssignedDetail(int rowIndex, String billId, JtgkBillReceivableInventoryEntity bill, JfskPayPlanDetailEntity detail, BigDecimal useAmount) {
        DateFormat yyyyMMddHHmmss = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateFormat yyyyMMdd = new SimpleDateFormat("yyyy-MM-dd");
        String now = yyyyMMddHHmmss.format(new Date());
        Map<String, Object> result = new HashMap<>();
        result.put("ID", UUID.randomUUID().toString());
        result.put("REFBILLID", billId);
        result.put("ROWNO", rowIndex);
        result.put("REQUESTAMOUNT", detail.getRequestAmount());
        result.put("PAIDAMOUNT", detail.getPaidAmount());
        result.put("PAYINGAMOUNT", detail.getPayingAmount());
        result.put("UNPAYAMOUNT", detail.getUnpayAmount());
        result.put("AMOUNT", detail.getAmount());
        result.put("USEAMOUNT", useAmount);
        result.put("BILLID", bill.getId());
        result.put("BILLNO", bill.getBillNo());
        if (detail.getCreatedOn() != null) {
            String createdOn = yyyyMMddHHmmss.format(detail.getCreatedOn());
            result.put("TIMESTAMPS_CREATEDON", createdOn);
        }
        result.put("TIMESTAMPS_LASTCHANGEDON", now);
        result.put("SUBBILLSTARTSN", bill.getSubbillStartSn());
        result.put("SUBBILLENDSN", bill.getSubbillEndSn());
        if (bill.getBillDueDate() != null) {
            String billDueDate = yyyyMMddHHmmss.format(bill.getBillDueDate());
            result.put("BILLDUEDATE", billDueDate);
        }
        result.put("BILLAMOUNT", bill.getBillAmt());
        result.put("PARENTID", detail.getParentId());
        result.put("PARENTID2", detail.getParentId2());
        result.put("PARENTID3", detail.getParentId3());
        result.put("LOGINFOID", detail.getLogInfoId());
        result.put("REFEXCELID", detail.getRefExcelId());
        result.put("SRCBIZSYS", detail.getSrcBizSys());
        result.put("SRCDOCID", detail.getSrcDocId());
        result.put("SRCDOCTYPE", detail.getSrcDocType());
        result.put("SRCDOCNO", detail.getSrcDocNo());
        result.put("PAYUNITID", detail.getPayUnitId());
        result.put("PAYUNITNAME", detail.getPayUnitName());
        result.put("REQUESTDEPTID", detail.getRequestDeptId());
        result.put("REQUESTDEPTNAME", detail.getRequestDeptName());
        result.put("PAYACCOUNTID", detail.getPayAccountId());
        result.put("PAYACCOUNTNO", detail.getPayAccountNo());
        result.put("PAYACCOUNTNAME", detail.getPayAccountName());
        result.put("RECEIVINGUNITID", detail.getReceivingUnitId());
        result.put("RECEIVINGUNITCODE", detail.getReceivingUnitCode());
        result.put("RECEIVINGUNITNAME", detail.getReceivingUnitName());
        result.put("RECEIVINGBANKACCOUNTID", detail.getReceivingBankAccountId());
        result.put("RECEIVINGBANKACCOUNTNO", detail.getReceivingBankAccountNo());
        result.put("RECEIVINGBANKACCOUNTNAME", detail.getReceivingBankAccountName());
        result.put("RECEIVINGBANKID", detail.getReceivingBankId());
        result.put("RECEIVINGBANKNO", detail.getReceivingBankNo());
        result.put("RECEIVINGBANKNAME", detail.getReceivingBankName());
        result.put("RECIPROCALCOUNTRY", detail.getReciprocalCountry());
        result.put("RECIPROCALPROVINCE", detail.getReciprocalProvince());
        result.put("RECIPROCALCITY", detail.getReciprocalCity());
        result.put("PRIVATEFLAG", detail.getPrivateFlag());
        result.put("ISPRIVATEACCOUNT", detail.getIsPrivateAccount());
        result.put("CURRENCYID", detail.getCurrencyId());
        result.put("TRANSCURRENCYID", detail.getTransCurrencyId());
        result.put("TRANSAMOUNT", detail.getTransAmount());
        result.put("TRANSEXCHANGERATE", detail.getTransExchangeRate());
        if (detail.getExpectPayDate() != null) {
            String expPayDate = yyyyMMddHHmmss.format(detail.getExpectPayDate());
            result.put("EXPECTPAYDATE", expPayDate);
        }
        result.put("SUMMARY", detail.getSummary());
        result.put("DESCRIPTION", detail.getDescription());
        result.put("SRCPAYMETHODCODE", detail.getSrcPayMethodCode());
        result.put("SRCPAYMETHODNAME", detail.getSrcPayMethodName());
        result.put("SETTLEMENTWAYID", detail.getSettlementWayId());
        result.put("BILLPAYWAY", detail.getBillPayWay());
        result.put("FUNDNATUREID", detail.getFundnatureId());
        result.put("CASHFLOWITEMID", detail.getCashflowItemId());
        result.put("DOCSTATUS", detail.getDocStatus());
        result.put("CREATEDBY", detail.getCreatedBy());
        result.put("LASTCHANGEDBY", detail.getLastChangedBy());
        result.put("EXTINNERORDER", detail.getExtInnerOrder());
        result.put("EXTCOSTCENTER", detail.getExtCostCenter());
        result.put("EXTPROFITCENTER", detail.getExtProfitCenter());
        result.put("EXTPROJECTCODE", detail.getExtProjectCode());
        result.put("EXTPURCHASECODE", detail.getExtPurchaseCode());
        result.put("TXT01", detail.getTxt01());
        result.put("TXT02", detail.getTxt02());
        result.put("TXT03", detail.getTxt03());
        result.put("TXT04", detail.getTxt04());
        result.put("TXT05", detail.getTxt05());
        result.put("TXT06", detail.getTxt06());
        result.put("TXT07", detail.getTxt07());
        result.put("TXT08", detail.getTxt08());
        result.put("TXT09", detail.getTxt09());
        result.put("TXT10", detail.getTxt10());
        result.put("TXT11", detail.getTxt11());
        result.put("TXT12", detail.getTxt12());
        result.put("TXT13", detail.getTxt13());
        result.put("TXT14", detail.getTxt14());
        result.put("TXT15", detail.getTxt15());
        result.put("TXT16", detail.getTxt16());
        result.put("TXT17", detail.getTxt17());
        result.put("TXT18", detail.getTxt18());
        result.put("TXT19", detail.getTxt19());
        result.put("TXT20", detail.getTxt20());
        result.put("AMT01", detail.getAmt01());
        result.put("AMT02", detail.getAmt02());
        result.put("AMT03", detail.getAmt03());
        result.put("AMT04", detail.getAmt04());
        result.put("AMT05", detail.getAmt05());
        result.put("AMT06", detail.getAmt06());
        result.put("AMT07", detail.getAmt07());
        result.put("AMT08", detail.getAmt08());
        result.put("AMT09", detail.getAmt09());
        result.put("AMT10", detail.getAmt10());
        if (null != detail.getDate01()) {
            String date01 = yyyyMMdd.format(detail.getDate01());
            result.put("DATE01", date01);
        }
        if (null != detail.getDate02()) {
            String date02 = yyyyMMdd.format(detail.getDate02());
            result.put("DATE02", date02);
        }
        if (null != detail.getDate03()) {
            String date03 = yyyyMMdd.format(detail.getDate03());
            result.put("DATE03", date03);
        }
        if (null != detail.getDate04()) {
            String date04 = yyyyMMdd.format(detail.getDate04());
            result.put("DATE04", date04);
        }
        if (null != detail.getDate05()) {
            String date05 = yyyyMMdd.format(detail.getDate05());
            result.put("DATE05", date05);
        }
        if (null != detail.getDate06()) {
            String date06 = yyyyMMdd.format(detail.getDate06());
            result.put("DATE06", date06);
        }
        if (null != detail.getDate07()) {
            String date07 = yyyyMMdd.format(detail.getDate07());
            result.put("DATE07", date07);
        }
        if (null != detail.getDate08()) {
            String date08 = yyyyMMdd.format(detail.getDate08());
            result.put("DATE08", date08);
        }
        if (null != detail.getDate09()) {
            String date09 = yyyyMMdd.format(detail.getDate09());
            result.put("DATE09", date09);
        }
        if (null != detail.getDate10()) {
            String date10 = yyyyMMdd.format(detail.getDate10());
            result.put("DATE10", date10);
        }
        return result;
    }

    /** 分配后的票据明细 */
    private static Map<String, Object> createAssignedBill(int rowIndex, String billId, JtgkBillReceivableInventoryEntity bill, JfskPayPlanDetailEntity detail, BigDecimal useAmount) {
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = sdf.format(new Date());
        Map<String, Object> result = new HashMap<>();
        result.put("ID", billId);
        result.put("ROWNO", rowIndex);
        result.put("REQUESTAMOUNT", useAmount);
        result.put("TIMESTAMPS_CREATEDON", now);
        result.put("TIMESTAMPS_LASTCHANGEDON", now);
        result.put("BILLID", bill.getId());
        result.put("BILLNO", bill.getBillNo());
        result.put("SUBBILLENDSN", bill.getSubbillEndSn());
        result.put("SUBBILLSTARTSN", bill.getSubbillStartSn());
        result.put("BILLAMOUNT", bill.getBillAmt());
        result.put("SYJE", bill.getSyje());
        result.put("PAYUNITID", detail.getPayUnitId());
        result.put("PAYUNITNAME", detail.getPayUnitName());
        result.put("PARENTID", detail.getParentId3());
        result.put("RECEIVINGUNITID", detail.getReceivingUnitId());
        result.put("RECEIVINGUNITCODE", detail.getReceivingUnitCode());
        result.put("RECEIVINGUNITNAME", detail.getReceivingUnitName());
        result.put("CREATEDBY", detail.getCreatedBy());
        result.put("LASTCHANGEDBY", detail.getLastChangedBy());
        // 根据 bill.getId() 获取 我方收票信息-开户行号RECACCNO
        String recAccNoSql = "select RECACCNO from TMBILLRECEIVABLEINVENTORY where ID='" + bill.getId() + "'";
        log.info(recAccNoSql);
        List<Map<String, Object>> rowsOfRecAccNo = DBUtil.querySql(recAccNoSql);
        if (rowsOfRecAccNo == null || rowsOfRecAccNo.isEmpty()) {
            log.error("未找到我方收票信息：id=" + bill.getId());
            return result;
        }
        result.put("TXT08", rowsOfRecAccNo.get(0).get("RECACCNO"));
        // 根据 我方收票信息-开户行号RECACCNO 获取直联状态
        String directStatusSql = "select ISEBDIRECT from BFBankAccounts where ACCOUNTNO='" + rowsOfRecAccNo.get(0).get("RECACCNO") + "'";
        log.info(directStatusSql);
        List<Map<String, Object>> rowsOfDirectStatus = DBUtil.querySql(directStatusSql);
        if (rowsOfDirectStatus == null || rowsOfDirectStatus.isEmpty()) {
            log.error("未找到银行账户状态：accountNo=" +rowsOfRecAccNo.get(0).get("RECACCNO"));
            result.put("TXT09", "0");
            return result;
        }
        Map<String, Object> rowOfDirectStatus = rowsOfDirectStatus.get(0);
        String directStatus = String.valueOf(rowOfDirectStatus.get("ISEBDIRECT"));
        String isEbc = String.valueOf(bill.getIsEbc());
        if ("1".equals(isEbc) && "1".equals(directStatus)) {
            result.put("TXT09", "1");
        } else {
            result.put("TXT09", "0");
        }
        return result;
    }

    /**
     * 支付明细按供应商分组代付金额升序排序方法
     */
    private static class PayPlanDetailMapComparator implements Comparator<Map.Entry<String, List<JfskPayPlanDetailEntity>>> {
        @Override
        public int compare(Map.Entry<String, List<JfskPayPlanDetailEntity>> o1, Map.Entry<String, List<JfskPayPlanDetailEntity>> o2) {
            BigDecimal amount1 = o1.getValue().stream().map(JfskPayPlanDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal amount2 = o2.getValue().stream().map(JfskPayPlanDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            return amount1.compareTo(amount2);
        }
    }

    /**
     * 票据明细按剩余金额降序排序方法
     */
    private static class BillMapDescComparator implements Comparator<JtgkBillReceivableInventoryEntity> {
        @Override
        public int compare(JtgkBillReceivableInventoryEntity o1, JtgkBillReceivableInventoryEntity o2) {
            BigDecimal amount1 = o1.getSyje();
            BigDecimal amount2 = o2.getSyje();
            return Integer.compare(0, amount1.compareTo(amount2));
        }
    }

    /**
     * 支付明细按待安排余额升序排序方法
     */
    private static class PayPlanDetailComparator implements Comparator<JfskPayPlanDetailEntity> {
        @Override
        public int compare(JfskPayPlanDetailEntity o1, JfskPayPlanDetailEntity o2) {
            BigDecimal amount1 = o1.getAmount();
            BigDecimal amount2 = o2.getAmount();
            return amount1.compareTo(amount2);
        }
    }
}
