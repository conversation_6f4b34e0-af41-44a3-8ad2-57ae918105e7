package com.inspur.cloud.jtgk.goldwind.unipay.service;

import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.gs.tm.tmfnd.fsjspub.api.dto.FsspResultRet;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.rpc.api.annotation.GspServiceBundle;
import io.iec.edp.caf.rpc.api.annotation.RpcServiceMethod;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@GspServiceBundle(applicationName = "TM", serviceUnitName = "CM", serviceName = "DWDBBackListenerRpcService")
public class DWDBBackListenerRpcService {
    private static final Logger log = LoggerFactory.getLogger(DWDBBackListenerRpcService.class);

    @Autowired
    private LogService logService;

    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.goldwind.unipay.service.DWDBBackListenerRpcService.call")
    public FsspResultRet call(Map<String, Object> data) {
        try {
            FsspResultRet result = new FsspResultRet();
            result.setResult(true);
            result.setMessage("success");
            logService.init("DWDBBackListenerRpcService");
            logService.info("DWDBBackListenerRpcService", JSON.toJSONString(data));

            Map<String, Object> contextParam = (Map<String, Object>) data.get("contextParam");

            String FORMTYPE = (String) contextParam.get("FORMTYPE");
            if ("TM_DWDBD".equals(FORMTYPE)) {
                logService.info("DWDBBackListenerRpcService", "getIn");
                String billId = (String) contextParam.get("BILLID");
                logService.info("DWDBBackListenerRpcService", billId);
                String approveopinion = (String) contextParam.get("APPROVEOPINION");
                if (StringUtils.isNotBlank(billId)) {
                    String updateSql = "update tmjsxx set gnbs='1', thyy=?1  where jsdnm = ?2";
                    logService.info("DWDBBackListenerRpcService", updateSql);
                    int count = DBUtil.executeUpdateSQL(updateSql, approveopinion, billId);
                    if (count == 0) {
                        result.setResult(false);
                        result.setMessage("update failed");
                        logService.flush();
                        return result;
                    }
                }
            }

            logService.flush();
            return result;
        } catch (Throwable ex) {
            throw ex;
        }
    }
}
