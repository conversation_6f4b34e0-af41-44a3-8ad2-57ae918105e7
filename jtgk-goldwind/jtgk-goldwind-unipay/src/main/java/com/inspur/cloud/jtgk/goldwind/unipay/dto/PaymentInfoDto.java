package com.inspur.cloud.jtgk.goldwind.unipay.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 待付池付款申请接收接口
 */
@Data
public class PaymentInfoDto {
    /** 来源系统标识 */
    @JSONField(name = "srcBizSys")
    private String srcBizSys;
    /**
     * 来源单据类型
     */
    @JSONField(name = "srcDocType")
    private String srcDocType;
    /** 来源单据内码 */
    @JSONField(name = "srcDocId")
    private String srcDocId;
    /** 来源单据编号 */
    @JSONField(name = "srcDocNo")
    private String srcDocNo;
    /** 申请单位编号 */
    @JSONField(name = "payCompanyCode")
    private String requestUnitCode;
    /** 付款账号 */
    @JSONField(name = "payAccountNo")
    private String payAccountNo;
    /**
     * 申请人编号
     */
    @JSONField(name = "applicantCode")
    private String applicantCode;
    /**
     * 申请人姓名
     */
    @JSONField(name = "applicantName")
    private String applicantName;
    /** 申请部门编号 */
    @JSONField(name = "requestDeptCode")
    private String requestDeptCode;
    /** 申请日期： YYYY-MM-DD格式 */
    @JSONField(name = "applyDate")
    private String originalApplyDate;
    /** 收款方单位编号 */
    @JSONField(name = "receivingUnitCode")
    private String receivingUnitCode;
    /** 收款方名称 */
    @JSONField(name = "receivingUnitName")
    private String receivingUnitName;
    /** 收款银行联行号 */
    @JSONField(name = "receivingBankNo")
    private String receivingBankNo;
    /** 收款银行名称 */
    @JSONField(name = "receivingBankName")
    private String receivingBankName;
    /** 收款银行账号 */
    @JSONField(name = "receivingBankAccountNo")
    private String receivingBankAccountNo;
    /** 收款户名 */
    @JSONField(name = "receivingBankAccountName")
    private String receivingBankAccountName;
    /** 币种编号 */
    @JSONField(name = "currencyCode")
    private String currencyCode;
    /** 期望付款日期: YYYY-MM-DD格式 */
    @JSONField(name = "expectPayDate")
    private String originalExpPayDate;
    /** 申请金额 */
    @JSONField(name = "requestAmount")
    private BigDecimal requestAmount;
    /** 摘要 */
    @JSONField(name = "summary")
    private String summary;
    /** 款项性质编号 */
    @JSONField(name = "fundNatureCode")
    private String fundNatureCode;
    /** 详细说明 */
    @JSONField(name = "description")
    private String description;
    /** 交易币种金额 */
    @JSONField(name = "transAmount")
    private BigDecimal transAmount;
    /** 交易币种编号 */
    @JSONField(name = "transCurrencyCode")
    private String transCurrencyCode;
    /** 交易币种汇率 */
    @JSONField(name = "transExchangeRate")
    private BigDecimal transExchangeRate;

    /** 文件明细 */
    @JSONField
    private List<PaymentFileDto> files;

    /**
     * 是否需要资金排程付款安排（SFS使用）
     * 1,直接到结算办理（默认）;0,付款安排
     */
    @JSONField(name = "extDirectPay")
    private String extDirectPay;
    /**
     * 支付方式
     */
    @JSONField(name = "extPayMethod")
    private String extPayMethod;
    /**
     * 共享拆分支付时关联父单据唯一ID
     */
    @JSONField(name = "refSrcDocId")
    private String refSrcDocId;
    /**
     * 采购组织编号
     */
    @JSONField(name = "extPurchase")
    private String extPurchase;
    /**
     * 项目WBS编号
     */
    @JSONField(name = "extProjectWbsCode")
    private String extProjectWbsCode;
    /**
     * 成本中心编号
     */
    @JSONField(name = "extCostCenter")
    private String extCostCenter;
    /**
     * 内部订单
     */
    @JSONField(name = "extInnerOrder")
    private String extInnerOrder;
    /**
     * 利润中心编号
     */
    @JSONField(name = "extProfitCenter")
    private String extProfitCenter;
    /** 业务单据联查地址 */
    @JSONField(name = "linkUrl")
    private String linkUrl;

    /** 自定义字段：前端业务系统传入的业务字段，待付池展示 */
    @JSONField
    private String txt01;
    @JSONField
    private String txt02;
    @JSONField
    private String txt03;
    @JSONField
    private String txt04;
    @JSONField
    private String txt05;
    @JSONField
    private String txt06;
    @JSONField
    private String txt07;
    @JSONField
    private String txt08;
    @JSONField
    private String txt09;
    @JSONField
    private String txt10;
    @JSONField(name = "TXT11")
    private String txt11;
    @JSONField
    private String txt12;
    @JSONField
    private String txt13;
    @JSONField
    private String txt14;
    @JSONField
    private String txt15;
    @JSONField
    private String txt16;
    @JSONField
    private String txt17;
    @JSONField
    private String txt18;
    @JSONField
    private String txt19;
    @JSONField
    private String txt20;
    @JSONField
    private BigDecimal amt01;
    @JSONField
    private BigDecimal amt02;
    @JSONField
    private BigDecimal amt03;
    @JSONField
    private BigDecimal amt04;
    @JSONField
    private BigDecimal amt05;
    @JSONField
    private BigDecimal amt06;
    @JSONField
    private BigDecimal amt07;
    @JSONField
    private BigDecimal amt08;
    @JSONField
    private BigDecimal amt09;
    @JSONField
    private BigDecimal amt10;
    @JSONField
    private String date01;
    @JSONField
    private String date02;
    @JSONField
    private String date03;
    @JSONField
    private String date04;
    @JSONField
    private String date05;
    @JSONField
    private String date06;
    @JSONField
    private String date07;
    @JSONField
    private String date08;
    @JSONField
    private String date09;
    @JSONField
    private String date10;
}
