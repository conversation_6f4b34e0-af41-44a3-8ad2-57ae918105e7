package com.inspur.cloud.jtgk.goldwind.unipay.service.impl;

import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.R;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.RD;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.*;
import com.inspur.cloud.jtgk.goldwind.unipay.repository.JfskPaymentInfoRepository;
import com.inspur.cloud.jtgk.goldwind.unipay.service.JfskDefaultPayAccountService;
import com.inspur.cloud.jtgk.goldwind.unipay.service.JfskPayPlanService;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.AmountUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.JfskException;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.boot.context.CAFContext;
import io.netty.util.internal.StringUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 付款安排自动处理
 */
@Service
@Slf4j
public class JfskPayPlanServiceImpl implements JfskPayPlanService {
    @Autowired
    @Setter
    private LogService logService;
    @Autowired
    @Setter
    private JfskPaymentInfoRepository requestRepository;
    @Autowired
    @Setter
    private JfskDefaultPayAccountService defaultPayAccountService;

    /**
     * 根据导入的票据明细自动分配到执行明细
     * @param bills 背书票据总额
     * @param details 支付明细
     */
    @Override
    public R autoMatchBill(List<JfskPayPlanBillEntity> bills, List<JfskPayPlanDetailEntity> details) {
        throw new JfskException("废弃的方法");
    }

    /**
     * 根据付款计划从待付池自动匹配支付明细
     * @param docId 付款安排单ID
     * @param docNo 付款安排单号
     * @param srcPayMethodCode 原始支付方式编号
     * @param srcPayMethodName 原始支付方式名称
     * @param settlementWayId 结算方式ID
     * @param plans 付款计划
     * @return 支付明细
     */
    @Override
    public List<JfskPayPlanDetailEntity> autoMatchPlan(String docId, String docNo, String srcPayMethodCode, String srcPayMethodName, String settlementWayId, List<JfskPayPlanExcelEntity> plans) {
        logService.init("K0105");
        log.info("准备根据付款计划从待付池自动匹配支付明细：" + JSON.toJSONString(plans));
        Map<String, JfskBankAccountEntity> cachedDefaultAccounts = new HashMap<>();
        Map<String, List<JfskPayPlanExcelEntity>> groupedPlans = plans.stream().collect(Collectors.groupingBy(o -> o.getPayUnitId() + o.getReceivingUnitId() + o.getPlanNo()));
        Object[] keys = groupedPlans.keySet().toArray();
        List<JfskPayPlanDetailEntity> results = new ArrayList<>();
        for (int i=0; i<keys.length; i++) {
            List<JfskPayPlanExcelEntity> valueOfGroupedPlans = groupedPlans.get(keys[i]);
            String payUnitId = valueOfGroupedPlans.get(0).getPayUnitId();
            String receivingUnitId = valueOfGroupedPlans.get(0).getReceivingUnitId();
            String planNo = valueOfGroupedPlans.get(0).getPlanNo();
            String msg01 = "准备处理第" + (i+1) + "组/共" + keys.length + "组付款计划：payUnitId=" + payUnitId + ", receivingUnitId=" + receivingUnitId + ", planNo=" + planNo;
            log.info(msg01);
            List<JfskPaymentInfoEntity> requestDtos = requestRepository.findUnpayRequests(payUnitId, receivingUnitId, planNo);
            if (requestDtos == null || requestDtos.isEmpty()) {
                log.error("未找到待安排的付款申请：payUnitId=" + payUnitId + ", receivingUnitId=" + receivingUnitId + ", planNo=" + planNo);
                continue;
            }
            log.info("加载的待安排付款申请：" + requestDtos.size());
            for (JfskPaymentInfoEntity request : requestDtos) {
                // 未匹配
                request.setAssigned("0");
                // 已匹配金额，使用工具类确保精度
                request.setAssignedAmount(AmountUtil.normalize(BigDecimal.ZERO));
            }
            for (int j=0; j<valueOfGroupedPlans.size(); j++) {
                JfskPayPlanExcelEntity planDto = valueOfGroupedPlans.get(j);
                String planId = planDto.getId();
                // 使用工具类确保总金额精度
                BigDecimal totalAmount = AmountUtil.normalize(planDto.getTotalAmount());
                String msg02 = "准备匹配第" + (j+1) + "条/共" + valueOfGroupedPlans.size() + "条付款计划：id=" + planId + ", payUnitId=" + payUnitId + ", receivingUnitId=" + receivingUnitId + ", totalAmount=" + totalAmount + ", planNo=" + planNo;
                log.info(msg02);
                BigDecimal remindAmount = AmountUtil.normalize(totalAmount);
                for (int k=0; k<requestDtos.size(); k++) {
                    JfskPaymentInfoEntity requestDto = requestDtos.get(k);
                    if ("1".equals(requestDto.getAssigned())) {
                        continue;
                    }
                    // 使用工具类确保金额精度
                    BigDecimal freeAmount = AmountUtil.subtract(requestDto.getUnplanAmount(), requestDto.getAssignedAmount());
                    if (AmountUtil.isZeroOrNegligible(freeAmount)) {
                        requestDto.setAssigned("1");
                        continue;
                    }
                    BigDecimal useAmount = AmountUtil.normalize(freeAmount.compareTo(remindAmount) >= 0 ? remindAmount : freeAmount);
                    log.info("remindAmount(待分配金额)=" + remindAmount + ", freeAmount(待支付余额)=" + freeAmount + ", useAmount(本次付款)=" + useAmount);
                    JfskPayPlanDetailEntity detailDto = convert(docId, srcPayMethodCode, srcPayMethodName, settlementWayId, planId, requestDto, useAmount);
                    if (StringUtil.isNullOrEmpty(detailDto.getPayAccountId())) {
                        getDefaultAccount(cachedDefaultAccounts, docNo, srcPayMethodCode, requestDto, detailDto);
                    }
                    log.info(JSON.toJSONString(detailDto));
                    results.add(detailDto);
                    // 设置已分配金额，使用工具类确保精度
                    requestDto.setAssignedAmount(AmountUtil.add(requestDto.getAssignedAmount(), useAmount));
                    // 是否全部分配 - 使用容差比较避免精度问题
                    if (AmountUtil.isGreaterOrEqual(requestDto.getAssignedAmount(), requestDto.getUnplanAmount())) {
                        requestDto.setAssigned("1");
                    }
                    remindAmount = AmountUtil.subtract(remindAmount, useAmount);
                    // 使用容差比较判断是否分配完成
                    if (AmountUtil.isZeroOrNegligible(remindAmount)) {
                        log.info("付款计划已分配完成，剩余金额：" + remindAmount);
                        remindAmount = AmountUtil.normalize(BigDecimal.ZERO);
                        break;
                    }
                }
                // 已分配金额、未分配金额，使用工具类确保精度
                planDto.setAmt02(AmountUtil.subtract(totalAmount, remindAmount));
                planDto.setAmt03(AmountUtil.normalize(remindAmount));
            }
        }
        logService.flush();
        log.info("付款计划自动匹配完成");
        return results;
    }

    // 补充付款账户
    private void getDefaultAccount(Map<String, JfskBankAccountEntity> cachedDefaultAccounts, String docNo, String srcPayMethodCode, JfskPaymentInfoEntity requestDto, JfskPayPlanDetailEntity detailDto) {
        String srcBizSys = requestDto.getSrcBizSys();;
        String payUnitCode = requestDto.getRequestUnitCode();
        String purchaseCode = requestDto.getExtPurchaseCode();
        RD<JfskBankAccountEntity> getDefaultPayAccount = defaultPayAccountService.getDefaultPayAccount(logService, srcBizSys, docNo, payUnitCode, purchaseCode, srcPayMethodCode);
        if (getDefaultPayAccount.getResult()) {
            if (getDefaultPayAccount.getData() != null) {
                detailDto.setPayAccountId(getDefaultPayAccount.getData().getId());
                detailDto.setPayAccountNo(getDefaultPayAccount.getData().getAccountNo());
                detailDto.setPayAccountName(getDefaultPayAccount.getData().getAccountName());
            }
        }else{
            throw new JfskException(getDefaultPayAccount.getMessage());
        }
    }

    private JfskPayPlanDetailEntity convert(String parentId, String srcPayMethodCode, String srcPayMethodName, String settlementWayId, String refExcelId, JfskPaymentInfoEntity requestDto, BigDecimal useAmount) {
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        JfskPayPlanDetailEntity result = new JfskPayPlanDetailEntity();
        result.setId(UUID.randomUUID().toString());
        result.setParentId(parentId);
        result.setLogInfoId(requestDto.getId());
        result.setRefExcelId(refExcelId);
        result.setSrcBizSys(requestDto.getSrcBizSys());
        result.setSrcDocType(requestDto.getSrcDocType());
        result.setSrcDocId(requestDto.getSrcDocId());
        result.setSrcDocNo(requestDto.getSrcDocNo());
        result.setSrcPayMethodCode(srcPayMethodCode);
        result.setSrcPayMethodName(srcPayMethodName);
        result.setPayUnitId(requestDto.getRequestUnitId());
        result.setPayUnitName(requestDto.getRequestUnitName());
        result.setRequestDeptId(requestDto.getRequestDeptId());
        result.setRequestDeptName(requestDto.getRequestDeptName());
        result.setPayAccountId(requestDto.getPayAccountId());
        result.setPayAccountNo(requestDto.getPayAccountNo());
        result.setPayAccountName(requestDto.getPayAccountName());
        result.setReceivingUnitId(requestDto.getReceivingUnitId());
        result.setReceivingUnitCode(requestDto.getReceivingUnitCode());
        result.setReceivingUnitName(requestDto.getReceivingUnitName());
        result.setReceivingBankAccountId(requestDto.getReceivingBankAccountId());
        result.setReceivingBankAccountNo(requestDto.getReceivingBankAccountNo());
        result.setReceivingBankAccountName(requestDto.getReceivingBankAccountName());
        result.setReceivingBankId(requestDto.getReceivingBankId());
        result.setReceivingBankNo(requestDto.getReceivingBankNo());
        result.setReceivingBankName(requestDto.getReceivingBankName());
        result.setReciprocalCountry(null);
        result.setReciprocalProvince(null);
        result.setReciprocalCity(null);
        result.setPrivateFlag('1');
        result.setIsPrivateAccount('0');
        result.setCurrencyId(requestDto.getCurrencyId());
        result.setSettlementWayId(settlementWayId);
        result.setRequestAmount(requestDto.getRequestAmount());
        result.setPaidAmount(requestDto.getPaidAmount());
        result.setPayingAmount(requestDto.getPayingAmount());
        result.setUnpayAmount(requestDto.getUnplanAmount());
        result.setAmount(AmountUtil.normalize(useAmount));
        if (!StringUtil.isNullOrEmpty(requestDto.getExpectPayDate())) {
            try {
                Date expPayDate = sdf.parse(requestDto.getExpectPayDate());
                result.setExpectPayDate(expPayDate);
            } catch (Throwable ex) {
                log.error("期望付款日期无效", ex);
            }
        }
        result.setSummary(requestDto.getSummary());
        result.setDescription(requestDto.getDescription());
        result.setBillPayWay(requestDto.getBillPayWay());
        result.setFundnatureId(requestDto.getFundnatureId());
        result.setDocStatus(0);
        String userId = CAFContext.current.getUserId();
        Date now = new Date();
        result.setCreatedBy(userId);
        result.setCreatedOn(now);
        result.setLastChangedBy(userId);
        result.setLastChangedOn(now);
        result.setExtInnerOrder(requestDto.getExtInnerOrder());
        result.setExtCostCenter(requestDto.getExtCostCenter());
        result.setExtProfitCenter(requestDto.getExtProfitCenter());
        result.setExtProjectCode(requestDto.getExtProjectCode());
        result.setExtPurchaseCode(requestDto.getExtPurchaseCode());
        result.setTxt01(requestDto.getTxt01());
        result.setTxt02(requestDto.getTxt02());
        // result.setTxt03(requestDto.getTxt03());

        String queryBillSql = "select TXT02, DATE01 from JTGKPAYPLANBILL where ID = '" + parentId + "'";
        List<Map<String, Object>> rowsOfBill = DBUtil.querySql(queryBillSql);
        if (rowsOfBill != null && rowsOfBill.size() > 0){
            result.setTxt03(rowsOfBill.get(0).get("TXT02").toString());
            Date date01 = (Date)rowsOfBill.get(0).get("DATE01");
            if (date01 != null){
                result.setDate01(date01);
            }
        }else{
            result.setTxt03(requestDto.getTxt03());
        }

        String CSSFLD = "0";
        String payUnitId = (String)requestDto.getRequestUnitId();
        if (StringUtils.isNotBlank(payUnitId)){
            String querySql = "select 1 from IDD_DATADICTIONARY where CATEGORYID = '87908b6c-48c3-96bb-63a9-2f507fd3870f' and CODE = ?1";
            List<Map<String, Object>> rows = DBUtil.querySql(querySql, payUnitId);
            if (rows != null && rows.size() > 0){
                CSSFLD = "1";
            }
        }
        result.setTxt04(CSSFLD);

        result.setTxt05(requestDto.getTxt05());
        result.setTxt06(requestDto.getTxt06());
        result.setTxt07(requestDto.getTxt07());
        result.setTxt08(requestDto.getTxt08());
        result.setTxt09(requestDto.getTxt09());
        result.setTxt10(requestDto.getTxt10());
        result.setTxt11(requestDto.getTxt11());
        result.setTxt12(requestDto.getTxt12());
        result.setTxt13(requestDto.getTxt13());
        result.setTxt14(requestDto.getTxt14());
        result.setTxt15(requestDto.getTxt15());
        result.setTxt16(requestDto.getTxt16());
        result.setTxt17(requestDto.getTxt17());
        result.setTxt18(requestDto.getTxt18());
        result.setTxt19(requestDto.getTxt19());
        result.setTxt20(requestDto.getTxt20());
        result.setAmt01(requestDto.getAmt01());
        result.setAmt02(requestDto.getAmt02());
        result.setAmt03(requestDto.getAmt03());
        result.setAmt04(requestDto.getAmt04());
        result.setAmt05(requestDto.getAmt05());
        result.setAmt06(requestDto.getAmt06());
        result.setAmt07(requestDto.getAmt07());
        result.setAmt08(requestDto.getAmt08());
        result.setAmt09(requestDto.getAmt09());
        result.setAmt10(requestDto.getAmt10());
        if (!StringUtil.isNullOrEmpty(requestDto.getDate01())) {
            try {
                Date date01 = sdf.parse(requestDto.getDate01());
                result.setDate01(date01);
            } catch (Throwable ex) {
                log.error(ex.toString());
            }
        }
        if (!StringUtil.isNullOrEmpty(requestDto.getDate02())) {
            try {
                Date date02 = sdf.parse(requestDto.getDate02());
                result.setDate02(date02);
            } catch (Throwable ex) {
                log.error(ex.toString());
            }
        }
        if (!StringUtil.isNullOrEmpty(requestDto.getDate03())) {
            try {
                Date date03 = sdf.parse(requestDto.getDate03());
                result.setDate03(date03);
            } catch (Throwable ex) {
                log.error(ex.toString());
            }
        }
        if (!StringUtil.isNullOrEmpty(requestDto.getDate04())) {
            try {
                Date date04 = sdf.parse(requestDto.getDate04());
                result.setDate04(date04);
            } catch (Throwable ex) {
                log.error(ex.toString());
            }
        }
        if (!StringUtil.isNullOrEmpty(requestDto.getDate05())) {
            try {
                Date date05 = sdf.parse(requestDto.getDate05());
                result.setDate05(date05);
            } catch (Throwable ex) {
                log.error(ex.toString());
            }
        }
        if (!StringUtil.isNullOrEmpty(requestDto.getDate06())) {
            try {
                Date date06 = sdf.parse(requestDto.getDate06());
                result.setDate06(date06);
            } catch (Throwable ex) {
                log.error(ex.toString());
            }
        }
        if (!StringUtil.isNullOrEmpty(requestDto.getDate07())) {
            try {
                Date date07 = sdf.parse(requestDto.getDate07());
                result.setDate07(date07);
            } catch (Throwable ex) {
                log.error(ex.toString());
            }
        }
        if (!StringUtil.isNullOrEmpty(requestDto.getDate08())) {
            try {
                Date date08 = sdf.parse(requestDto.getDate08());
                result.setDate08(date08);
            } catch (Throwable ex) {
                log.error(ex.toString());
            }
        }
        if (!StringUtil.isNullOrEmpty(requestDto.getDate09())) {
            try {
                Date date09 = sdf.parse(requestDto.getDate09());
                result.setDate09(date09);
            } catch (Throwable ex) {
                log.error(ex.toString());
            }
        }
        if (!StringUtil.isNullOrEmpty(requestDto.getDate10())) {
            try {
                Date date10 = sdf.parse(requestDto.getDate10());
                result.setDate10(date10);
            } catch (Throwable ex) {
                log.error(ex.toString());
            }
        }
        return result;
    }
}
