package com.inspur.cloud.jtgk.goldwind.unipay.service.impl;

import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.R;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.RD;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPayPlanBillEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPayPlanDetailEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPaymentDetailEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.repository.JfskPayPlanBillRepository;
import com.inspur.cloud.jtgk.goldwind.unipay.repository.JfskPayPlanDetailRepository;
import com.inspur.cloud.jtgk.goldwind.unipay.repository.JfskPaymentDetailRepository;
import com.inspur.cloud.jtgk.goldwind.unipay.service.JfskPayBillService;
import com.inspur.cloud.jtgk.goldwind.unipay.service.PayPlan2ProcessBillBase;
import com.inspur.cloud.jtgk.goldwind.unipay.tm.GenerateResultDto;
import com.inspur.cloud.jtgk.goldwind.unipay.tm.bizpaymentrequest.JfskBizPayRequestService;
import com.inspur.gs.tm.tmfnd.fsjspub.api.dto.FsspResultRet;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.JfskException;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.commons.transaction.JpaTransaction;
import io.iec.edp.caf.lockservice.api.*;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 付款安排单
 */
@Service
@Slf4j
public class JfskPayBillServiceImpl implements JfskPayBillService {
    @Autowired
    private ILockService lockService;
    @Autowired
    private JfskPaymentDetailRepository logDetailRepository;
    @Autowired
    private JfskPayPlanBillRepository planBillRepository;
    @Autowired
    private JfskPayPlanDetailRepository planDetailRepository;
    @Autowired
    private JfskBizPayRequestService bizPayRequestService;
    @Autowired
    private PayPlan2ProcessBillBase payPlan2ProcessBillBase;

    /**
     * 内部接口：复核退回
     * @param docId 复核单据ID
     * @param docNo 单据编号
     * @return 处理结果
     */
    @Override
    public R checkBack(String docId, String docNo) {
        String selectDocStatus = "select ID,PAYUNITID,DOCNO,DOCSTATUS from JTGKPAYPLANBILL2 where ID='" + docId + "'";
        log.info(selectDocStatus);
        List<Map<String, Object>> rowsOfDocStatus = DBUtil.querySql(selectDocStatus);
        if (rowsOfDocStatus == null || rowsOfDocStatus.isEmpty()) {
            log.error("未找到指定的付款安排复核表：ID=" + docId);
            return R.error("未找到指定的付款安排复核表：ID=" + docId);
        }
        String docStatus = rowsOfDocStatus.get(0).get("DOCSTATUS").toString();
        if (!"0".equals(docStatus) && !"4".equals(docStatus)) {
            log.error("付款安排复核表当前状态不允许退回：id=" + docId + ", docStatus=" + docStatus);
            return R.error("付款安排复核表当前状态不允许退回，请刷新后重试");
        }
        // 检查同一付款安排的其他明细是否已处理
        String selectSql2 = "select b.ID,b.DOCNO,b.PAYUNITID,o.NAME_CHS,b.DOCSTATUS,\n" +
                "(select PARENTID from JTGKPAYPLANDETAIL d where d.PARENTID2=b.ID limit 1) as PARENTID\n" +
                "from JTGKPAYPLANBILL2 b\n" +
                "inner join BFADMINORGANIZATION o on b.PAYUNITID=o.ID\n" +
                "where b.ID in (select d1.PARENTID2 from JTGKPAYPLANDETAIL d1\n" +
                "inner join JTGKPAYPLANDETAIL d2 on d1.PARENTID=d2.PARENTID\n" +
                "where d2.PARENTID2='" + docId + "')";
        log.info(selectSql2);
        List<Map<String, Object>> rowsOfBill = DBUtil.querySql(selectSql2);
        log.info(JSON.toJSONString(rowsOfBill));
        if (rowsOfBill == null || rowsOfBill.isEmpty()) {
            log.error("未找到关联的付款安排复核表：id=" + docId);
            return R.error("未找到关联的付款安排复核表：id=" + docId);
        }
        Optional<Map<String, Object>> findOtherStatus = rowsOfBill.stream()
                .filter(row -> !"0".equals(row.get("DOCSTATUS").toString()) || !"4".equals(row.get("DOCSTATUS").toString()))
                .findFirst();
        if (findOtherStatus.isPresent()) {
            String msg2 = findOtherStatus.get().get("DOCNO").toString() + "的付款安排已复核，当前单据不允许退回";
            log.error(msg2 + "：ID=" + docId);
            return R.error(msg2);
        }
        // 付款安排表
        String parentId = (String)rowsOfBill.get(0).get("PARENTID");
        // 付款安排复核表
        List<String> docIds = rowsOfBill.stream().map(row -> row.get("ID").toString()).collect(Collectors.toList());
        String jsonOfDocIds = JSON.toJSONString(docIds);
        JpaTransaction transaction = JpaTransaction.getTransaction();
        log.info("数据库事务已启动");
        try {
            transaction.begin();
            String updateBill2 = "update JTGKPAYPLANBILL2 set DOCSTATUS=3 where ID in (" + jsonOfDocIds.replace("\"", "'").replace("[", "").replace("]", "") + ")";
            log.info(updateBill2);
            int count2 = DBUtil.executeUpdateSQL(updateBill2);
            log.info("受影响行数：" + count2);
            String updateBill = "update JTGKPAYPLANBILL set DOCSTATUS=3 where ID='" + parentId + "'";
            log.info(updateBill);
            int count1 = DBUtil.executeUpdateSQL(updateBill);
            log.info("受影响行数：" + count1);
            String updateBill3 = "update JTGKPAYPLANDETAIL set DOCSTATUS=0 where parentId='" + parentId + "'";
            log.info(updateBill3);
            int count3 = DBUtil.executeUpdateSQL(updateBill3);
            log.info("受影响行数：" + count3);

            transaction.commit();
            log.info("数据库事务已提交");
            return R.ok();
        } catch (Throwable ex) {
            transaction.rollback();
            log.error("数据库事务已回滚：", ex);
            return R.error("付款安排复核退回时发生异常：" + ex.getMessage());
        }
    }

    /**
     * 内部接口：票据退回
     * @param docId 复核单据ID
     * @param docNo 单据编号
     * @return 处理结果
     */
    @Override
    public R checkBillBack(String docId, String docNo) {
//        String selectDocStatus = "select ID,PAYUNITID,DOCNO,DOCSTATUS from JTGKPAYPLANBILL3 where ID='" + docId + "'";
//        log.info(selectDocStatus);
//        List<Map<String, Object>> rowsOfDocStatus = DBUtil.querySql(selectDocStatus);
//        if (rowsOfDocStatus == null || rowsOfDocStatus.isEmpty()) {
//            log.error("未找到指定的票据补录表：ID=" + docId);
//            return R.error("未找到指定的票据补录表：ID=" + docId);
//        }
//        String docStatus = rowsOfDocStatus.get(0).get("DOCSTATUS").toString();
//        if (!"0".equals(docStatus)) {
//            log.error("票据补录表当前状态不允许退回：id=" + docId + ", docStatus=" + docStatus);
//            return R.error("票据补录表当前状态不允许退回，请刷新后重试");
//        }
//        // 检查同一付款安排的其他明细是否已安排
//        String selectSql2 = "select b.ID,b.DOCNO,b.PAYUNITID,o.NAME_CHS,b.DOCSTATUS,\n" +
//                "(select PARENTID from JTGKPAYPLANDETAIL d where d.PARENTID2=b.ID limit 1) as PARENTID\n" +
//                "from JTGKPAYPLANBILL2 b\n" +
//                "inner join BFADMINORGANIZATION o on b.PAYUNITID=o.ID\n" +
//                "where b.ID in (select d1.PARENTID2 from JTGKPAYPLANDETAIL d1\n" +
//                "inner join JTGKPAYPLANDETAIL d2 on d1.PARENTID=d2.PARENTID\n" +
//                "where d2.PARENTID3='" + docId + "')";
//        log.info(selectSql2);
//        List<Map<String, Object>> rowsOfBill = DBUtil.querySql(selectSql2);
//        log.info(JSON.toJSONString(rowsOfBill));
//        if (rowsOfBill == null || rowsOfBill.isEmpty()) {
//            log.error("未找到关联的付款安排复核表：id=" + docId);
//            return R.error("未找到关联的付款安排复核表：id=" + docId);
//        }
////        Optional<Map<String, Object>> findOtherStatus = rowsOfBill.stream()
////        .filter(row -> !"0".equals(row.get("DOCSTATUS").toString()))
////        .findFirst();
////        if (findOtherStatus.isPresent()) {
////            String msg2 = findOtherStatus.get().get("DOCNO").toString() + "的付款安排已复核，当前单据不允许退回";
////            log.error(msg2 + "：ID=" + docId);
////            return R.error(msg2);
////        }
//
//        String parentId = (String)rowsOfBill.get(0).get("PARENTID");
//        // 付款安排复核表
//        List<String> docIds = rowsOfBill.stream().map(row -> row.get("ID").toString()).collect(Collectors.toList());
//        String jsonOfDocIds = JSON.toJSONString(docIds);
//
//        // 检查同一付款安排的其他明细是否已票据补录
//        String selectSql3 = "select b.ID,b.DOCNO,b.PAYUNITID,o.NAME_CHS,b.DOCSTATUS,\n" +
//            "(select PARENTID from JTGKPAYPLANDETAIL d where d.PARENTID3=b.ID limit 1) as PARENTID\n" +
//            "from JTGKPAYPLANBILL3 b\n" +
//            "inner join BFADMINORGANIZATION o on b.PAYUNITID=o.ID\n" +
//            "where b.ID in (select d1.PARENTID3 from JTGKPAYPLANDETAIL d1\n" +
//            "inner join JTGKPAYPLANDETAIL d2 on d1.PARENTID=d2.PARENTID\n" +
//            "where d2.PARENTID3='" + docId + "')";
//        log.info(selectSql3);
//        List<Map<String, Object>> rowsOfBill3 = DBUtil.querySql(selectSql3);
//        log.info(JSON.toJSONString(rowsOfBill3));
//        if (rowsOfBill3 == null || rowsOfBill3.isEmpty()) {
//            log.error("未找到关联的票据补录表：id=" + docId);
//            return R.error("未找到关联的票据补录表：id=" + docId);
//        }
//        Optional<Map<String, Object>> findOtherStatus3 = rowsOfBill3.stream()
//            .filter(row -> !"0".equals(row.get("DOCSTATUS").toString()))
//            .findFirst();
//        if (findOtherStatus3.isPresent()) {
//            String msg2 = findOtherStatus3.get().get("").toString() + "的票据补录已复核，当前单据不允许退回";
//            log.error(msg2 + "：ID=" + docId);
//            return R.error(msg2);
//        }
//        // 付款安排表
//        String parentId3 = (String)rowsOfBill3.get(0).get("PARENTID");
//        // 付款安排复核表
//        List<String> docIds3 = rowsOfBill3.stream().map(row -> row.get("ID").toString()).collect(Collectors.toList());
//        String jsonOfDocIds3 = JSON.toJSONString(docIds3);


        // 主动回退流程
        String querySql = "select distinct JTGKPAYPLANBILL2.ID as BILLID, JTGKPAYPLANBILL2.DOCNO as DOCNO, JTGKPAYPLANBILL2.PAYUNITID as PAYUNITID, JTGKPAYPLANBILL2.APPROVALID as APPROVALID from JTGKPAYPLANBILL2\n" +
                "LEFT JOIN JTGKPAYPLANDETAIL on JTGKPAYPLANDETAIL.PARENTID2 = JTGKPAYPLANBILL2.ID\n" +
                "LEFT JOIN JTGKPAYPLANBILL3 on JTGKPAYPLANBILL3.ID = JTGKPAYPLANDETAIL.PARENTID3\n" +
                "where JTGKPAYPLANBILL3.ID = ?1" ;

        List<Map<String, Object>> list = DBUtil.querySql(querySql, docId);
        if (list == null || list.size() == 0) {
            throw new JfskException("goldwind", "PayPlanProcessBillBaseEvent-002", "未找到付款安排复核记录", null);
        }
        String billid2 = (String) list.get(0).get("BILLID");

        Map<String, Object> data = new HashMap<>();
        data.put("BILLID", billid2);
        data.put("FORMTYPE", "JFPAYPLAN2");
        data.put("DWID", list.get(0).get("PAYUNITID"));
        data.put("DJBH", list.get(0).get("DOCNO"));
        data.put("PFTASKID", list.get(0).get("APPROVALID"));
        data.put("DQHJBH", "STARTNODE");
        FsspResultRet result = payPlan2ProcessBillBase.fsspretract(data);
        if (result.isResult()){
            JpaTransaction transaction = JpaTransaction.getTransaction();
            log.info("数据库事务已启动");
            try {
                transaction.begin();
    //            String updateBill2 = "update JTGKPAYPLANBILL2 set DOCSTATUS=3 where ID in (" + jsonOfDocIds.replace("\"", "'").replace("[", "").replace("]", "") + ")";
    //            log.info(updateBill2);
    //            int count2 = DBUtil.executeUpdateSQL(updateBill2);
    //            log.info("受影响行数：" + count2);
    //            String updateBill = "update JTGKPAYPLANBILL set DOCSTATUS=3 where ID='" + parentId + "'";
    //            log.info(updateBill);
    //            int count1 = DBUtil.executeUpdateSQL(updateBill);
    //            log.info("受影响行数：" + count1);
    //
    //
    //            String updateBill3 = "update JTGKPAYPLANBILL3 set DOCSTATUS=3 where ID in (" + jsonOfDocIds3.replace("\"", "'").replace("[", "").replace("]", "") + ")";
    //            log.info(updateBill3);
    //            int count3 = DBUtil.executeUpdateSQL(updateBill3);
    //            log.info("受影响行数：" + count3);
    //            String updateBill4 = "update JTGKPAYPLANBILL3 set DOCSTATUS=3 where ID='" + parentId3 + "'";
    //            log.info(updateBill4);
    //            int count4 = DBUtil.executeUpdateSQL(updateBill4);
    //            log.info("受影响行数：" + count4);



                String updateSql = "update JTGKPAYPLANBILL2 set DOCSTATUS = '4' where ID = ?1";

                int count = DBUtil.executeUpdateSQL(updateSql, billid2);
                if (count == 0) {
                    throw new JfskException("goldwind", "PayPlanProcessBillBaseEvent-002", "付款安排审批更新失败", null);
                }

                // 如果有，票据补录单据作废并
                String updateSql2 = "UPDATE JTGKPAYPLANBILL3 \n" +
                        "SET DOCSTATUS = '3' \n" +
                        "FROM JTGKPAYPLANBILL3 a\n" +
                        "INNER JOIN JTGKPAYPLANDETAIL b ON b.PARENTID3 = a.ID\n" +
                        "INNER JOIN JTGKPAYPLANBILL2 c ON c.ID = b.PARENTID2\n" +
                        "WHERE JTGKPAYPLANBILL3.ID = a.ID\n" +
                        "AND c.ID = ?1";
                DBUtil.executeUpdateSQL(updateSql2, billid2);

                // 更新明细状态为待复核且去掉parentid3
                String updateSql3 = "UPDATE JTGKPAYPLANDETAIL \n" +
                        "SET docstatus = '1', parentId3='' \n" +
                        "FROM JTGKPAYPLANDETAIL a\n" +
                        "INNER JOIN JTGKPAYPLANBILL2 b ON a.parentId2 = b.Id \n" +
                        "WHERE JTGKPAYPLANDETAIL.Id = a.Id\n" +
                        "AND b.Id = ?1";
                DBUtil.executeUpdateSQL(updateSql3, billid2);

                transaction.commit();
                log.info("数据库事务已提交");

                return R.ok();
            } catch (Throwable ex) {
                transaction.rollback();
                log.error("数据库事务已回滚：", ex);
                return R.error("付款安排复核退回时发生异常：" + ex.getMessage());
            }
        }else{
            return R.error(result.getMessage());
        }
    }

    /**
     * 内部接口：复核通过
     * @param logService 接口日志
     * @param docId 复核单据ID
     * @param docNo 单据编号
     * @return 处理结果
     */
    @Override
    public R checkPass(LogService logService, String docId, String docNo, boolean onlyCheck) {
        String selectDocStatus = "select ID,PAYUNITID,DOCNO,DOCSTATUS,BILLPAYWAY,TXT01,NOTE from JTGKPAYPLANBILL2 where ID='" + docId + "'";
        log.info(selectDocStatus);
        logService.info(docNo, selectDocStatus);
        List<Map<String, Object>> rowsOfDocStatus = DBUtil.querySql(selectDocStatus);
        log.info(JSON.toJSONString(rowsOfDocStatus));
        logService.info(docNo, JSON.toJSONString(rowsOfDocStatus));
        if (rowsOfDocStatus == null || rowsOfDocStatus.isEmpty()) {
            log.error("未找到指定的付款安排复核表：ID=" + docId);
            logService.error(docNo, "未找到指定的付款安排复核表：ID=" + docId);
            return R.error("未找到指定的付款安排复核表：ID=" + docId);
        }
        String docStatus = rowsOfDocStatus.get(0).get("DOCSTATUS").toString();
        String note = rowsOfDocStatus.get(0).get("NOTE").toString();
        if (!"1".equals(docStatus) && !"0".equals(docStatus) &&  !"4".equals(docStatus)) {
            log.error("付款安排复核表当前状态不允许通过：id=" + docId + ", docStatus=" + docStatus);
            logService.error(docNo, "付款安排复核表当前状态不允许通过：id=" + docId + ", docStatus=" + docStatus);
            return R.error("付款安排复核表当前状态不允许通过，请刷新后重试");
        }
        Integer billPayWay = (Integer) rowsOfDocStatus.get(0).get("BILLPAYWAY");
        List<JfskPayPlanDetailEntity> details = planDetailRepository.findAllByParentId2(docId);
        log.info(JSON.toJSONString(details));
        logService.info(docNo, JSON.toJSONString(details));
        if (details == null || details.isEmpty()) {
            log.error("未找到付款安排复核关联的的支付明细：id=" + docId);
            logService.error(docNo, "未找到付款安排复核关联的的支付明细：id=" + docId);
            return R.error("未找到付款安排复核关联的支付明细");
        }

        Map<String, BigDecimal> totalAmountMap = new HashMap<>();

        for (JfskPayPlanDetailEntity detail : details) {
            if (BigDecimal.ZERO.compareTo(detail.getAmount()) >= 0) {
                continue;
            }
            if (!StringUtil.isNullOrEmpty(detail.getPayAccountId())) {
                BigDecimal amount = totalAmountMap.get(detail.getPayAccountId());
                if (amount == null) {
                    amount = BigDecimal.ZERO;
                }
                totalAmountMap.put(detail.getPayAccountId(), amount.add(detail.getAmount()));
            }
            if (!"1".equals(detail.getDocStatus().toString())) {
                log.error("付款安排支付明细状态不正确：docId=" + docId + ", docNo=" + docNo + ", detailId=" + detail.getId() + ", detailStatus=" + detail.getDocStatus());
                logService.error(docNo, "付款安排支付明细状态不正确：docId=" + docId + ", docNo=" + docNo + ", detailId=" + detail.getId() + ", detailStatus=" + detail.getDocStatus());
                return R.error("付款安排支付明细状态不正确");
            }
        }

        String txt01 = rowsOfDocStatus.get(0).get("TXT01").toString();
        if ("b637a781-17ea-52cf-5d54-27569db8f058".equals(txt01) || "10395ab1-194d-a12a-e64c-8a5ffb5ed1f1".equals(txt01) || "177ff9f6-ace9-b285-15b8-45ca57835894".equals(txt01)) {
            for (Map.Entry<String, BigDecimal> entry : totalAmountMap.entrySet()) {
                String payAccountId = entry.getKey();
                BigDecimal amount = entry.getValue();
                log.info("付款安排支付明细总金额：payAccountId=" + payAccountId + ", amount=" + amount);
                logService.info(docNo, "付款安排支付明细总金额：payAccountId=" + payAccountId + ", amount=" + amount);
    
                // 获取支付账户余额
                String selectPayAccountBalance = "select CURRENTBALANCE from BPBalanceOnBank left outer join BFBankAccounts on BPBalanceOnBank.Account = BFBankAccounts.ID where BFBankAccounts.ID='" + payAccountId + "' order by BPBalanceOnBank.BALANCEDATETIME desc limit 1";
                log.info(selectPayAccountBalance);
                logService.info(docNo, selectPayAccountBalance);
                List<Map<String, Object>> rowsOfPayAccountBalance = DBUtil.querySql(selectPayAccountBalance);
                log.info(JSON.toJSONString(rowsOfPayAccountBalance));
                logService.info(docNo, JSON.toJSONString(rowsOfPayAccountBalance));
                if (rowsOfPayAccountBalance == null || rowsOfPayAccountBalance.isEmpty()) {
                    log.error("未找到支付账户余额信息：payAccountId=" + payAccountId);
                    logService.error(docNo, "未找到支付账户余额信息：payAccountId=" + payAccountId);
                    return R.error("未找到支付账户余额信息");
                }
                BigDecimal currentBalance = (BigDecimal)rowsOfPayAccountBalance.get(0).get("CURRENTBALANCE");
                if (currentBalance.compareTo(amount) < 0) {
                    log.error("支付账户余额不足：payAccountId=" + payAccountId + ", amount=" + amount + ", currentBalance=" + currentBalance);
                    logService.error(docNo, "支付账户余额不足：payAccountId=" + payAccountId + ", amount=" + amount + ", currentBalance=" + currentBalance);
                    return R.error("支付账户余额不足");
                }
            }
        }

        if (onlyCheck){
            return R.ok();
        }

        JpaTransaction transaction = JpaTransaction.getTransaction();
        log.info("数据库事务已启动");
        logService.info(docNo, "数据库事务已启动");
        try {
            transaction.begin();
            for (int i=0; i<details.size(); i++) {
                JfskPayPlanDetailEntity detail = details.get(i);
                String msg1 = "准备处理第" + (i+1) + "条/共" + details.size() + "条支付明细：id=" + docId + ", docNo=" + docNo + ",detailId=" + detail.getId() + ", srcBizSys=" + detail.getSrcBizSys() + ", srcDocId=" + detail.getSrcDocId() + ", srcDocNo=" + detail.getSrcDocNo();
                log.info(msg1);
                logService.info(docNo, msg1);
                if (BigDecimal.ZERO.compareTo(detail.getAmount()) >= 0) {
                    log.info("本次支付金额为零，不处理：detailId=" + detail.getId());
                    logService.info(docNo, "本次支付金额为零，不处理：detailId=" + detail.getId());
                    String updateDetailStatus = "update JTGKPAYPLANDETAIL set DOCSTATUS=3 where ID='" + detail.getId() + "'";
                    log.info(updateDetailStatus);
                    logService.info(docNo, updateDetailStatus);
                    int countOfUpdateDetailStatus = DBUtil.executeWithoutTrans(updateDetailStatus);
                    log.info("受影响行数：" + countOfUpdateDetailStatus);
                    logService.info(docNo, "受影响行数：" + countOfUpdateDetailStatus);
                    continue;
                }
                if (null != billPayWay && 2 == billPayWay) {
                    String msg3 = "准备生成票据补录：id=" + docId + ", docNo=" + docNo + ",detailId=" + detail.getId() + ", srcBizSys=" + detail.getSrcBizSys() + ", srcDocId=" + detail.getSrcDocId() + ", srcDocNo=" + detail.getSrcDocNo();
                    log.info(msg3);
                    logService.info(docNo, msg3);
                    R generateResult = generatePayPlanBill3(logService, docId, docNo, detail);
                    if (!generateResult.getResult()) {
                        log.error("付款安排支付明细生成票据补录失败：" + generateResult.getMessage());
                        logService.error(docNo, "付款安排支付明细生成票据补录失败：" + generateResult.getMessage());
                        throw new JfskException("goldwind", "JfskPayBillController-02", generateResult.getMessage(), null);
                    }
                } else {
                    String msg2 = "准备生成业务支付申请：id=" + docId + ", docNo=" + docNo + ",detailId=" + detail.getId() + ", srcBizSys=" + detail.getSrcBizSys() + ", srcDocId=" + detail.getSrcDocId() + ", srcDocNo=" + detail.getSrcDocNo();
                    log.info(msg2);
                    logService.info(docNo, msg2);
                    R generateResultDto = generateBizPayment(logService, docId, docNo, detail, note);
                    if (!generateResultDto.getResult()) {
                        log.error("付款安排支付明细生成业务支付申请失败：" + generateResultDto.getMessage());
                        logService.error(docNo, "付款安排支付明细生成业务支付申请失败：" + generateResultDto.getMessage());
                        throw new JfskException("goldwind", "JfskPayBillController-01", generateResultDto.getMessage(), null);
                    }
                }
            }
            log.info("准备更新付款安排复核单状态：id=" + docId);
            logService.info(docNo, "准备更新付款安排复核单状态：id=" + docId);
            String updateDocStatus = "update JTGKPAYPLANBILL2 set DOCSTATUS=2 where ID='" + docId + "'";
            log.info(updateDocStatus);
            logService.info(docNo, updateDocStatus);
            int countOfDocStatus = DBUtil.executeWithoutTrans(updateDocStatus);
            log.info("受影响行数：" + countOfDocStatus);
            logService.info(docNo, "受影响行数：" + countOfDocStatus);
            transaction.commit();
            log.info("数据库事务已提交");
            logService.info(docNo, "数据库事务已提交");
            return R.ok();
        } catch (Throwable ex) {
            transaction.rollback();
            log.error("数据库事务已回滚：", ex);
            logService.error(docNo, "数据库事务已回滚：" + ExceptionUtils.getStackTrace(ex));
            return R.error("付款安排复核通过时发生异常：" + ex.getMessage());
        }
    }

    /**
     * 付款安排复核生成业务支付申请
     * @param logService 接口日志
     * @param docId 付款安排复核表ID
     * @param docNo 付款安排单号
     * @param detail 支付明细
     * @return 是否生成成功
     */
    private R generateBizPayment(LogService logService, String docId, String docNo, JfskPayPlanDetailEntity detail, String note) {
        String lockId;
        try {
            String moduleId = "goldwind";
            String funcId = "generatePayBill";
            String categoryId = "JfskPayBillService";
            String dataID = detail.getLogInfoId();
            String comment = "业务支付申请生单前加锁防止并发操作";
            LockResult lockResult = lockService.addLock(moduleId, categoryId, docId, new DataLockOptions(Duration.ofMinutes(30), ReplacedScope.Exclusive, LockedScope.AppInstance, Duration.ofMinutes(30)), funcId, comment);
            if (lockResult == null || !lockResult.isSuccess()) {
                log.error("业务支付申请生单前加锁失败");
                return R.error("业务支付申请生单前加锁失败");
            }
            lockId = lockResult.getLockId();
            log.info("业务支付申请生单前加锁结果：lockId=" + lockId);
        } catch (Throwable e) {
            log.error("业务支付申请生单前加锁过程发生异常：{}", e.toString());
            return R.error("业务支付申请生单前加锁过程发生异常：" + e.getMessage());
        }
        R result;
        log.info("业务支付申请生单前检查可用余额：id=" + detail.getLogInfoId() + ", srcBizSys=" + detail.getSrcBizSys() + ", srcDocId=" + detail.getSrcDocId());
        logService.info(docNo, "业务支付申请生单前检查可用余额：id=" + detail.getLogInfoId() + ", srcBizSys=" + detail.getSrcBizSys() + ", srcDocId=" + detail.getSrcDocId());
        String selectAmount = "select REQUESTAMOUNT,(select SUM(AMOUNT) from JTGKPAYMENTDETAIL where DOCSTATUS in (0,1,5) and PARENTID=JTGKPAYMENTINFO.ID) as PAYAMOUNT from JTGKPAYMENTINFO where ID='" + detail.getLogInfoId() + "'";
        log.info(selectAmount);
        logService.info(docNo, selectAmount);
        List<Map<String, Object>> rowsOfAmount = DBUtil.querySql(selectAmount);
        if (rowsOfAmount == null || rowsOfAmount.isEmpty()) {
            log.error("未找到代付池申请金额信息：id=" + detail.getLogInfoId());
            logService.error(docNo, "未找到代付池申请金额信息：id=" + detail.getLogInfoId());
            return R.error("未找到代付池申请金额信息");
        }
        log.info(JSON.toJSONString(rowsOfAmount));
        logService.info(docNo, JSON.toJSONString(rowsOfAmount));
        BigDecimal totalAmount = (BigDecimal)rowsOfAmount.get(0).get("REQUESTAMOUNT");
        BigDecimal payedAmount = BigDecimal.ZERO;
        if (rowsOfAmount.get(0).get("PAYAMOUNT") != null) {
            payedAmount = (BigDecimal)rowsOfAmount.get(0).get("PAYAMOUNT");
        }
        BigDecimal planAmount = detail.getAmount();
        log.info("申请金额=" + totalAmount + ", 已付/在途金额=" + payedAmount + ", 本次支付金额=" + planAmount);
        logService.info(docNo, "申请金额=" + totalAmount + ", 已付/在途金额=" + payedAmount + ", 本次支付金额=" + planAmount);

        GenerateResultDto generateResultDto = null;
        if (planAmount.add(payedAmount).compareTo(totalAmount) > 0) {
            String msg1 = "来源系统" + detail.getSrcBizSys() + "单据" + detail.getSrcDocNo() + "超过申请金额：申请金额=" + totalAmount + ", 已付/在途=" + payedAmount + "，本次计划支付=" + planAmount;
            log.error(msg1);
            logService.error(docNo, msg1);
            result = R.error("来源系统" + detail.getSrcBizSys() + "单据" + detail.getSrcDocNo() + "超过申请金额");
        } else {
            // 抵账-不支付、现金折扣-扣减不支付结算方式在付款安排复核后不生成业务支付申请单
            if ("抵账-不支付".equals(detail.getSrcPayMethodCode()) || "现金折扣-扣减不支付".equals(detail.getSrcPayMethodCode())) {
                log.info("抵账-不支付、现金折扣-扣减不支付结算方式在付款安排复核后不生成业务支付申请单");
                logService.info(docNo, "抵账-不支付、现金折扣-扣减不支付结算方式在付款安排复核后不生成业务支付申请单");
            }else{
                generateResultDto = bizPayRequestService.generate(logService, docNo, detail, note);
            }

            if (generateResultDto != null && !generateResultDto.getResult()) {
                log.error("付款安排支付明细生成业务支付申请失败：" + generateResultDto.getMessage());
                logService.error(docNo, "付款安排支付明细生成业务支付申请失败：" + generateResultDto.getMessage());
                result = R.error("付款安排支付明细生成业务支付申请失败：" + generateResultDto.getMessage());
            } else {
                String updateDetailStatus = "update JTGKPAYPLANDETAIL set DOCSTATUS=3 where ID='" + detail.getId() + "'";
                log.info(updateDetailStatus);
                logService.info(docNo, updateDetailStatus);
                int countOfUpdateDetailStatus = DBUtil.executeWithoutTrans(updateDetailStatus);
                log.info("受影响行数：" + countOfUpdateDetailStatus);
//                if(generateResultDto == null){
//                    String updateLogInfoStatus = "update JTGKPAYMENTINFO set DOCSTATUS=3 where ID='" + detail.getLogInfoId() + "'";
//                    log.info(updateLogInfoStatus);
//                    logService.info(docNo, updateLogInfoStatus);
//                    int countOfUpdateLogInfoStatus = DBUtil.executeWithoutTrans(updateLogInfoStatus);
//                    log.info("受影响行数：" + countOfUpdateLogInfoStatus);
//                }
                logService.info(docNo, "受影响行数：" + countOfUpdateDetailStatus);
                JfskPaymentDetailEntity detailEntity = createDetailEntity(docNo, detail, generateResultDto==null?null:generateResultDto.getDocId(), generateResultDto==null?null:generateResultDto.getDocNo(), detail.getAmount());
                log.info("准备保存执行记录表：" + JSON.toJSONString(detailEntity));
                logService.info(docNo, "准备保存执行记录表：id=" + detailEntity.getId());
                logDetailRepository.save(detailEntity);
                log.info("执行记录表保存完成");
                logService.info(docNo, "执行记录表保存完成");

                if (planAmount.add(payedAmount).compareTo(totalAmount) == 0){
                    // 如果正好更新JTGKPAYMENTINFO的状态
                    String updateSql = "update JTGKPAYMENTINFO set DOCSTATUS=2 where DOCSTATUS=1 and ID='" + detail.getLogInfoId() + "'";
                    log.info("准备保存接口日志表：" + updateSql);
                    logService.info(docNo, "准备修改接口日志表：" + updateSql);
                    int count = DBUtil.executeWithoutTrans(updateSql);
                    log.info("受影响行数：" + count);
                }

                result = R.ok();
            }
        }
        try {
            lockService.removeLock(lockId);
            log.info("业务支付申请生单已解锁");
            logService.info(docNo, "业务支付申请生单已解锁");
        } catch (Throwable e) {
            log.error("业务支付申请生单解锁过程发生异常：" + e.toString());
            logService.error(docNo, "业务支付申请生单解锁过程发生异常：" + ExceptionUtils.getStackTrace(e));
        }
        return result;
    }

    /**
     * 付款安排复核生成票据补录明细
     * @param logService 接口日志
     * @param docId 付款安排复核表ID
     * @param docNo 付款安排单号
     * @param detail 支付明细
     * @return 是否生成成功
     */
    private R generatePayPlanBill3(LogService logService, String docId, String docNo, JfskPayPlanDetailEntity detail) {
        log.info("检查是否已存在指定单位、待办理的票据补录表");
        logService.info(docNo, "检查是否已存在指定单位、待办理的票据补录表");
        String parentId3;
        Date now = new Date();
        String selectExists = "select d.ID,d.PARENTID3\n" +
                "from JTGKPAYPLANDETAIL d INNER JOIN JTGKPAYPLANBILL3 b on d.PARENTID3=b.ID\n" +
                "where b.DOCSTATUS=0 and d.PARENTID2='" + docId + "'" +
                " and b.PAYUNITID='" + detail.getPayUnitId() + "'" +
                " and b.SETTLEMENTWAYID='" + detail.getSettlementWayId() + "' and b.BILLPAYWAY=2";
        log.info(selectExists);
        logService.info(docNo, selectExists);
        List<Map<String, Object>> rowsOfBill3 = DBUtil.querySql(selectExists);
        log.info(JSON.toJSONString(rowsOfBill3));
        logService.info(docNo, JSON.toJSONString(rowsOfBill3));
        if (rowsOfBill3 == null || rowsOfBill3.size() == 0) {
            log.info("准备新增票据补录表");
            logService.info(docNo, "准备新增票据补录表");
            parentId3 = UUID.randomUUID().toString();
            String userId = CAFContext.current.getUserId();
            String insertBill3 = "insert into JTGKPAYPLANBILL3 (ID,SRCBIZSYS,DOCNO,PAYUNITID,SETTLEMENTWAYID,BILLPAYWAY," +
                    "DOCSTATUS,INSTANCEID,TIMESTAMPS_CREATEDBY,TIMESTAMPS_CREATEDON,TIMESTAMPS_LASTCHANGEDBY,TIMESTAMPS_LASTCHANGEDON,VERSION," +
                    "EXPPAYDATE,DATASRC,NOTE,AMT01,AMT02,AMT03,AMT04,AMT05,DATE01,DATE02,DATE03,DATE04,DATE05,TXT01,TXT02,TXT03,TXT04,TXT05,TXT06,TXT07,TXT08,TXT09)\n" +
                    "select '" + parentId3 + "' as ID,SRCBIZSYS,DOCNO,'" + detail.getPayUnitId() + "' as PAYUNITID,'" + detail.getSettlementWayId() + "' as SETTLEMENTWAYID,2 as BILLPAYWAY," +
                    "0 as DOCSTATUS,null as INSTANCEID,?1,?2,?1,?2,?2," +
                    "EXPPAYDATE,DATASRC,NOTE,AMT01,AMT02,AMT03,AMT04,AMT05,DATE01,DATE02,DATE03,DATE04,DATE05,TXT01,TXT02,TXT03,TXT04,TXT05,TXT06,TXT07,TXT08,TXT09\n" +
                    "from JTGKPAYPLANBILL2 where ID='" + docId + "'";
            log.info(insertBill3 + ", ?1=" + userId + ", ?2=" + now);
            logService.info(docNo, insertBill3 + ", ?1=" + userId + ", ?2=" + now);
            int countOfInsertBill3 = DBUtil.executeWithoutTrans(insertBill3, userId, now);
            log.info("受影响行数：" + countOfInsertBill3);
            logService.info(docNo, "受影响行数：" + countOfInsertBill3);
        } else {
            parentId3 = (String) rowsOfBill3.get(0).get("PARENTID3");
            String updateBill3 = "update JTGKPAYPLANBILL3 set VERSION=?1 where ID='" + parentId3 + "'";
            log.info(updateBill3 + ", ?1=" + now);
            logService.info(docNo, updateBill3 + ", ?1=" + now);
            int countOfUpdateBill3 = DBUtil.executeWithoutTrans(updateBill3, now);
            log.info("受影响行数：" + countOfUpdateBill3);
            logService.info(docNo, "受影响行数：" + countOfUpdateBill3);
        }
        String updateDetail = "update JTGKPAYPLANDETAIL set DOCSTATUS=2, PARENTID3='" + parentId3 + "' where ID='" + detail.getId() + "'";
        log.info(updateDetail);
        logService.info(docNo, updateDetail);
        int countOfUpdateDetail = DBUtil.executeWithoutTrans(updateDetail);
        log.info("受影响行数：" + countOfUpdateDetail);
        logService.info(docNo, "受影响行数：" + countOfUpdateDetail);
        String updateTotalAmount = "update JTGKPAYPLANBILL3 set TOTALAMOUNT=(select SUM(AMOUNT) from JTGKPAYPLANDETAIL where PARENTID3=JTGKPAYPLANBILL3.ID) where ID='" + parentId3 + "'";
        log.info(updateTotalAmount);
        logService.info(docNo, updateTotalAmount);
        int countOfUpdateAmount = DBUtil.executeWithoutTrans(updateTotalAmount);
        log.info("受影响行数：" + countOfUpdateAmount);
        logService.info(docNo, "受影响行数：" + countOfUpdateAmount);
        return R.ok();
    }

    /**
     * 生成付款单后增加待付池执行记录
     * @param detail 付款安排表支付明细
     * @param requestDocId 业务支付申请ID
     * @param requestDocNo 业务支付申请单号
     * @return 执行记录
     */
    private static JfskPaymentDetailEntity createDetailEntity(String docNo, JfskPayPlanDetailEntity detail, String requestDocId, String requestDocNo, BigDecimal amount) {
        JfskPaymentDetailEntity detailEntity = new JfskPaymentDetailEntity();
        detailEntity.setId(UUID.randomUUID().toString());
        detailEntity.setParentId(detail.getLogInfoId());
        // SFS支付方式
        detailEntity.setSrcPayMethodCode(detail.getSrcPayMethodCode());
        detailEntity.setSrcPayMethodName(detail.getSrcPayMethodName());
        // 结算方式
        detailEntity.setSettlementwayId(detail.getSettlementWayId());
        // 付款安排ID
        detailEntity.setRefDocId(detail.getParentId());
        detailEntity.setRefDocNo(docNo);
        detailEntity.setRefDetailId(detail.getId());
        detailEntity.setPaydocType("0");
        // 业务支付申请
        detailEntity.setRequestDocId(requestDocId == null ? "" : requestDocId);
        detailEntity.setRequestDocNo(requestDocNo == null ? "" : requestDocNo);
        // 本次付款金额
        detailEntity.setPayAccountId(detail.getPayAccountId());
        detailEntity.setAmount(amount);
        detailEntity.setDocStatus(requestDocId == null ? 1 : 0);
        detailEntity.setCreatedOn(new Date());
        detailEntity.setLastchangedOn(new Date());
        return detailEntity;
    }

    /**
     * 内部接口：票据补录完成
     * @param logService 接口日志
     * @param docId 票据补录表ID
     * @param docNo 单据编号
     * @return 处理结果
     */
    @Override
    public R handlePass(LogService logService, String docId, String docNo) {
        String selectDocStatus = "select ID,PAYUNITID,DOCNO,DOCSTATUS,BILLPAYWAY,NOTE from JTGKPAYPLANBILL3 where ID='" + docId + "'";
        log.info(selectDocStatus);
        logService.info(docNo, selectDocStatus);
        List<Map<String, Object>> rowsOfDocStatus = DBUtil.querySql(selectDocStatus);
        log.info(JSON.toJSONString(rowsOfDocStatus));
        logService.info(docNo, JSON.toJSONString(rowsOfDocStatus));
        if (rowsOfDocStatus == null || rowsOfDocStatus.isEmpty()) {
            log.error("未找到指定的票据补录表：ID=" + docId);
            logService.error(docNo, "未找到指定的票据补录表：ID=" + docId);
            return R.error("未找到指定的票据补录表：ID=" + docId);
        }
        String docStatus = rowsOfDocStatus.get(0).get("DOCSTATUS").toString();
        if (!"1".equals(docStatus)) {
            log.error("票据补录表当前状态不允许办理完成：id=" + docId + ", docStatus=" + docStatus);
            logService.error(docNo, "票据补录表当前状态不允许办理完成：id=" + docId + ", docStatus=" + docStatus);
            return R.error("票据补录表当前状态不允许办理完成，请刷新后重试");
        }
        List<JfskPayPlanBillEntity> bills = planBillRepository.findAllByParentId(docId);
        log.info(JSON.toJSONString(bills));
        logService.info(docNo, JSON.toJSONString(bills));
        if (bills == null || bills.isEmpty()) {
            log.error("未找到票据补录关联的票据明细：id=" + docId);
            logService.error(docNo, "未找到票据补录关联的票据明细：id=" + docId);
            return R.error("未找到票据补录关联的票据明细");
        }
        String note = (String) rowsOfDocStatus.get(0).get("NOTE");
        List<JfskPayPlanDetailEntity> details = planDetailRepository.findAllByParentId3(docId);
        log.info(JSON.toJSONString(details));
        logService.info(docNo, JSON.toJSONString(details));
        if (details == null || details.isEmpty()) {
            log.error("未找到票据补录关联的的支付明细：id=" + docId);
            logService.error(docNo, "未找到票据补录关联的的支付明细：id=" + docId);
            return R.error("未找到票据补录关联的支付明细");
        }
        for (JfskPayPlanDetailEntity detail : details) {
            if (!"2".equals(detail.getDocStatus().toString())) {
                log.error("付款安排支付明细状态不正确：docId=" + docId + ", docNo=" + docNo + ", detailId=" + detail.getId() + ", detailStatus=" + detail.getDocStatus());
                logService.error(docNo, "付款安排支付明细状态不正确：docId=" + docId + ", docNo=" + docNo + ", detailId=" + detail.getId() + ", detailStatus=" + detail.getDocStatus());
                return R.error("付款安排支付明细第" + detail.getRowNo() + "行状态不正确");
            }
            if (BigDecimal.ZERO.compareTo(detail.getAmount()) >= 0 || BigDecimal.ZERO.compareTo(detail.getUseAmount()) >= 0) {
                continue;
            }
            if (StringUtil.isNullOrEmpty(detail.getRefBillId())) {
                log.error("支付明细的关联票据不能为空：docId=" + docId + ", docNo=" + docNo + ", detailId=" + detail.getId());
                logService.error(docNo, "支付明细的关联票据不能为空：docId=" + docId + ", docNo=" + docNo + ", detailId=" + detail.getId());
                return R.error("支付明细第" + detail.getRowNo() + "行的背书票据不能为空");
            }
            Optional<JfskPayPlanBillEntity> findBill = bills.stream().filter(b -> b.getId().equals(detail.getRefBillId())).findFirst();
            if (!findBill.isPresent()) {
                log.error("支付明细的关联票据无效：docId=" + docId + ", docNo=" + docNo + ", detailId=" + detail.getId() + ", refBillId=" + detail.getRefBillId());
                logService.error(docNo, "支付明细的关联票据无效：docId=" + docId + ", docNo=" + docNo + ", detailId=" + detail.getId() + ", refBillId=" + detail.getRefBillId());
                return R.error("支付明细第" + detail.getRowNo() + "行的背书票据无效");
            }
        }
        // 按供应商分组
        Map<String, innerGroupedBill> groupedBills = groupBillsByReceivingUnit(bills, details);
        JpaTransaction transaction = JpaTransaction.getTransaction();
        log.info("数据库事务已启动");
        logService.info(docNo, "数据库事务已启动");
        try {
            transaction.begin();
            List<Map.Entry<String, innerGroupedBill>> groupedBillsEntry = groupedBills.entrySet().stream().collect(Collectors.toList());
            for (int i=0; i<groupedBillsEntry.size(); i++) {
                innerGroupedBill groupedBill = groupedBillsEntry.get(i).getValue();
                String msg1 = "1." + (i+1) + ".1.准备生成支付申请：id=" + docId + ", docNo=" + docNo + ", " + JSON.toJSONString(groupedBill);
                log.info(msg1);
                logService.info(docNo, msg1);
                GenerateResultDto generateResultDto = bizPayRequestService.generate(logService, docNo, groupedBill.details, groupedBill.bills, note);
                if (!generateResultDto.getResult()) {
                    log.error("付款安排支付明细生成业务支付申请失败：" + generateResultDto.getMessage());
                    logService.error(docNo, "付款安排支付明细生成业务支付申请失败：" + generateResultDto.getMessage());
                    throw new JfskException("goldwind", "JfskPayBillController-01", generateResultDto.getMessage(), null);
                } else {
                    for (int k=0; k<groupedBill.details.size(); k++) {
                        JfskPayPlanDetailEntity detail = groupedBill.details.get(k);
                        String msg2 = "1." + (i+1) + ".2." + (k+1) + ".1.准备更新支付明细状态：id=" + docId + ", docNo=" + docNo + ", detailId=" + detail.getId();
                        log.info(msg2);
                        logService.info(docNo, msg2);
                        String updateDetailStatus = "update JTGKPAYPLANDETAIL set DOCSTATUS=3 where ID='" + detail.getId() + "'";
                        log.info(updateDetailStatus);
                        logService.info(docNo, updateDetailStatus);
                        int countOfUpdateDetailStatus = DBUtil.executeWithoutTrans(updateDetailStatus);
                        log.info("受影响行数：" + countOfUpdateDetailStatus);
                        logService.info(docNo, "受影响行数：" + countOfUpdateDetailStatus);
                        String msg3 = "1." + (i+1) + ".2." + (k+1) + ".2.准备增加执行记录：id=" + docId + ", docNo=" + docNo + ", detailId=" + detail.getId();
                        log.info(msg3);
                        logService.info(docNo, msg3);
                        JfskPaymentDetailEntity detailEntity = createDetailEntity(docNo, detail, generateResultDto.getDocId(), generateResultDto.getDocNo(), detail.getUseAmount());
                        log.info(JSON.toJSONString(detailEntity));
                        logService.info(docNo, detailEntity.getId());
                        logDetailRepository.save(detailEntity);
                        log.info("执行记录表保存完成");
                        logService.info(docNo, "执行记录表保存完成");
                    }
                }
            }
            log.info("2.1.准备更新无需付款的支付明细状态：id=" + docId);
            logService.info(docNo, "2.1.准备更新无需付款的支付明细状态：id=" + docId);
            String updateDetailStatus = "update JTGKPAYPLANDETAIL set DOCSTATUS=3 where AMOUNT<=0 and PARENTID='" + docId + "'";
            log.info(updateDetailStatus);
            logService.info(docNo, updateDetailStatus);
            int countOfDetailStatus = DBUtil.executeWithoutTrans(updateDetailStatus);
            log.info("受影响行数：" + countOfDetailStatus);
            logService.info(docNo, "受影响行数：" + countOfDetailStatus);
            log.info("2.2.准备更新票据补录单状态：id=" + docId);
            logService.info(docNo, "2.2.准备更新票据补录单状态：id=" + docId);
            String updateDocStatus = "update JTGKPAYPLANBILL3 set DOCSTATUS=2 where ID='" + docId + "'";
            log.info(updateDocStatus);
            logService.info(docNo, updateDocStatus);
            int countOfDocStatus = DBUtil.executeWithoutTrans(updateDocStatus);
            log.info("受影响行数：" + countOfDocStatus);
            logService.info(docNo, "受影响行数：" + countOfDocStatus);

//            String querySql = "select distinct JTGKPAYPLANBILL2.ID as BILLID from JTGKPAYPLANBILL2\n" +
//                    "LEFT JOIN JTGKPAYPLANDETAIL on JTGKPAYPLANDETAIL.PARENTID2 = JTGKPAYPLANBILL2.ID\n" +
//                    "LEFT JOIN JTGKPAYPLANBILL3 on JTGKPAYPLANBILL3.ID = JTGKPAYPLANDETAIL.PARENTID3\n" +
//                    "where JTGKPAYPLANBILL3.ID = ?1";
//            List<Map<String, Object>> rows = DBUtil.querySql(querySql, docId);
//            if (rows != null && rows.size() > 0) {
//                String billId2 = rows.get(0).get("BILLID").toString();
//
//                log.info("准备更新付款安排复核单状态：id=" + billId2);
//                logService.info(docNo, "准备更新付款安排复核单状态：id=" + billId2);
//                String updateDocStatus2 = "update JTGKPAYPLANBILL2 set DOCSTATUS=2 where ID='" + billId2 + "'";
//                log.info(updateDocStatus2);
//                logService.info(docNo, updateDocStatus2);
//                int countOfDocStatus2 = DBUtil.executeWithoutTrans(updateDocStatus2);
//                log.info("受影响行数：" + countOfDocStatus2);
//                logService.info(docNo, "受影响行数：" + countOfDocStatus2);
//            }

            transaction.commit();
            log.info("数据库事务已提交");
            logService.info(docNo, "数据库事务已提交");
            return R.ok();
        } catch (Throwable ex) {
            transaction.rollback();
            log.error("数据库事务已回滚：", ex);
            logService.error(docNo, "数据库事务已回滚：" + ExceptionUtils.getStackTrace(ex));
            return R.error("票据补录完成时发生异常：" + ex.getMessage());
        }
    }

    /**
     * 按供应商对票据明细和支付明细分组用于生成业务支付申请
     * @param bills 票据明细
     * @param details 支付明细
     * @return 分组结果
     */
    private static Map<String, innerGroupedBill> groupBillsByReceivingUnit(List<JfskPayPlanBillEntity> bills, List<JfskPayPlanDetailEntity> details) {
        Map<String, innerGroupedBill> groupedBills = new HashMap<>();
        for (JfskPayPlanBillEntity bill : bills) {
            String receivingUnitId = bill.getReceivingUnitId();
            innerGroupedBill groupedBill;
            if (!groupedBills.containsKey(receivingUnitId)) {
                groupedBill = new innerGroupedBill();
                groupedBill.receivingUnitId = receivingUnitId;
                groupedBill.bills = new ArrayList<>();
                groupedBill.details = new ArrayList<>();
                groupedBills.put(receivingUnitId, groupedBill);
            } else {
                groupedBill = groupedBills.get(receivingUnitId);
            }
            groupedBill.bills.add(bill);
            List<JfskPayPlanDetailEntity> filterDetails = details.stream()
                    .filter(detail -> detail.getRefBillId().equals(bill.getId()))
                    .collect(Collectors.toList());
            for (JfskPayPlanDetailEntity detail : filterDetails) {
                if (BigDecimal.ZERO.compareTo(detail.getAmount()) >= 0 || BigDecimal.ZERO.compareTo(detail.getUseAmount()) >= 0) {
                    continue;
                }
                groupedBill.details.add(detail);
            }
        }
        return groupedBills;
    }

    /**
     * 内部接口：批量复核通过 - 支持并行处理以提高性能
     * @param logService 接口日志
     * @param billInfos 批量单据信息列表
     * @return 批量处理结果
     */
    @Override
    public RD<List<Map<String, Object>>> batchCheckPassAndSubmit(LogService logService, List<Map<String, Object>> billInfos) {
        log.info("开始批量复核处理，单据数量：" + billInfos.size());
        logService.info("BATCH", "开始批量复核处理，单据数量：" + billInfos.size());

        if (billInfos == null || billInfos.isEmpty()) {
            log.error("批量处理请求参数不正确");
            return RD.error("批量处理请求参数不正确");
        }

        // 创建线程池用于并行处理，限制最大线程数
        Executor executor = Executors.newFixedThreadPool(Math.min(billInfos.size(), 8));

        // 创建CompletableFuture列表来并行处理每个单据
        List<CompletableFuture<Map<String, Object>>> futures = new ArrayList<>();

        for (Map<String, Object> billInfo : billInfos) {
            CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                String docId = (String) billInfo.get("docId");
                String docNo = (String) billInfo.get("docNo");
                Integer billPayWay = (Integer) billInfo.get("billPayWay");
                String payUnitId = (String) billInfo.get("payUnitId");

                Map<String, Object> result = new HashMap<>();
                result.put("docId", docId);
                result.put("docNo", docNo);
                result.put("billPayWay", billPayWay);


                String unitCodeSql = "select CODE from BFADMINORGANIZATION where ID='" + payUnitId + "'";
                List<Map<String, Object>> rowsOfUnitCode = DBUtil.querySql(unitCodeSql);
                if (rowsOfUnitCode != null && !rowsOfUnitCode.isEmpty()) {
                    result.put("payUnitId", rowsOfUnitCode.get(0).get("CODE").toString());
                }
                
                String querySql = "select TXT01 from JTGKPAYPLANBILL2 where ID='" + docId + "'";
                List<Map<String, Object>> rows = DBUtil.querySql(querySql);
                if (rows == null || rows.isEmpty()) {
                    log.error("未找到指定的票据补录表：ID=" + docId);
                    result.put("success", false);
                    result.put("message", "未找到指定的票据补录表：ID=" + docId);
                    return result;
                }
                String TXT01 = rows.get(0).get("TXT01").toString();

                try {
                    // 根据BILLPAYWAY判断处理逻辑
                    if ((billPayWay != null && billPayWay == 2) || ("3a12db33-4e04-9186-9634-6d58681f52f9".equals(TXT01)) || ("f028636f-942c-769b-b6de-50f824504d9a".equals(TXT01))) {
                        // 票据支付方式：先预校验，再提交审批
                        result = processBillPayWay2(logService, docId, docNo, payUnitId);
                    } else {
                        // 其他支付方式：直接复核通过
                        result = processBillPayWayOther(logService, docId, docNo);
                    }

                    if (rowsOfUnitCode != null && !rowsOfUnitCode.isEmpty()) {
                        result.put("payUnitId", rowsOfUnitCode.get(0).get("CODE").toString());
                    }

                    log.info("单据 " + docNo + " 处理完成：" + result.get("message"));

                } catch (Exception e) {
                    log.error("处理单据 " + docNo + " 时发生异常：", e);
                    result.put("success", false);
                    result.put("message", "处理发生异常：" + e.getMessage());
                }

                return result;
            }, executor);

            futures.add(future);
        }

        // 等待所有任务完成并收集结果
        List<Map<String, Object>> results = new ArrayList<>();
        try {
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
            );

            // 等待所有任务完成
            allFutures.get();

            // 收集所有结果
            for (CompletableFuture<Map<String, Object>> future : futures) {
                results.add(future.get());
            }

        } catch (Exception e) {
            log.error("批量处理过程中发生异常：", e);
            logService.error("BATCH", "批量处理过程中发生异常：" + ExceptionUtils.getStackTrace(e));
            // 关闭线程池
            if (executor instanceof java.util.concurrent.ExecutorService) {
                ((java.util.concurrent.ExecutorService) executor).shutdown();
            }
            return RD.error("批量处理过程中发生异常：" + e.getMessage());
        }

        // 关闭线程池
        if (executor instanceof java.util.concurrent.ExecutorService) {
            ((java.util.concurrent.ExecutorService) executor).shutdown();
        }

        // 统计处理结果
        long successCount = results.stream().mapToLong(r -> (Boolean) r.get("success") ? 1 : 0).sum();
        long failCount = results.size() - successCount;

        String summaryMessage = String.format("批量处理完成：总计 %d 条，成功 %d 条，失败 %d 条", 
            results.size(), successCount, failCount);

        log.info(summaryMessage);
        logService.info("BATCH", summaryMessage);

        RD<List<Map<String, Object>>> response = new RD<>();
        response.setResult(true);
        response.setMessage(summaryMessage);
        response.setData(results);

        return response;
    }

    /**
     * 处理票据支付方式（BILLPAYWAY=2）：先预校验，再提交审批
     */
    private Map<String, Object> processBillPayWay2(LogService logService, String docId, String docNo, String payUnitId) {
        Map<String, Object> result = new HashMap<>();
        result.put("docId", docId);
        result.put("docNo", docNo);

        try {
            // 第一步：预校验（ONLYCHECK=true）
            R checkResult = this.checkPass(logService, docId, docNo, true);
            if (!checkResult.getResult()) {
                result.put("success", false);
                result.put("message", "预校验失败：" + checkResult.getMessage());
                return result;
            }

            // 第二步：提交审批
            Map<String, Object> submitData = new HashMap<>();
            submitData.put("DJBH", docNo);
            submitData.put("BILLID", docId);
            submitData.put("FORMTYPE", "JFPAYPLAN2");
            submitData.put("DWID", payUnitId);

            FsspResultRet submitResult = payPlan2ProcessBillBase.fsspsubmit(submitData, null);
            if (submitResult.getCode() != 0) {
                result.put("success", false);
                result.put("message", "提交审批失败：" + submitResult.getMessage());
                return result;
            }

            result.put("success", true);
            result.put("message", "提交审批成功");
            return result;

        } catch (Exception e) {
            log.error("处理票据支付方式单据时发生异常：", e);
            result.put("success", false);
            result.put("message", "处理异常：" + e.getMessage());
            return result;
        }
    }

    /**
     * 处理其他支付方式：直接复核通过
     */
    private Map<String, Object> processBillPayWayOther(LogService logService, String docId, String docNo) {
        Map<String, Object> result = new HashMap<>();
        result.put("docId", docId);
        result.put("docNo", docNo);

        try {
            // 直接复核通过（ONLYCHECK=false）
            R checkResult = this.checkPass(logService, docId, docNo, false);
            
            result.put("success", checkResult.getResult());
            result.put("message", checkResult.getResult() ? "提交成功" : checkResult.getMessage());
            
            return result;

        } catch (Exception e) {
            log.error("处理其他支付方式单据时发生异常：", e);
            result.put("success", false);
            result.put("message", "处理异常：" + e.getMessage());
            return result;
        }
    }

    private static class innerGroupedBill {
        // 供应商ID
        public String receivingUnitId;
        // 背书票据明细
        public List<JfskPayPlanBillEntity> bills;
        // 业务支付明细
        public List<JfskPayPlanDetailEntity> details;
    }
}
