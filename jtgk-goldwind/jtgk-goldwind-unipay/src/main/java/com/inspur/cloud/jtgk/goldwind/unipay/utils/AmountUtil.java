package com.inspur.cloud.jtgk.goldwind.unipay.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 金额精度处理工具类
 * 解决金额计算中的精度问题，确保财务数据准确性
 */
public class AmountUtil {
    
    /**
     * 金额标准精度：2位小数
     */
    public static final int STANDARD_SCALE = 2;
    
    /**
     * 金额标准舍入模式：四舍五入
     */
    public static final RoundingMode STANDARD_ROUNDING_MODE = RoundingMode.HALF_UP;
    
    /**
     * 容差阈值：0.01
     */
    public static final BigDecimal TOLERANCE = new BigDecimal("0.01");
    
    /**
     * 标准化金额精度
     * @param amount 原始金额
     * @return 标准化后的金额（2位小数，四舍五入）
     */
    public static BigDecimal normalize(BigDecimal amount) {
        if (amount == null) {
            return BigDecimal.ZERO.setScale(STANDARD_SCALE, STANDARD_ROUNDING_MODE);
        }
        return amount.setScale(STANDARD_SCALE, STANDARD_ROUNDING_MODE);
    }
    
    /**
     * 金额加法
     * @param amount1 金额1
     * @param amount2 金额2
     * @return 标准化后的和
     */
    public static BigDecimal add(BigDecimal amount1, BigDecimal amount2) {
        return normalize(normalize(amount1).add(normalize(amount2)));
    }
    
    /**
     * 金额减法
     * @param amount1 被减数
     * @param amount2 减数
     * @return 标准化后的差
     */
    public static BigDecimal subtract(BigDecimal amount1, BigDecimal amount2) {
        return normalize(normalize(amount1).subtract(normalize(amount2)));
    }
    
    /**
     * 金额比较（使用容差）
     * @param amount1 金额1
     * @param amount2 金额2
     * @return 如果两个金额的差值在容差范围内则返回true
     */
    public static boolean isEqual(BigDecimal amount1, BigDecimal amount2) {
        BigDecimal diff = normalize(amount1).subtract(normalize(amount2)).abs();
        return diff.compareTo(TOLERANCE) <= 0;
    }
    
    /**
     * 判断金额是否小于等于容差（可视为零）
     * @param amount 金额
     * @return 如果金额小于等于容差则返回true
     */
    public static boolean isZeroOrNegligible(BigDecimal amount) {
        return normalize(amount).compareTo(TOLERANCE) <= 0;
    }
    
    /**
     * 判断第一个金额是否大于等于第二个金额（考虑容差）
     * @param amount1 金额1
     * @param amount2 金额2
     * @return 如果amount1大于等于amount2（考虑容差）则返回true
     */
    public static boolean isGreaterOrEqual(BigDecimal amount1, BigDecimal amount2) {
        return normalize(amount1).compareTo(normalize(amount2)) >= 0 || 
               isEqual(amount1, amount2);
    }
}