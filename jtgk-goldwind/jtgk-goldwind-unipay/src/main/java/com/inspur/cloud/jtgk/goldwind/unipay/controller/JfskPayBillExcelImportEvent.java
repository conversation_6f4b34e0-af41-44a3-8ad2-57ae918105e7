package com.inspur.cloud.jtgk.goldwind.unipay.controller;

import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.R;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPayPlanDetailEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPayPlanExcelEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.repository.JfskPayPlanDetailRepository;
import com.inspur.cloud.jtgk.goldwind.unipay.repository.JfskPayPlanExcelRepository;
import com.inspur.cloud.jtgk.goldwind.unipay.service.JfskPayPlanService;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.JfskException;
import com.inspur.fastdweb.mgrcard.EventEx;
import com.inspur.fastdweb.mgrcard.FilterEvent;
import com.inspur.fastdweb.mgrcard.FilterState;
import com.inspur.fastdweb.mgrcard.Request;
import com.inspur.fastdweb.model.excel.ExcelObject;
import com.inspur.fastdweb.service.excel.IExcelImportEvent;
import io.iec.edp.caf.commons.transaction.JpaTransaction;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Controller;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 付款安排表业务明细导入excel事件
 */
@Controller
@Slf4j
public class JfskPayBillExcelImportEvent implements IExcelImportEvent, FilterEvent {
    /**
     * 导入前校验
     */
    @Override
    public ExcelObject beforeImport(ExcelObject excelObject) {
        log.info("导入数据之前检查数据有效性");
        if (excelObject == null || excelObject.rows == null) {
            log.error("excelObject数据为空");
            throw new JfskException("goldwind", "JfskPayBillExcelImportEvent-001", "excelObject不能为空", null);
        }
        log.info(JSON.toJSONString(excelObject.rows));
        // 获取起始行
        int rowStartIndex = 0;
        try {
            rowStartIndex = Integer.parseInt(excelObject.rowStart) - 1;
            log.info("rowStartIndex={}", rowStartIndex);
        } catch (NumberFormatException e) {
            log.error("excelObject.rowStart有误");
            throw new JfskException("goldwind", "JfskPayBillExcelImportEvent-002", "excelObject.rowStart有误", e);
        }
        // 获取业务数据对应字段顺序
        Integer indexOfZjjhbh = null, indexOfGsdm = null, indexOfGysbm = null, indexOfSjzfje = null, indexOfFkfs = null;
        List<String> headerRow = excelObject.rows.get(0).result;
        for (int i=0; i<headerRow.size(); i++) {
            String title = (String) headerRow.get(i);
            if ("资金计划编号".equals(title)) {
                indexOfZjjhbh = i;
            } else if ("公司代码".equals(title)) {
                indexOfGsdm = i;
            } else if ("供应商编码".equals(title)) {
                indexOfGysbm = i;
            } else if ("实际支付金额".equals(title)) {
                indexOfSjzfje = i;
            } else if ("付款方式".equals(title)) {
                indexOfFkfs = i;
            }
        }
        log.info("indexOfZjjhbh={}, indexOfGsdm={}, indexOfGysbm={}, indexOfSjzfje={}", indexOfZjjhbh, indexOfGsdm, indexOfGysbm, indexOfSjzfje);
        if (indexOfZjjhbh == null || indexOfGsdm == null || indexOfGysbm == null || indexOfSjzfje == 0) {
            StringBuilder errors = new StringBuilder();
            if (indexOfZjjhbh == null) {
                errors.append("未从Excel文件中找到“资金计划编号”列\n");
            }
            if (indexOfGsdm == null) {
                errors.append("未从Excel文件中找到“公司代码”列\n");
            }
            if (indexOfGysbm == null) {
                errors.append("未从Excel文件中找到“供应商编码”列\n");
            }
            if (indexOfSjzfje == null) {
                errors.append("未从Excel文件中找到“实际支付金额”列\n");
            }
            throw new JfskException("goldwind", "JfskPayBillExcelImportEvent-003", errors.toString(), null);
        }
        StringBuilder errorsOfData = new StringBuilder();

        // 比较付款方式是否保持一致
        String lastPayMethod = null;

        for (int i = rowStartIndex; i < excelObject.rows.size(); i++) {
            List<String> currentRow = excelObject.rows.get(i).result;
            log.info("开始检查第" + (i) + "行数据：" + JSON.toJSONString(currentRow));
            // 资金计划编号
            String zjjhbh = (String) currentRow.get(indexOfZjjhbh.intValue());
            if (StringUtil.isNullOrEmpty(zjjhbh)) {
                log.error("第" + (i+1) + "行资金计划编号不能为空");
                errorsOfData.append("第" + (i+1) + "行资金计划编号不能为空\n");
            }
            // 公司代码
            String payUnitCode = (String) currentRow.get(indexOfGsdm.intValue());
            String payUnitId = null;
            if (StringUtil.isNullOrEmpty(payUnitCode)) {
                log.error("第" + (i+1) + "行公司代码不能为空");
                errorsOfData.append("第" + (i+1) + "行公司代码不能为空\n");
            } else {
                String selectPayUnit = "select ID,CODE,NAME_CHS from BFADMINORGANIZATION where ORGTYPE='9edf25d1-b991-4dde-90b9-30145422d24d' and CODE=?1";
                log.info(selectPayUnit + ", ?1=" + payUnitCode);
                List<Map<String, Object>> rowsOfPayUnit = DBUtil.querySql(selectPayUnit, payUnitCode);
                log.info(JSON.toJSONString(rowsOfPayUnit));
                if (rowsOfPayUnit == null || rowsOfPayUnit.size() == 0) {
                    log.error("第" + (i+1) + "行公司代码无效");
                    errorsOfData.append("第" + (i+1) + "行公司代码" + payUnitCode + "无效");
                } else {
                    payUnitId = (String) rowsOfPayUnit.get(0).get("ID");
                    log.info("payUnitId={}", payUnitId);
                }
            }
            // 供应商编码
            String partnerCode = (String) currentRow.get(indexOfGysbm.intValue());
            String partnerId = null;
            if (StringUtil.isNullOrEmpty(partnerCode)) {
                log.error("第" + (i+1) + "行供应商编码不能为空");
                errorsOfData.append("第" + (i+1) + "行供应商编码不能为空");
            } else {
                String selectOfPartner = "select ID,CODE,NAME_CHS from BFPARTNER where STATE_ISENABLED='1' AND CODE=?1";
                log.info(selectOfPartner + ", ?1=" + partnerCode);
                List<Map<String, Object>> rowsOfPartner = DBUtil.querySql(selectOfPartner, partnerCode);
                log.info(JSON.toJSONString(rowsOfPartner));
                if (rowsOfPartner == null || rowsOfPartner.size() == 0) {
                    log.error("第" + (i+1) + "行供应商编码无效");
                    errorsOfData.append("第" + (i+1) + "行供应商编码" + partnerCode + "无效");
                } else {
                    partnerId = (String) rowsOfPartner.get(0).get("ID");
                    log.info("partnerId={}", partnerId);
                }
            }
            // 实际支付金额
            Object payAmount = currentRow.get(indexOfSjzfje.intValue());
            if (payAmount == null) {
                log.error("第" + (i+1) + "行实际支付金额不能为空");
                errorsOfData.append("第" + (i+1) + "行实际支付金额不能为空");
            } else {
                try {
                    new BigDecimal(payAmount.toString());
                } catch (Throwable ex) {
                    log.error("第" + (i+1) + "行实际支付金额无效");
                    errorsOfData.append("第" + (i+1) + "行实际支付金额" + payAmount + "无效");
                }
            }
            // 付款方式
            String payMethod = (String) currentRow.get(indexOfFkfs.intValue());
            if (!StringUtil.isNullOrEmpty(payMethod)) {
                if (lastPayMethod == null){
                    lastPayMethod = payMethod;
                }else{
                    if (!payMethod.equals(lastPayMethod)){
                        log.error("第" + i + "行付款方式不一致");
                        errorsOfData.append("第" + i + "行付款方式不一致");
                    }
                }
            }
        }
        if (errorsOfData.length() > 0) {
            log.info("数据检查失败：" + errorsOfData.toString());
            throw new JfskException("goldwind", "JfskPayBillExcelImportEvent-004", errorsOfData.toString(), null);
        }

        excelObject.isContinue = true;
        log.info("数据检查通过");
        return excelObject;
    }

    /**
     * 新增保存前
     */
    @Override
    public void beforeAdd(Request request, EventEx ex) {
        log.info("新增保存前");
        Map<String, List<Map>> details = request.detailData;
        if (details.containsKey("JTGKPAYPLANEXCEL")) {
            List<Map> detailsOfExcel = details.get("JTGKPAYPLANEXCEL");
            if (detailsOfExcel != null && detailsOfExcel.size() > 0) {
                for (int i=0; i<detailsOfExcel.size(); i++) {
                    processNullDataId(detailsOfExcel.get(i));
                }
            }
        }
    }

    /**
     * 更新保存前
     */
    public void beforeUpdate(Request request, EventEx ex) {
        log.info("更新保存前");
        Map<String, List<Map>> details = request.detailData;
        if (details.containsKey("JTGKPAYPLANEXCEL")) {
            List<Map> detailsOfExcel = details.get("JTGKPAYPLANEXCEL");
            if (detailsOfExcel != null && detailsOfExcel.size() > 0) {
                for (int i=0; i<detailsOfExcel.size(); i++) {
                    processNullDataId(detailsOfExcel.get(i));
                }
            }
        }
    }

    /**
     * 从Excel导入的付款计划在保存前检查并处理空ID
     */
    private void processNullDataId(Map valMap) {
        log.info("数据处理前：{}", JSON.toJSONString(valMap));
        // 公司代码
        String payUnitId = (String) valMap.get("PAYUNITID");
        if (StringUtil.isNullOrEmpty(payUnitId)) {
            String payUnitCode = (String) valMap.get("PAYUNITCODE");
            if (StringUtil.isNullOrEmpty(payUnitCode)) {
                log.error("单位代码不能为空");
                throw new JfskException("goldwind", "JfskPayBillExcelImportEvent-101", "单位代码不能为空", null);
            } else {
                String selectPayUnit = "select ID,CODE,NAME_CHS from BFADMINORGANIZATION where ORGTYPE='9edf25d1-b991-4dde-90b9-30145422d24d' and CODE=?1";
                log.info(selectPayUnit + ", ?1=" + payUnitCode);
                List<Map<String, Object>> rowsOfPayUnit = DBUtil.querySql(selectPayUnit, payUnitCode);
                log.info(JSON.toJSONString(rowsOfPayUnit));
                if (rowsOfPayUnit == null || rowsOfPayUnit.size() == 0) {
                    log.error("单位代码" + payUnitCode + "无效");
                    throw new JfskException("goldwind", "JfskPayBillExcelImportEvent-102", "单位代码" + payUnitCode + "无效", null);
                } else {
                    payUnitId = (String) rowsOfPayUnit.get(0).get("ID");
                    valMap.put("PAYUNITID", payUnitId);
                }
            }
        }
        // 供应商编码
        String partnerId = (String) valMap.get("RECEIVINGUNITID");
        if (StringUtil.isNullOrEmpty(partnerId)) {
            String partnerCode = (String) valMap.get("RECEIVINGUNITCODE");
            if (StringUtil.isNullOrEmpty(partnerCode)) {
                log.error("供应商编码不能为空");
                throw new JfskException("goldwind", "JfskPayBillExcelImportEvent-103", "供应商编码不能为空", null);
            } else {
                String selectOfPartner = "select ID,CODE,NAME_CHS from BFPARTNER where STATE_ISENABLED='1' AND CODE=?1";
                log.info(selectOfPartner + ", ?1=" + partnerCode);
                List<Map<String, Object>> rowsOfPartner = DBUtil.querySql(selectOfPartner, partnerCode);
                log.info(JSON.toJSONString(rowsOfPartner));
                if (rowsOfPartner == null || rowsOfPartner.size() == 0) {
                    log.error("供应商编码" + partnerCode + "无效");
                    throw new JfskException("goldwind", "JfskPayBillExcelImportEvent-104", "供应商编码" + partnerCode + "不能为空", null);
                } else {
                    partnerId = (String) rowsOfPartner.get(0).get("ID");
                    valMap.put("RECEIVINGUNITID", partnerId);
                }
            }
        }
        // 币种
        String currencyId = (String) valMap.get("CURRENCYID");
        if (StringUtil.isNullOrEmpty(currencyId)) {
            String currencyCode = (String) valMap.get("CURRENCYCODE");
            if (StringUtil.isNullOrEmpty(currencyCode)) {
                // 默认人民币
                valMap.put("CURRENCYID", "6a4b352a-f5c7-56c6-dfa8-ed501a87d98e");
                valMap.put("CURRENCYCODE", "CNY");
            } else {
                String selectCurrency = "select ID,CODE,NAME_CHS from BFCURRENCY where STATE_ISENABLED='1' AND CODE=?1";
                log.info(selectCurrency + ", ?1=" + currencyCode);
                List<Map<String, Object>> rowsOfCurrency = DBUtil.querySql(selectCurrency, currencyCode);
                log.info(JSON.toJSONString(rowsOfCurrency));
                if (rowsOfCurrency == null || rowsOfCurrency.size() == 0) {
                    log.error("币种无效：" + currencyCode);
                    throw new JfskException("goldwind", "JfskPayBillExcelImportEvent-105", "币种无效：" + currencyCode, null);
                } else {
                    currencyId = (String) rowsOfCurrency.get(0).get("ID");
                    valMap.put("CURRENCYID", currencyId);
                }
            }
        }
        // 实际支付金额
        Object planPayAmount = valMap.get("TOTALAMOUNT");
        // 已分配金额、未分配金额
        valMap.put("AMT02", BigDecimal.ZERO);
        valMap.put("AMT03", planPayAmount);
        log.info("数据处理完成：{}", JSON.toJSONString(valMap));
    }

    /**
     * 新增保存后
     */
    @Override
    public void afterAdd(Request request, EventEx ex) {
        log.info("新增保存后事件");
        Map mainData = request.mainData.getValue();
        String docId = mainData.get("ID").toString();
        String dataSrc = mainData.get("DATASRC").toString();
        log.info("id=" + docId + ", dataSrc=" + dataSrc);
        R result = null;
        if ("0".equals(dataSrc)) {
            JpaTransaction transaction = JpaTransaction.getTransaction();
            try {
                transaction.begin();
                result = autoMatchPlanDetail(docId);
                transaction.commit();
            } catch (Throwable e){
                transaction.rollback();
                log.error("付款计划自动匹配支付明细发生异常：", e);
                result = R.error(ExceptionUtils.getStackTrace(e));
            }
            if (!result.getResult()) {
                ex.state = FilterState.WARN;
                ex.message = result.getMessage();
            }
        }
    }

    /**
     * 修改保存后
     */
    @Override
    public void afterUpdate(Request request, EventEx ex) {
        log.info("修改保存后事件");
        Map mainData = request.mainData.getValue();
        String docId = mainData.get("ID").toString();
        String dataSrc = mainData.get("DATASRC").toString();
        log.info("id=" + docId + ", dataSrc=" + dataSrc);
        if ("0".equals(dataSrc)) {
            R result = null;
            JpaTransaction transaction = JpaTransaction.getTransaction();
            try {
                transaction.begin();
                result = autoMatchPlanDetail(docId);
                transaction.commit();
            } catch (Throwable e){
                transaction.rollback();
                log.error("付款计划自动匹配支付明细发生异常：", e);
                result = R.error(ExceptionUtils.getStackTrace(e));
            }
            if (!result.getResult()) {
                ex.state = FilterState.WARN;
                ex.message = result.getMessage();
            }
        }
    }

    /**
     * Excel导入付款计划在保存后重新匹配支付明细
     * @param docId 付款安排表ID
     */
    public R autoMatchPlanDetail(String docId) {
        log.info("Excel导入付款计划在保存后重新匹配支付明细：id=" + docId);
        JfskPayPlanExcelRepository planExcelRepository = SpringBeanUtils.getBean(JfskPayPlanExcelRepository.class);
        JfskPayPlanService payPlanService = SpringBeanUtils.getBean(JfskPayPlanService.class);
        JfskPayPlanDetailRepository detailRepository = SpringBeanUtils.getBean(JfskPayPlanDetailRepository.class);
        List<JfskPayPlanExcelEntity> planDtos = null;
        List<JfskPayPlanDetailEntity> detailDtos = null;
        BigDecimal totalAmount = BigDecimal.ZERO;
//        try {
            String selectSql = "select JTGKPAYPLANBILL.DOCNO,SRCPAYMETHOD.NAME,SRCPAYMETHOD.TXT01,JTGKPAYPLANBILL.SETTLEMENTWAYID\n" +
                    "from JTGKPAYPLANBILL\n" +
                    "LEFT JOIN IDD_DATADICTIONARY SRCPAYMETHOD ON JTGKPAYPLANBILL.TXT01=SRCPAYMETHOD.ID and SRCPAYMETHOD.CATEGORYID='3a6b4bb2-2548-6bf3-c17b-3ce189e2081c'\n" +
                    "where JTGKPAYPLANBILL.ID='" + docId + "'";
            log.info(selectSql);
            List<Map<String, Object>> rowsOfBill = DBUtil.querySql(selectSql);
            log.info(JSON.toJSONString(rowsOfBill));
            String docNo = (String) rowsOfBill.get(0).get("DOCNO");
            String srcPayMethodCode = (String) rowsOfBill.get(0).get("NAME");
            String srcPayMethodName = (String) rowsOfBill.get(0).get("TXT01");
            String settlementwayId = (String) rowsOfBill.get(0).get("SETTLEMENTWAYID");
            planDtos = planExcelRepository.findAllByParentId(docId);
            if (planDtos == null || planDtos.isEmpty()) {
                return R.ok();
            }
            
            // 在调用autoMatchPlan之前删除JTGKPAYPLANDETAIL中parentId是docId的数据
            String predeleteSQL = "delete from JTGKPAYPLANDETAIL where PARENTID='" + docId + "'";
            log.info("预先删除支付明细: " + predeleteSQL);
            int preDeleteCount = DBUtil.executeWithoutTrans(predeleteSQL);
            log.info("预先删除支付明细受影响行数: " + preDeleteCount);
            
            detailDtos = payPlanService.autoMatchPlan(docId, docNo, srcPayMethodCode, srcPayMethodName, settlementwayId, planDtos);
            if (detailDtos == null || detailDtos.isEmpty() || CollectionUtils.isEmpty(detailDtos)) {
                throw new JfskException("goldwind", "JfskPayBillExcelImportEvent-106", "未找到可以匹配的付款安排明细", null);
            }
            totalAmount = detailDtos.stream().map(JfskPayPlanDetailEntity::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//        } catch (Throwable ex) {
//        }
//        try {
            String updateSql = "update JTGKPAYPLANBILL set TOTALAMOUNT=?1 where ID='" + docId + "'";
            log.info(updateSql + ", ?1=" + totalAmount.toPlainString());
            int countOfUpdate = DBUtil.executeWithoutTrans(updateSql, totalAmount);
            log.info("受影响行数：" + countOfUpdate);
            if (detailDtos != null && !detailDtos.isEmpty()) {
                detailRepository.saveAll(detailDtos);
            }
            planExcelRepository.saveAll(planDtos);
            return R.ok();
//        } catch (Throwable ex) {
//            return R.error(ex.getMessage());
//        }
    }
}
