package com.inspur.cloud.jtgk.goldwind.unipay.api;

import com.inspur.cloud.jtgk.goldwind.unipay.dto.AssignBillsParam;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.R;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.RD;
import com.inspur.gs.tm.tmfnd.fsjspub.api.dto.FsspResultRet;
import org.springframework.web.bind.annotation.RequestBody;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;
import java.util.Map;

/**
 * 付款安排表
 */
@Path("/")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
public interface JfskPayBillApi {
    /**
     * 内部接口：获取单据状态
     * @param docId 安排单据ID
     * @return 单据状态 { result: true, message: null, data: 0 }
     */
    @GET
    @Path("/docStatus")
    String getDocStatus(@QueryParam("ID") String docId);

    /**
     * 内部接口：获取帮助内容
     * @param name 帮助内容
     * @return 帮助相关内容
     */
    @GET
    @Path("/getHelpInfos")
    RD<Map<String, Object>> getHelpInfos(@QueryParam("NAME") String name);

    /**
     * 内部接口：查询某一支付明细的默认付款账户
     * @param payPlanDetail 支付明细
     * @return 查询结果
     */
    @POST
    @Path("defaultPayAccount")
    RD<Map<String, Object>> getDefaultAccount(Map<String, Object> payPlanDetail);

    /**
     * 内部接口：付款安排提交
     * @param docId 安排单据ID
     * @return 操作结果 { result: true, message: null }
     */
    @GET
    @Path("/submit")
    R submit(@QueryParam("ID") String docId);

    /**
     * 内部接口：付款安排保存前校验 - 再次校验不要超过待付池的剩余金额
     * @param checkInfos 待检查信息
     * @return 操作结果 { result: true, message: null }
     */
    @POST
    @Path("/save")
    R save(Map<String, Object> checkInfos);

    /**
     * 内部接口：付款安排复核提交（新）
     * @param maps 待提交信息
     * @return 操作结果 { result: true, message: null }
     */
    @POST
    @Path("/submit2New")
    FsspResultRet submit2New(@RequestBody Map<String, Object> maps);


    /**
     * 内部接口：付款安排复核撤回（新）
     * @param maps 待提交信息
     * @return 操作结果 { result: true, message: null }
     */
    @POST
    @Path("/retract2New")
    FsspResultRet retract2New(@RequestBody Map<String, Object> maps);


    /**
     * 内部接口：付款安排复核查看流程（新）
     * @param maps 待提交信息
     * @return 操作结果 { result: true, message: null }
     */
    @POST
    @Path("/view2New")
    FsspResultRet view2New(@RequestBody Map<String, Object> maps);



    /**
     * 内部接口：票据补录核提交（新）
     * @param maps 待提交信息
     * @return 操作结果 { result: true, message: null }
     */
    @POST
    @Path("/submit3New")
    FsspResultRet submit3New(@RequestBody Map<String, Object> maps);


    /**
     * 内部接口：查看流程（新）
     * @param maps 待提交信息
     * @return 操作结果 { result: true, message: null }
     */
    @POST
    @Path("/view3New")
    FsspResultRet view3New(@RequestBody Map<String, Object> maps);


    /**
     * 内部接口：获取复核状态
     * @param docId 复核单据ID
     * @return 复核状态 { result: true, message: null, data: 0 }
     */
    @GET
    @Path("/detailStatus")
    String getDetailStatus(@QueryParam("DOCID") String docId);

    /**
     * 内部接口：复核通过
     * @param docId 复核单据ID
     * @param docNo 单据编号
     * @return 处理结果 { result: true, message: null }
     */
    @GET
    @Path("/checkPass")
    R checkPass(@QueryParam("DOCID") String docId, @QueryParam("DOCNO") String docNo, @QueryParam("ONLYCHECK") String onlyCheck);

    /**
     * 内部接口：复核退回
     * @param docId 复核单据ID
     * @param docNo 单据编号
     * @return 处理结果
     */
    @GET
    @Path("/checkBack")
    R checkBack(@QueryParam("DOCID") String docId, @QueryParam("DOCNO") String docNo);

    /**
     * 内部接口：票据补录退回
     * @param docId 复核单据ID
     * @param docNo 单据编号
     * @return 处理结果
     */
    @GET
    @Path("/checkBillBack")
    R checkBillBack(@QueryParam("DOCID") String docId, @QueryParam("DOCNO") String docNo);

    /**
     * 内部接口：分配背书票据
     * @param args { docId: 背书补录单ID, billIds: [票据ID] }
     * @return 分配结果
     */
    @POST
    @Path("/assignBills")
    String assignBills(AssignBillsParam args);

    /**
     * 内部接口：获取票据背书办理状态
     * @param docId 票据背书办理ID
     * @return 办理状态 { result: true, message: null, data: 0 }
     */
    @GET
    @Path("/handleStatus")
    String getHandleStatus(@QueryParam("DOCID") String docId);

    /**
     * 内部接口：票据背书办理完成
     * @param docId 票据背书办理ID
     * @param docNo 单据编号
     * @return 处理结果 { result: true, message: null }
     */
    @GET
    @Path("/handlePass")
    R handlePass(@QueryParam("DOCID") String docId, @QueryParam("DOCNO") String docNo);

    /**
     * 内部接口：批量复核通过
     * @param batchCheckRequest 批量复核请求参数
     * @return 批量处理结果 { result: true, message: null, data: [处理结果列表] }
     */
    @POST
    @Path("/batchCheckPass")
    RD<List<Map<String, Object>>> batchCheckPass(@RequestBody Map<String, Object> batchCheckRequest);

}
