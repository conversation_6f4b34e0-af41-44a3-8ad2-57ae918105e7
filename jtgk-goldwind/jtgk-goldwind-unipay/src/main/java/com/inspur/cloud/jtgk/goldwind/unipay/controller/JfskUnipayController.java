package com.inspur.cloud.jtgk.goldwind.unipay.controller;

import com.alibaba.fastjson.JSONObject;
import com.inspur.cloud.jtgk.goldwind.unipay.api.JfskUnipayApi;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.*;
import com.inspur.cloud.jtgk.goldwind.unipay.service.JfskPaymentAutoDealService;
import com.inspur.cloud.jtgk.goldwind.unipay.service.JfskUnipayResultService;
import com.inspur.cloud.jtgk.goldwind.unipay.service.JfskUnipayService;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.CallbackUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.JfskException;
import com.inspur.fastdweb.model.qry.Qry;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.lockservice.api.*;
import io.iec.edp.caf.rpc.api.service.RpcClient;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import com.alibaba.fastjson.JSON;
import org.springframework.web.bind.annotation.RequestBody;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;

/**
 * 待付池
 */
@Controller
@Slf4j
public class JfskUnipayController implements JfskUnipayApi {
    @Autowired
    private LogService logService;
    @Autowired
    private ILockService lockService;
    @Autowired
    private JfskUnipayService unipayService;
    @Autowired
    private JfskUnipayResultService unipayResultService;
    @Autowired
    private JfskPaymentAutoDealService paymentAutoDealService;
    @Autowired
    private JtgkSettlementConfirmTaskService jtgkSettlementConfirmTaskService;
    @Autowired
    private JfskReceivableBillSapPushScheduler jfskReceivableBillSapPushScheduler;

    /**
     * 外部接口：待付池数据接收接口
     * @param args 付款申请信息
     * @return 司库系统是否处理成功
     */
    @Override
    public String request(String args) {
        log.info("付款申请接口入参：{}", args);
        RD<PaymentRequestDto> checkResult = check(args);
        if (!checkResult.getResult()) {
            return new PaymentResponseDto(null, checkResult.getMessage()).toString();
        }
        PaymentRequestDto request = checkResult.getData();

        logService.init("K0101");
        String groupId;
        try {
            String moduleId = "goldwind";
            String funcId = "request";
            String categoryId = "JfskUnipayController";
            List<String> datas = request.getDetails().stream().map(PaymentInfoDto::getSrcDocId).collect(Collectors.toList());
            String comment = "待付池付款申请接口";
            BatchLockResult lockResult = lockService.addBatchLock(moduleId, categoryId, datas, null, Duration.ofMinutes(30), funcId, comment);
            if (lockResult == null || !lockResult.isSuccess()) {
                logService.error(request.getRequestId(), "付款申请接口加锁失败");
                logService.flush();
                log.error("付款申请接口加锁失败");
                return new PaymentResponseDto(request.getRequestId(), "付款申请接口加锁失败").toString();
            }
            groupId = lockResult.getGroupId();
            log.info("付款申请接口加锁结果：groupId=" + groupId);
        } catch (Throwable e) {
            logService.error(request.getRequestId(), "付款申请接口加锁过程发生异常：" + ExceptionUtils.getStackTrace(e));
            logService.flush();
            log.error("付款申请接口加锁过程发生异常:", e);
            return new PaymentResponseDto(request.getRequestId(), "付款申请接口加锁过程发生异常").toString();
        }

        logService.info(request.getRequestId(), "付款申请接口入参：" + args);
        String result;
        try {
            result = unipayService.request(logService, request);
            log.info("付款申请处理结果：" + result);
            logService.info(request.getRequestId(), "付款申请处理结果：" + result);
        } catch (Throwable ex) {
            log.error("付款申请接口处理过程发生异常：", ex);
            logService.error(request.getRequestId(), "付款申请接口处理过程发生异常：" + ExceptionUtils.getStackTrace(ex));
            PaymentResponseDto resp = new PaymentResponseDto(request.getRequestId(), "付款申请接口处理过程发生异常：" + ex.getMessage());
            result = JSON.toJSONString(resp);
        }

        try {
            lockService.removeBatchLock(groupId);
            log.info("付款申请接口已解锁");
        } catch (Throwable e) {
            log.error("付款申请接口解锁过程发生异常：", e);
            logService.error(request.getRequestId(),"付款申请接口解锁过程发生异常：" + ExceptionUtils.getStackTrace(e));
        }

        log.info("付款申请接口处理结果：{}", result);
        logService.info(request.getRequestId(), "付款申请接口处理结果：" + result);
        logService.flush();
        return result;
    }


    /**
     * 外部接口：汇联易
     * @param args 汇联易请求信息
     * @return 司库处理完成信息
     */
    public Map<String, Object> oaVerification(@RequestBody Map<String, Object> args){
        LogService logService = SpringBeanUtils.getBean(LogService.class);
        logService.init("oaVerification");
        String requestId = "HLY" + System.currentTimeMillis();
        
        Map<String, Object> result = new HashMap<>();
        logService.info(requestId, "汇联易接口调用开始，入参：{}", JSON.toJSONString(args));
        
        try{
            // 把args转为JSONObject
            JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(args));
            String userInfo = jsonObject.getString("userInfo");
            // String companyIdOrTenantId = jsonObject.getString("tenantId");
            // logService.info(requestId, "解析入参：message={}, tenantId={}", message, companyIdOrTenantId);

            // CallbackUtil callbackUtil = CallbackUtil.init("d2IolxVAkVH6Hnt1CthmS2", "goldwind", companyIdOrTenantId);
            // logService.info(requestId, "初始化CallbackUtil完成");

            // String decryptMSg = callbackUtil.decryptMSg(jsonObject.getString("signature"), jsonObject.getString("timestamp"), jsonObject.getString("nonce"), message);
            // logService.info(requestId, "消息解密结果：{}", decryptMSg);

            // if (decryptMSg != null) {
                // JSONObject jsonObject1 = JSONObject.parseObject(decryptMSg);
                // String username = jsonObject1.getString("username");
                // logService.info(requestId, "获取用户名：{}", username);

            if (StringUtils.isBlank(userInfo)) {
                logService.warn(requestId, "入参错误：{}", userInfo);
                result.put("code", "ERROR");
                result.put("message", "校验失败");
                return result;
            }

            // 对userInfo进行AES-256解密，获取原始userId
            String decryptedUserId = decryptUserInfo(requestId, userInfo);
            if (decryptedUserId == null) {
                logService.warn(requestId, "用户信息解密失败：{}", userInfo);
                result.put("code", "ERROR");
                result.put("message", "校验失败");
                return result;
            }

            logService.info(requestId, "用户信息解密成功，原始userId：{}", decryptedUserId);

                String querySql = "select CODE from GSPUSER where ID = ?1";
                logService.info(requestId, "执行SQL：{}，参数：{}", querySql, decryptedUserId);

                List<Map<String, Object>> rows = DBUtil.querySql(querySql, decryptedUserId);
                logService.info(requestId, "查询结果：{}", JSON.toJSONString(rows));

                if (rows == null || rows.isEmpty()) {
                    logService.warn(requestId, "用户名不存在：{}", decryptedUserId);
                    result.put("code", "ERROR");
                    result.put("message", "校验失败");
                } else {
                    logService.info(requestId, "用户校验成功：{}", rows.get(0).get("CODE"));
                    Map<String, String> body = new HashMap<>();
                    body.put("employeeId", (String)rows.get(0).get("CODE"));
                    result.put("body", body);
                    result.put("code", "SUCCESS");
                    result.put("message", "OK");
                }
            // } else {
            //     logService.warn(requestId, "消息解密失败");
            //     result.put("CODE", "ERROR");
            //     result.put("MESSAGE", "消息解密失败");
            // }
        } catch(Throwable ex){
            logService.error(requestId, "汇联易处理过程发生异常：" + ExceptionUtils.getStackTrace(ex));
            log.error("汇联易处理过程发生异常：", ex);
            result.put("code", "ERROR");
            result.put("message","校验失败");
        }
        
        logService.info(requestId, "汇联易接口调用结束，返回结果：{}", JSON.toJSONString(result));
        logService.flush();
        return result;
    }


    public static RD<PaymentRequestDto> check(String args) {
        if (StringUtil.isNullOrEmpty(args)) {
            log.error("入参不能为空");
            return RD.error("入参不能为空");
        }
        PaymentRequestDto request;
        try {
            request = JSON.parseObject(args, PaymentRequestDto.class);
        } catch (Throwable ex) {
            log.error("入参解析发生异常：", ex);
            return RD.error("入参解析发生异常：" + ex.getMessage());
        }
        if (request == null) {
            log.error("入参格式错误");
            return RD.error("入参格式错误");
        }
        if (StringUtil.isNullOrEmpty(request.getRequestId())) {
            log.error("请求流水号不能为空");
            return RD.error("请求流水号不能为空");
        }
        if (request.getDetails() == null || request.getDetails().isEmpty()) {
            log.error("付款申请明细不能为空");
            return RD.error("付款申请明细不能为空");
        }
        if (request.getDetails().size() >= 50) {
            log.error("付款申请明细超数量：" + request.getDetails().size());
            return RD.error("付款申请明细超数量");
        }
        long countOfNullSrcDocId = request.getDetails().stream()
                .filter(detail -> (StringUtil.isNullOrEmpty(detail.getSrcBizSys()) || StringUtil.isNullOrEmpty(detail.getSrcDocId()) || StringUtil.isNullOrEmpty(detail.getSrcDocNo())))
                .count();
        if (countOfNullSrcDocId > 0) {
            return RD.error("来源系统标识、来源单据内码、来源单据编号不能为空");
        }
        long countOfRepeatSrcDocId = request.getDetails().stream().collect(Collectors.groupingBy(o -> o.getSrcBizSys() + "-" + o.getSrcDocId()))
                .entrySet().stream().filter(e -> e.getValue().size() > 1).count();
        if (countOfRepeatSrcDocId > 0) {
            return RD.error("来源单据内码不允许重复");
        }
        return RD.ok(null, request);
    }

    /**
     * 内部接口：获取异构系统标识
     * @return 异构系统标识{"code":"FSSC","name":"共享系统"}
     */
    @Override
    public String getBizSys() {
        log.info("准备查询付款申请接口来源系统标识");
        String selectSql = "select CODE,NAME from IDD_DATADICTIONARY where CATEGORYID='25c76693-a046-de5a-c474-49a8af20f2a8' order by TREE_PATH";
        log.info(selectSql);
        List<Map<String, Object>> rowsOfBizSys = DBUtil.querySql(selectSql);
        RD result = new RD();
        if (rowsOfBizSys == null || rowsOfBizSys.isEmpty()) {
            log.error("未从数据库中获取到来源系统标识");
            result.setResult(false);
            result.setMessage("未从数据库中获取到来源系统标识");
        } else {
            log.info("来源系统标识：" + JSON.toJSONString(rowsOfBizSys));
            result.setResult(true);
            result.setData(rowsOfBizSys);
        }
        return result.toString();
    }

    /**
     * 内部接口：查询待安排的付款申请
     * ids 英文逗号分隔的接口日志表ID
     * @return 付款申请信息
     */
    @Override
    public String getUnpayList(String ids) {
        log.info("查询待安排的付款申请：" + ids);
        String selectSql;
        try {
            LinkedHashMap<String, Object> rpcGetQryPams = new LinkedHashMap<>();
            rpcGetQryPams.put("qryId", "ee9d8ca8-a1ca-0330-1cf9-6b4fa212cb6c");
            Qry qryEntity;
            try {
                RpcClient rpcClient = SpringBeanUtils.getBean(RpcClient.class);
                qryEntity = rpcClient.invoke(Qry.class, "idp.rpc.common.data.service.getQry", "Idp", rpcGetQryPams, null);
            } catch (Throwable ex) {
                log.error("没有找到指定的IDP查询");
                return R.error("没有找到指定的IDP查询").toString();
            }
            selectSql = qryEntity.qSql + " and JTGKPAYMENTINFO.DOCSTATUS=1 and JTGKPAYMENTINFO.ID in ('" + ids.replace(",", "','") + "')";
            log.info(selectSql);
        } catch (Throwable ex) {
            log.error("获取取数SQL时发生异常：", ex);
            return R.error("获取取数SQL时发生异常" + ex.getMessage()).toString();
        }
        List<Map<String, Object>> rowsOfRequest = DBUtil.querySql(selectSql);

        // 新增内容
        for (Map<String, Object> row : rowsOfRequest) {
            String CSSFLD = "0";
            String payUnitId = (String)row.get("PAYUNITID");
            if (StringUtils.isNotBlank(payUnitId)){
                String querySql = "select 1 from IDD_DATADICTIONARY where CATEGORYID = '87908b6c-48c3-96bb-63a9-2f507fd3870f' and CODE = ?1";
                List<Map<String, Object>> rows = DBUtil.querySql(querySql, payUnitId);
                if (rows != null && rows.size() > 0){
                    CSSFLD = "1";
                }
            }
            row.put("TXT04", CSSFLD);
        }

        log.info(JSON.toJSONString(rowsOfRequest));
        String resp = RD.ok(null, rowsOfRequest).toString();
        return resp;
    }

    /**
     * 外部接口：付款申请撤回到业务系统
     * @param args 撤回申请信息
     * @return 司库系统是否处理成功
     */
    @Override
    public String rollbackRequest(String args) {
        throw new JfskException("未实现的方法");
    }

    /**
     * 内部接口：获取共享系统组合支付的拆分页面地址
     * @param id 付款申请ID
     * @return 查询结果
     */
    @Override
    public RD<String> getFsscSplitUrl(String id) {
        throw new JfskException("未实现的方法");
    }

    /**
     * 获取认证code
     */
    private String getAuthCode(String clientId, String clientSecret, String apiUrl, String username, String password) {
        LogService logService = SpringBeanUtils.getBean(LogService.class);
        logService.init("getAuthCode");
        String billCode = clientId; // 使用clientId作为日志标识
        
        try {
            logService.info(billCode, "开始获取认证code，请求URL：{}", apiUrl);
            URL url = new URL(apiUrl);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "application/json");

            // 添加BASIC认证
            String auth = username + ":" + password;
            String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));
            conn.setRequestProperty("Authorization", "BASIC " + encodedAuth);
            logService.info(billCode, "设置BASIC认证头，username：{}", username);

            // 发送请求参数
            conn.setDoOutput(true);
            JSONObject jsonParam = new JSONObject();
            jsonParam.put("clientId", clientId);
            jsonParam.put("clientSecret", clientSecret);
            logService.info(billCode, "请求参数：{}", jsonParam.toJSONString());

            try (OutputStream os = conn.getOutputStream()) {
                byte[] input = jsonParam.toString().getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            // 读取响应
            int responseCode = conn.getResponseCode();
            logService.info(billCode, "HTTP响应码：{}", responseCode);
            
            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
                    StringBuilder response = new StringBuilder();
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        response.append(responseLine.trim());
                    }
                    
                    String responseStr = response.toString();
                    logService.info(billCode, "认证接口返回完整内容：{}", responseStr);

                    JSONObject jsonResponse = JSON.parseObject(responseStr);
                    if (jsonResponse.getBoolean("success")) {
                        String code = jsonResponse.getString("data");
                        logService.info(billCode, "成功获取认证code：{}", code);
                        logService.flush();
                        return code;
                    } else {
                        String errorMsg = jsonResponse.getString("message");
                        logService.error(billCode, "获取code失败：{}，完整响应：{}", errorMsg, responseStr);
                        logService.flush();
                        return null;
                    }
                }
            } else {
                // 读取错误响应
                String errorResponse = "";
                if (conn.getErrorStream() != null) {
                    try (BufferedReader br = new BufferedReader(
                            new InputStreamReader(conn.getErrorStream(), StandardCharsets.UTF_8))) {
                        errorResponse = br.lines().collect(Collectors.joining());
                    }
                }
                logService.error(billCode, "HTTP请求失败，响应码：{}，错误内容：{}", responseCode, errorResponse);
                logService.flush();
                return null;
            }
        } catch (Exception e) {
            logService.error(billCode, "获取认证code异常：" + ExceptionUtils.getStackTrace(e));
            logService.flush();
            return null;
        }
    }
    
    /**
     * AES-256加密用户信息
     * 加密内容：userId + "|" + 时间戳
     */
    private String encryptUserInfo(String userId) {
        LogService logService = SpringBeanUtils.getBean(LogService.class);
        logService.init("encryptUserInfo");
        String billCode = "encryptUserInfo";

        try {
            logService.debug(billCode, "开始AES-256加密用户信息，userId：{}", userId);

            // 生成时间戳
            long timestamp = System.currentTimeMillis();
            String plainText = userId + "|" + timestamp;

            // 使用固定32字节密钥（AES-256）
            String key = "JtgkGoldwindAES256Key123456789AB"; // 32字节密钥

            // 创建AES加密器，使用ECB模式和PKCS5Padding
            AES aes = new AES(Mode.ECB, Padding.PKCS5Padding, key.getBytes(CharsetUtil.CHARSET_UTF_8));

            // 加密并转为Base64
            String encryptedText = aes.encryptBase64(plainText, CharsetUtil.CHARSET_UTF_8);

            logService.info(billCode, "AES-256加密成功");
            logService.flush();
            return encryptedText;
        } catch (Exception e) {
            logService.error(billCode, "AES-256加密异常：" + ExceptionUtils.getStackTrace(e));
            logService.flush();
            // 加密失败时返回原始userId（向后兼容）
            return userId;
        }
    }

    /**
     * AES-256解密用户信息
     * 解密内容：userId + "|" + 时间戳
     */
    private String decryptUserInfo(String requestId, String encryptedInfo) {
        LogService logService = SpringBeanUtils.getBean(LogService.class);
//        logService.init("decryptUserInfo");
//        String billCode = "decryptUserInfo";

        try {
            logService.debug(requestId, "开始AES-256解密用户信息");

            // 使用相同的32字节密钥
            String key = "JtgkGoldwindAES256Key123456789AB"; // 32字节密钥

            // 创建AES解密器
            AES aes = new AES(Mode.ECB, Padding.PKCS5Padding, key.getBytes(CharsetUtil.CHARSET_UTF_8));

            // 解密
            String decryptedText = aes.decryptStr(encryptedInfo, CharsetUtil.CHARSET_UTF_8);

            // 解析userId和时间戳
            String[] parts = decryptedText.split("\\|");
            if (parts.length != 2) {
                logService.error(requestId, "解密后的数据格式不正确");
                logService.flush();
                return null;
            }

            String userId = parts[0];
            long timestamp = Long.parseLong(parts[1]);

            // 检查时间戳有效性（1小时内有效）
            long currentTime = System.currentTimeMillis();
            long timeDiff = Math.abs(currentTime - timestamp); // 使用绝对值处理时间差
            if (timeDiff > 60 * 60 * 1000) { // 1小时
                logService.warn(requestId, "时间戳已过期，时间差：{}ms", timeDiff);
                logService.flush();
                return null;
            }

            logService.info(requestId, "AES-256解密成功，userId：{}", userId);
            logService.flush();
            return userId;
        } catch (Exception e) {
            logService.error(requestId, "AES-256解密异常：" + ExceptionUtils.getStackTrace(e));
            logService.flush();
            return null;
        }
    }

    /**
     * MD5加密
     */
    private String md5(String input) {
        LogService logService = SpringBeanUtils.getBean(LogService.class);
        logService.init("md5");
        String billCode = "md5"; // 使用md5作为日志标识
        
        try {
            logService.debug(billCode, "开始MD5加密，输入字符串：{}", input);
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 按照示例代码，不指定编码，使用系统默认编码
            byte[] messageDigest = md.digest(input.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            String result = hexString.toString();
            logService.info(billCode, "MD5加密成功，结果：{}", result);
            logService.flush();
            return result;
        } catch (Exception e) {
            logService.error(billCode, "MD5加密异常：" + ExceptionUtils.getStackTrace(e));
            logService.flush();
            return null;
        }
    }
    
    /**
     * 内部接口：获取现汇付款单拆分页面跳转URL
     * @param  billInfo 包含必要信息的Map对象
     * @return 跳转URL
     */
    public RD<String> obtainSplitURL(Map<String, Object> billInfo){
        LogService logService = SpringBeanUtils.getBean(LogService.class);
        logService.init("obtainSplitURL");
        
        String billCode = billInfo != null ? (String) billInfo.get("SRC_DOC_ID") : "unknown";
        
        logService.info(billCode, "开始获取现汇付款单拆分页面跳转URL，参数：{}", JSON.toJSONString(billInfo));

        String selectSql = "select TXT01, TXT02, TXT03, TXT04, BIGTXT01, BIGTXT02 from IDD_DATADICTIONARY where CATEGORYID='b029742e-323d-4f00-3510-dfcdb91e0afd' and TREE_PATH='0001'";
        logService.info(billCode, "执行SQL：{}", selectSql);
        List<Map<String, Object>> rowsOfBizSys = DBUtil.querySql(selectSql);
        logService.info(billCode, "查询结果：{}", JSON.toJSONString(rowsOfBizSys));
        if (rowsOfBizSys == null || rowsOfBizSys.isEmpty()) {
            logService.error(billCode, "未找到业务系统配置");
            logService.flush();
            return RD.error("未找到业务系统配置");
        }

        // 环境配置
        String clientId = rowsOfBizSys.get(0).get("TXT01").toString();
        String clientSecret = rowsOfBizSys.get(0).get("TXT02").toString();
        String apiUrl = rowsOfBizSys.get(0).get("TXT03").toString();
        String username = rowsOfBizSys.get(0).get("TXT04").toString();
        String password = rowsOfBizSys.get(0).get("BIGTXT01").toString();
        String ssoBaseUrl = rowsOfBizSys.get(0).get("BIGTXT02").toString();
        
        logService.info(billCode, "系统配置参数：clientId={}, apiUrl={}, username={}, ssoBaseUrl={}", clientId, apiUrl, username, ssoBaseUrl);

        try {
            // 参数校验和获取
            if (billInfo == null || billInfo.isEmpty()) {
                logService.error(billCode, "参数infos为空");
                logService.flush();
                return RD.error("参数不能为空");
            }

            String account = (String) billInfo.get("ACCOUNT"); // OA账号
            String srcDocId = (String) billInfo.get("SRC_DOC_ID"); // 来源单据ID
            
            logService.info(billCode, "获取参数：account={}, srcDocId={}", account, srcDocId);

            // 必要参数校验
            if (StringUtil.isNullOrEmpty(account)) {
                logService.error(billCode, "OA账号为空");
                logService.flush();
                return RD.error("OA账号不能为空");
            }else{
                String userQuerySQL = "select CODE from GSPUSER where id = ?1";
                logService.info(billCode, "查询用户CODE，SQL：{}, 参数：{}", userQuerySQL, account);
                List<Map<String, Object>> rowOfUser = DBUtil.querySql(userQuerySQL, account);
                logService.info(billCode, "查询用户结果：{}", JSON.toJSONString(rowOfUser));
                if (rowOfUser == null || rowOfUser.isEmpty()) {
                    logService.error(billCode, "OA账号不存在");
                    logService.flush();
                    return RD.error("OA账号不存在");
                }else{
                    account = rowOfUser.get(0).get("CODE").toString();
                    logService.info(billCode, "获取到用户CODE：{}", account);
                }
            }
            if (StringUtil.isNullOrEmpty(srcDocId)) {
                logService.error(billCode, "来源单据ID为空");
                logService.flush();
                return RD.error("来源单据ID不能为空");
            }else{
                if (StringUtils.contains(srcDocId, "/")) {
                    srcDocId = StringUtils.substringBefore(srcDocId, "/");
                }
            }

            // 1. 获取code
            logService.info(billCode, "开始获取认证code，调用参数：clientId={}, apiUrl={}, username={}", clientId, apiUrl, username);
            String code = getAuthCode(clientId, clientSecret, apiUrl, username, password);
            if (StringUtil.isNullOrEmpty(code)) {
                logService.error(billCode, "获取认证code失败");
                logService.flush();
                return RD.error("获取认证code失败");
            }
            logService.info(billCode, "成功获取认证code: {}", code);

            // 2. 生成sign
            String signStr = clientId + clientSecret + account + code;
            logService.info(billCode, "准备生成签名，原始字符串：{}", signStr);
            String sign = md5(signStr);
            if (StringUtil.isNullOrEmpty(sign)) {
                logService.error(billCode, "MD5签名失败");
                logService.flush();
                return RD.error("生成签名失败");
            }
            logService.info(billCode, "成功生成签名: {}", sign);

            // 3. 拼接最终URL
            String finalUrl = ssoBaseUrl + srcDocId
                             + "?account=" + account
                             + "&code=" + code
                             + "&sign=" + sign;

            logService.info(billCode, "成功生成跳转URL: {}", finalUrl);
            logService.flush();
            return RD.ok("获取URL成功", finalUrl);

        } catch (Exception e) {
            logService.error(billCode, "获取跳转URL异常：" + ExceptionUtils.getStackTrace(e));
            logService.flush();
            return RD.error("获取跳转URL异常: " + e.getMessage());
        }
    }


    public RD<String> obtainFSSCURL(Map<String, Object> billInfo){
        LogService logService = SpringBeanUtils.getBean(LogService.class);
        logService.init("obtainSplitURL");

        String billCode = "linkURL";

        logService.info(billCode, "开始获取共享页面跳转URL，参数：{}", JSON.toJSONString(billInfo));

        String selectSql = "select TXT01, TXT02, TXT03, TXT04, BIGTXT01, BIGTXT02 from IDD_DATADICTIONARY where CATEGORYID='b029742e-323d-4f00-3510-dfcdb91e0afd' and TREE_PATH='0001'";
        logService.info(billCode, "执行SQL：{}", selectSql);
        List<Map<String, Object>> rowsOfBizSys = DBUtil.querySql(selectSql);
        logService.info(billCode, "查询结果：{}", JSON.toJSONString(rowsOfBizSys));
        if (rowsOfBizSys == null || rowsOfBizSys.isEmpty()) {
            logService.error(billCode, "未找到业务系统配置");
            logService.flush();
            return RD.error("未找到业务系统配置");
        }

        // 环境配置
        String clientId = rowsOfBizSys.get(0).get("TXT01").toString();
        String clientSecret = rowsOfBizSys.get(0).get("TXT02").toString();
        String apiUrl = rowsOfBizSys.get(0).get("TXT03").toString();
        String username = rowsOfBizSys.get(0).get("TXT04").toString();
        String password = rowsOfBizSys.get(0).get("BIGTXT01").toString();
        String ssoBaseUrl = (String) billInfo.getOrDefault("linkURL", "");

        logService.info(billCode, "系统配置参数：clientId={}, apiUrl={}, username={}, ssoBaseUrl={}", clientId, apiUrl, username, ssoBaseUrl);

        try {
            // 参数校验和获取
            if (billInfo == null || billInfo.isEmpty()) {
                logService.error(billCode, "参数infos为空");
                logService.flush();
                return RD.error("参数不能为空");
            }

            String account = (String) billInfo.get("ACCOUNT"); // OA账号
            String linkURL = (String) billInfo.get("linkURL"); // 来源单据ID

            logService.info(billCode, "获取参数：account={}, linkURL={}", account, linkURL);

            // 必要参数校验
            if (StringUtil.isNullOrEmpty(account)) {
                logService.error(billCode, "OA账号为空");
                logService.flush();
                return RD.error("OA账号不能为空");
            }else{
                String userQuerySQL = "select CODE from GSPUSER where id = ?1";
                logService.info(billCode, "查询用户CODE，SQL：{}, 参数：{}", userQuerySQL, account);
                List<Map<String, Object>> rowOfUser = DBUtil.querySql(userQuerySQL, account);
                logService.info(billCode, "查询用户结果：{}", JSON.toJSONString(rowOfUser));
                if (rowOfUser == null || rowOfUser.isEmpty()) {
                    logService.error(billCode, "OA账号不存在");
                    logService.flush();
                    return RD.error("OA账号不存在");
                }else{
                    account = rowOfUser.get(0).get("CODE").toString();
                    logService.info(billCode, "获取到用户CODE：{}", account);
                }
            }
            if (StringUtil.isNullOrEmpty(linkURL)) {
                logService.error(billCode, "联查地址为空");
                logService.flush();
                return RD.error("联查地址不能为空");
            }

            // 1. 获取code
            logService.info(billCode, "开始获取认证code，调用参数：clientId={}, apiUrl={}, username={}", clientId, apiUrl, username);
            String code = getAuthCode(clientId, clientSecret, apiUrl, username, password);
            if (StringUtil.isNullOrEmpty(code)) {
                logService.error(billCode, "获取认证code失败");
                logService.flush();
                return RD.error("获取认证code失败");
            }
            logService.info(billCode, "成功获取认证code: {}", code);

            // 2. 生成sign
            String signStr = clientId + clientSecret + account + code;
            logService.info(billCode, "准备生成签名，原始字符串：{}", signStr);
            String sign = md5(signStr);
            if (StringUtil.isNullOrEmpty(sign)) {
                logService.error(billCode, "MD5签名失败");
                logService.flush();
                return RD.error("生成签名失败");
            }
            logService.info(billCode, "成功生成签名: {}", sign);

            // 3. 拼接最终URL
            String finalUrl = ssoBaseUrl
                    + "?account=" + account
                    + "&code=" + code
                    + "&sign=" + sign;

            logService.info(billCode, "成功生成跳转URL: {}", finalUrl);
            logService.flush();
            return RD.ok("获取URL成功", finalUrl);

        } catch (Exception e) {
            logService.error(billCode, "获取跳转URL异常：" + ExceptionUtils.getStackTrace(e));
            logService.flush();
            return RD.error("获取跳转URL异常: " + e.getMessage());
        }
    }


    public RD<String> managerImage(Map<String, Object> billInfo){
        LogService logService = SpringBeanUtils.getBean(LogService.class);
        logService.init("managerImage");

        String billCode = "managerImage";

        logService.info(billCode, "开始获取待付池附件，参数：{}", JSON.toJSONString(billInfo));

        String billtype = (String) billInfo.get("BILLTYPE");
        List<String> IDs = (List<String>) billInfo.get("IDs");
        if (IDs == null || IDs.isEmpty()) {
            logService.error(billCode, "参数IDs为空");
            logService.flush();
            return RD.error("参数IDs不能为空");
        }
        String querySql = "";
        if (billtype.equals("JFPAYPLAN2")) {
            querySql = "select DOCNO, TXT06 from JTGKPAYPLANBILL2 where ID in ('" + StringUtils.join(IDs, "','") + "')";
        }else if (billtype.equals("JFPAYPLAN3")) {
            querySql = "select DOCNO, TXT06 from JTGKPAYPLANBILL3 where ID in ('" + StringUtils.join(IDs, "','") + "')";
        }
        logService.info(billCode, "执行SQL：{}", querySql);
        List<Map<String, Object>> rowsOfBill = DBUtil.querySql(querySql);
        logService.info(billCode, "查询结果：{}", JSON.toJSONString(rowsOfBill));

        if (rowsOfBill == null || rowsOfBill.isEmpty()) {
            logService.error(billCode, "未找到待付池附件");
            logService.flush();
            return RD.error("未找到待付池附件");
        }
        
        // 判断rowsOfBill中的TXT06是否有有不同的
        Set<String> imageNames = new HashSet<>();
        for (Map<String, Object> rowOfBill : rowsOfBill) {
            String imageName = rowOfBill.get("TXT06").toString();
            imageNames.add(imageName);
        }
        if (imageNames.size() > 1) {  
            logService.error(billCode, "存在不同的附件");
            logService.flush();
            return RD.error("存在不同的附件");
        }else if (imageNames.size() == 1 && !StringUtil.isNullOrEmpty(imageNames.iterator().next())) {
            String imageId = imageNames.iterator().next();
            // 获取所有rowsOfBill中的DOCNO，放入Set中
            Set<String> docNos = new HashSet<>();
            for (Map<String, Object> rowOfBill : rowsOfBill) {
                docNos.add(rowOfBill.get("DOCNO").toString());
            }
            // 遍历docNos
            for (String docNo : docNos) {
                String updateSql = "update JTGKPAYPLANBILL2 set TXT06='" + imageId + "' where DOCNO='" + docNo + "'";
                logService.info(billCode, "执行SQL：{}", updateSql);
                int count = DBUtil.executeUpdateSQL(updateSql);
                logService.info(billCode, "更新结果：{}", count);
            }
            return RD.ok("success", imageId);
        }else{
            // 创建UUID
            String imageId = UUID.randomUUID().toString();
            // 获取所有rowsOfBill中的DOCNO，放入Set中
            Set<String> docNos = new HashSet<>();
            for (Map<String, Object> rowOfBill : rowsOfBill) {
                docNos.add(rowOfBill.get("DOCNO").toString());
            }
            // 遍历docNos
            for (String docNo : docNos) {
                String updateSql = "update JTGKPAYPLANBILL2 set TXT06='" + imageId + "' where DOCNO='" + docNo + "'";
                logService.info(billCode, "执行SQL：{}", updateSql);
                int count = DBUtil.executeUpdateSQL(updateSql);
                logService.info(billCode, "更新结果：{}", count);
            }
            return RD.ok("success", imageId);
        }
        
        
    }


    public RD<String> managerImageSize(Map<String, Object> billInfo){
        LogService logService = SpringBeanUtils.getBean(LogService.class);
        logService.init("managerImageSize");

        String billCode = "managerImageSize";

        logService.info(billCode, "开始获取待付池附件大小，参数：{}", JSON.toJSONString(billInfo));
        
        List<String> IDs = (List<String>) billInfo.get("IDs");
        if (IDs == null || IDs.isEmpty()) {
            logService.error(billCode, "参数IDs为空");
            logService.flush();
            return RD.error("参数IDs不能为空");
        }
        
        for (String ID : IDs) {
            String querySql = "select JTGKPAYPLANBILL2.ID, IDD_DATADICTIONARY.NAME as NAME, JTGKPAYPLANBILL2.TXT06 as TXT06 from JTGKPAYPLANBILL2 LEFT JOIN IDD_DATADICTIONARY  ON JTGKPAYPLANBILL2.TXT01 = IDD_DATADICTIONARY.ID and IDD_DATADICTIONARY.CODE='SFS-PAY' and IDD_DATADICTIONARY.CATEGORYID='3a6b4bb2-2548-6bf3-c17b-3ce189e2081c' where JTGKPAYPLANBILL2.ID='" + ID + "'";
            List<Map<String, Object>> rowsOfBill = DBUtil.querySql(querySql);
            if (rowsOfBill == null || rowsOfBill.isEmpty()) {
                logService.error(billCode, "未找到待付池附件");
                logService.flush();
                continue;
            }
            String payMethod = rowsOfBill.get(0).get("NAME").toString();
            String imageID = rowsOfBill.get(0).get("TXT06").toString();
            if (StringUtil.isNullOrEmpty(payMethod)) {
                continue;
            }
            if ("现金折扣-扣减不支付".equals(payMethod) || payMethod.contains("抵账") || payMethod.contains("开转") || payMethod.contains("转承")) {
                if (StringUtil.isNullOrEmpty(imageID)) {
                    logService.error(billCode, "附件不能为空");
                    logService.flush();
                    return RD.error("支付方式要求附件不能为空");
                }

                String querySql2 = "select count(1) as COUNT from EISIMAGEFILE where BILLID = '" + imageID + "'";
                List<Map<String, Object>> rowsOfImage = DBUtil.querySql(querySql2);
                if (rowsOfImage == null || rowsOfImage.isEmpty()) {
                    logService.error(billCode, "附件不能为空");
                    logService.flush();
                    return RD.error("支付方式要求附件不能为空");
                }else{
                    int count = Integer.parseInt(rowsOfImage.get(0).get("COUNT").toString());
                    if (count <= 0) {
                        logService.error(billCode, "附件不能为空");
                        logService.flush();
                        return RD.error("支付方式要求附件不能为空");
                    }
                }
            }
        }
        return RD.ok("success");
    }

    public RD<String> obtainHLYURL(Map<String, Object> billInfo){
        String billCode = "linkURL";

        // 参数校验和获取
        if (billInfo == null || billInfo.isEmpty()) {
            logService.error(billCode, "参数infos为空");
            logService.flush();
            return RD.error("参数不能为空");
        }

        String userId = (String) billInfo.get("ACCOUNT"); // OA账号
        String linkURL = (String) billInfo.get("linkURL"); // 来源单据ID
        String querySql = "select CODE from GSPUSER where id = ?1";
        List<Map<String, Object>> rowOfUser = DBUtil.querySql(querySql, userId);
        String userCode = rowOfUser.get(0).get("CODE").toString();
        if (StringUtil.isNullOrEmpty(userCode)) {
            logService.error(billCode, "用户CODE为空");
            logService.flush();
            return RD.error("用户CODE为空");
        }

        // 对userId进行AES-256加密
        String encryptedUserId = encryptUserInfo(userId);

        try {
            // 对加密后的用户信息进行URL编码
            String encodedUserInfo = URLEncoder.encode(encryptedUserId, StandardCharsets.UTF_8.toString());
            return RD.ok("获取URL成功", linkURL + "&clientName=goldwind&userinfo=" + encodedUserInfo);
        } catch (Exception e) {
            logService.error(billCode, "URL编码异常：" + ExceptionUtils.getStackTrace(e));
            logService.flush();
            return RD.error("URL编码异常: " + e.getMessage());
        }
    }

    /**
     * 内部接口：未安排完成的付款申请准备退回到业务系统
     * @param args 入参：{ ID: 接口日志表ID, REASON: 退回原因 }
     * @return 是否操作成功
     */
    @Override
    public R backStatus(String args) {
        if (StringUtil.isNullOrEmpty(args)) {
            return R.error("入参不能为空");
        }
        JSONObject jObjectArgs = JSON.parseObject(args);
        String docId = jObjectArgs.getString("ID");
        if (StringUtil.isNullOrEmpty(docId)) {
            return R.error("入参ID不能为空");
        }
        String reason = jObjectArgs.getString("REASON");
        if (StringUtil.isNullOrEmpty(reason)) {
            return R.error("退回原因不能为空");
        }

        String lockId;
        try {
            String moduleId = "goldwind";
            String funcId = "cancel";
            String categoryId = "JfskUnipayController";
            String dataID = docId;
            String comment = "待付池申请退回";
            LockResult lockResult = lockService.addLock(moduleId, categoryId, dataID, new DataLockOptions(Duration.ofMinutes(30), ReplacedScope.Exclusive, LockedScope.AppInstance, Duration.ofMinutes(30)), funcId, comment);
            if (lockResult == null || !lockResult.isSuccess()) {
                log.error("待付池申请退回加锁失败");
                return R.error("待付池申请退回加锁失败");
            }
            lockId = lockResult.getLockId();
            log.info("待付池申请退回加锁结果：lockId=" + lockId);
        } catch (Throwable e) {
            log.error("待付池申请加锁过程发生异常：", e.toString());
            return R.error("待付池申请加锁过程发生异常");
        }

        R resp;
        try {
            resp = unipayService.backToBizSys(docId, reason);
            log.info("付款申请处理结果：" + JSON.toJSONString(resp));
        } catch (Throwable ex) {
            log.error("待付池申请处理过程发生异常：", ex);
            resp = R.error("待付池申请处理过程发生异常：" + ex.getMessage());
        }

        try {
            lockService.removeLock(lockId);
            log.info("待付池申请已解锁");
        } catch (Throwable e) {
            log.error("待付池申请解锁过程发生异常：", e);
        }

        return resp;
    }


    /**
     * 内部接口：挂起
     * @param billInfo 传入信息
     * @return 查询结果
     */
    @Override
    public RD<String> pause(Map<String, Object> billInfo) {
        LogService logService = SpringBeanUtils.getBean(LogService.class);
        logService.init("pauseOperation");

        List<String> billIDs = (List<String>) billInfo.get("billIDs");
        if (billIDs == null || billIDs.isEmpty()) {
            return RD.error("入参不能为空");
        }

        for (String docId : billIDs) {
            // 更新状态为挂起状态
            String updateSql = "update JTGKPAYMENTINFO set DOCSTATUS=-4 where ID='" + docId + "'";
            logService.info("pauseOperation", "更新付款申请状态，SQL：{}", updateSql);
            int count = DBUtil.executeUpdateSQL(updateSql);
            logService.info("pauseOperation", "更新付款申请状态结果：{}", count);
        }
        logService.flush();
        return RD.ok("success");
    }


    /**
     * 内部接口：取消挂起
     * @param billInfo 传入信息
     * @return 查询结果
     */
    @Override
    public RD<String> cancelpause(Map<String, Object> billInfo) {
        LogService logService = SpringBeanUtils.getBean(LogService.class);
        logService.init("cancelpauseOperation");

        List<String> billIDs = (List<String>) billInfo.get("billIDs");
        if (billIDs == null || billIDs.isEmpty()) {
            return RD.error("入参不能为空");
        }

        for (String docId : billIDs) {
            // 更新状态为付款安排状态
            String updateSql = "update JTGKPAYMENTINFO set DOCSTATUS=1 where ID='" + docId + "'";
            logService.info("cancelpauseOperation", "更新付款申请状态，SQL：{}", updateSql);
            int count = DBUtil.executeUpdateSQL(updateSql);
            logService.info("cancelpauseOperation", "更新付款申请状态结果：{}", count);
        }
        logService.flush();
        return RD.ok("success");
    }


    @Override
    public R testUnipayResultScheduler(Map<String, Object> args){
        logService.init("K0104");
        try {
            unipayResultService.processResult(logService);
        } catch (Throwable ex) {
            log.error("处理异构系统付款结果发生异常：" + ex.toString());
            logService.error("K0104", "处理异构系统付款结果发生异常：" + ExceptionUtils.getStackTrace(ex));
        }
        logService.flush();
        return R.ok("success");
    }

    @Override
    public R testPaymentAutoDealScheduler(Map<String, Object> args){
        logService.init("K0105");
        try {
            paymentAutoDealService.processBills(logService);
        } catch (Throwable ex) {
            log.error("业务支付申请自动办理发生异常：" + ex.toString());
            logService.error("K0105", "业务支付申请自动办理发生异常：" + ExceptionUtils.getStackTrace(ex));
        }
        logService.flush();
        return R.ok("success");
    }

    @Override
    public R testPaymentAutoConfirmScheduler(Map<String, Object> args){
        logService.init("K0106");
        try {
            jtgkSettlementConfirmTaskService.settlementConfirmTask();
        } catch (Throwable ex) {
            log.error("业务支付申请自动办理发生异常：" + ex.toString());
            logService.error("K0105", "业务支付申请自动办理发生异常：" + ExceptionUtils.getStackTrace(ex));
        }
        logService.flush();
        return R.ok("success");
    }

    @Override
    public R testPushDiscountBillToSap(Map<String, Object> args){
        logService.init("K0106");
        try {
            jfskReceivableBillSapPushScheduler.pushDiscountBillToSap();
        } catch (Throwable ex) {
            log.error("业务支付申请自动办理发生异常：" + ex.toString());
            logService.error("K0105", "业务支付申请自动办理发生异常：" + ExceptionUtils.getStackTrace(ex));
        }
        logService.flush();
        return R.ok("success");
    }

    @Override
    public R testPushCollectionBillToSap(Map<String, Object> args){
        logService.init("K0106");
        try {
            jfskReceivableBillSapPushScheduler.pushCollectionBillToSap();
        } catch (Throwable ex) {
            log.error("业务支付申请自动办理发生异常：" + ex.toString());
            logService.error("K0105", "业务支付申请自动办理发生异常：" + ExceptionUtils.getStackTrace(ex));
        }
        logService.flush();
        return R.ok("success");
    }

    @Override
    public R testPushPayableBillToSap(Map<String, Object> args){
        logService.init("K0106");
        try {
            jfskReceivableBillSapPushScheduler.pushPayableBillToSap();
        } catch (Throwable ex) {
            log.error("业务支付申请自动办理发生异常：" + ex.toString());
            logService.error("K0105", "业务支付申请自动办理发生异常：" + ExceptionUtils.getStackTrace(ex));
        }
        logService.flush();
        return R.ok("success");
    }
}
