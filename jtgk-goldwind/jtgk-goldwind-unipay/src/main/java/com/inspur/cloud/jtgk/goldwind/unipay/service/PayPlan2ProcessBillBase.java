package com.inspur.cloud.jtgk.goldwind.unipay.service;

import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.JfskException;
import com.inspur.gs.tm.tmfnd.fsjspub.api.dto.FsspResultRet;
import com.inspur.gs.tm.tmfnd.fsjspub.core.entity.TMProcessBillBase;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @project jtgk-goldwind
 * @description
 * @date 2025/5/20 07:32:34
 */

@Slf4j
@Component
@Transactional(rollbackFor = Exception.class)
public class PayPlan2ProcessBillBase extends TMProcessBillBase {

    @Override
    public String getFormType() {
        return "JFPAYPLAN2";
    }

    @Override
    public String getFormUrl() {
        return "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=ad2b927e-689f-d432-c8e6-bb6b642beb76&modid=ad2b927e-689f-d432-c8e6-bb6b642beb76";
    }

    @Override
    public Map<String, Object> getBillInfo(String billId) {
        Map<String, Object> mapParams = new HashMap<String, Object>();
        String sql = "select DOCNO,PAYUNITID,ID from JTGKPAYPLANBILL2 where ID=?1 ";
        List<Map<String, Object>> list = DBUtil.querySql(sql, billId);
        if (list.size() > 0) {
            Map<String, Object> map = list.get(0);
            mapParams.put("DJBH", map.get("DOCNO"));
            mapParams.put("DWID", map.get("PAYUNITID"));
            mapParams.put("OTHERINFO", "付款安排审批");
            mapParams.put("PROCESSID", map.get("ID"));
        } else {
            throw new JfskException("goldwind", "PayPlanProcessBillBaseEvent-001", "付款安排审批获取表单信息失败", null);
        }
        return mapParams;
    }

    @Override
    public boolean setBillProcessID(String billId, String processId) {
        String sql = "update JTGKPAYPLANBILL2 set TXT07=?1 where ID=?2 ";
        int upcount = DBUtil.executeUpdateSQL(sql, processId, billId);
        if (upcount == 0) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 提交后事件
     *
     * @return
     */
    @Override
    public FsspResultRet fspfsubmitApprove(Map<String, Object> data) {
        Map<String, Object> map = (Map<String, Object>) data.get("contextParam");
        String id = (String) map.get("BILLID");
        
        String updateSql = "update JTGKPAYPLANBILL2 set DOCSTATUS = '1' where ID = ?1";

        int count = DBUtil.executeUpdateSQL(updateSql, id);
        if (count == 0) {
            throw new JfskException("goldwind", "PayPlanProcessBillBaseEvent-002", "付款安排审批更新失败", null);
        }
        return super.fspfsubmitApprove(data);
    }

    /**
     * 通过后后事件
     *
     * @return
     */
    @Override
    public FsspResultRet fspfapprovePass(Map<String, Object> data) {
//        Map<String, Object> map = (Map<String, Object>) data.get("contextParam");
//        String DQHJBH = (String) map.get("DQHJBH");
//        if (StringUtils.equals(DQHJBH, "APPROVAL-1")) {
//            String docId = (String) map.get("BILLID");
//            checkPass(docId);
//        }
        return super.fspfapprovePass(data);
    }

    /**
     * 退回后事件
     *
     * @return
     */
    @Override
    public FsspResultRet fspfapproveBack(Map<String, Object> data) {
        Map<String, Object> map = (Map<String, Object>) data.get("contextParam");
        String id = (String) map.get("BILLID");

        String updateSql = "update JTGKPAYPLANBILL2 set DOCSTATUS = '4' where ID = ?1";

        int count = DBUtil.executeUpdateSQL(updateSql, id);
        if (count == 0) {
            throw new JfskException("goldwind", "PayPlanProcessBillBaseEvent-002", "付款安排审批更新失败", null);
        }

        // 如果有，票据补录单据作废并
        String updateSql2 = "UPDATE JTGKPAYPLANBILL3 \n" +
                "SET DOCSTATUS = '3' \n" +
                "FROM JTGKPAYPLANBILL3 a\n" +
                "INNER JOIN JTGKPAYPLANDETAIL b ON b.PARENTID3 = a.ID\n" +
                "INNER JOIN JTGKPAYPLANBILL2 c ON c.ID = b.PARENTID2\n" +
                "WHERE JTGKPAYPLANBILL3.ID = a.ID\n" +
                "AND c.ID = ?1";
        DBUtil.executeUpdateSQL(updateSql2, id);

        // 更新明细状态为待复核且去掉parentid3
        String updateSql3 = "UPDATE JTGKPAYPLANDETAIL \n" +
                "SET docstatus = '1', parentId3='' \n" +
                "FROM JTGKPAYPLANDETAIL a\n" +
                "INNER JOIN JTGKPAYPLANBILL2 b ON a.parentId2 = b.Id \n" +
                "WHERE JTGKPAYPLANDETAIL.Id = a.Id\n" +
                "AND b.Id = ?1";
        DBUtil.executeUpdateSQL(updateSql3, id);

        return super.fspfapproveBack(map);
    }

    /**
     * 流程结束
     */
    @Override
    public FsspResultRet fspfwflowfinish(Map<String, Object> data) {
        Map<String, Object> map = (Map<String, Object>) data.get("contextParam");
        String id = (String) map.get("BILLID");

        return super.fspfwflowfinish(data);
    }

    @Override
    public FsspResultRet fspfbillView(Map<String, Object> data) {
        Map<String, Object> contextParam = (Map)data.get("contextParam");
        FsspResultRet resultRet = new FsspResultRet();
        String DQHJBH = String.valueOf(contextParam.get("DQHJBH"));
        String DQHJ = String.valueOf(contextParam.get("DQHJ"));
        String taskId = String.valueOf(contextParam.get("PFTASKID"));
        String taskType = String.valueOf(contextParam.get("TASKTYPE"));
        String dataId = String.valueOf(contextParam.get("BILLID"));
        String bizType = String.valueOf(contextParam.get("FORMTYPE"));
        String catecode = String.valueOf(contextParam.get("BIZCATCODE"));
        String operation = String.valueOf(contextParam.get("OPERATION"));
        String codeType = String.valueOf(contextParam.get("NODETYPE"));
        String formCode = String.valueOf(contextParam.get("FORMCODE"));
        String formName = String.valueOf(contextParam.get("FORMNAME"));
        String formId = String.valueOf(contextParam.get("FORMID"));
        String sourceType = String.valueOf(contextParam.get("SOURCETYPE"));
        Map<String, Object> imageParams = (Map)contextParam.get("IMAGEPARAMS");
        List<Map<String, Object>> operates = (List)contextParam.get("OPERATES");
        Map<String, Object> otherParams = (Map)contextParam.get("OTHERPARAM");
        String status = "view";
        switch (operation) {
            case "edit":
                status = "edit";
                break;
            case "add":
                status = "add";
            case "dbbl":
            case "ybbl":
        }

        Map<String, Object> map = new HashMap();
        map.put("dataId", dataId);
        map.put("FORMTYPE", bizType);
        String url = "";
        if (StringUtils.isNotEmpty(sourceType) && "mobile".equals(sourceType.toLowerCase())) {
            url = this.getMobileFormUrl(map);
        } else {
            url = this.getFormUrlFormType(bizType);
        }

        if ("ZJFH".equals(DQHJBH)) {
            // 获取对应的表单
            String querySql = "select distinct JTGKPAYPLANBILL3.ID as BILLID from JTGKPAYPLANBILL3\n" +
                    "LEFT JOIN JTGKPAYPLANDETAIL on JTGKPAYPLANDETAIL.PARENTID3 = JTGKPAYPLANBILL3.ID\n" +
                    "LEFT JOIN JTGKPAYPLANBILL2 on JTGKPAYPLANBILL2.ID = JTGKPAYPLANDETAIL.PARENTID2\n" +
                    "where JTGKPAYPLANBILL2.ID = ?1";
            List<Map<String, Object>> rows = DBUtil.querySql(querySql, dataId);
            if (rows == null || rows.isEmpty()) {
                resultRet.setResult(false);
                resultRet.setMessage("未找到对应的表单");
                return resultRet;
            }
            dataId = rows.get(0).get("BILLID").toString();
            url = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=4d7e2ab0-61bb-3e09-6ad2-d331747b5527";
        }

        url = url + "&source=fssp&status=" + status + "&dataid=" + dataId + "&DQHJBH=" + DQHJBH + "&DQHJ=" + DQHJ + "&PFTASKID=" + taskId + "&TASKTYPE=" + taskType + "&FORMTYPE=" + bizType + "&BIZCATCODE=" + catecode + "&OPERATION=" + operation + "&NODETYPE=" + codeType + "&FORMCODE=" + formCode + "&FORMNAME=" + formName + "&FORMID=" + formId + "&runtime=true&fdim=" + this.getfDIM(contextParam) + "&sdim=" + this.getsDIM(contextParam) + "&imageParams=" + JSON.toJSONString(imageParams) + "&operates=" + JSON.toJSONString(operates) + "&otherParam=" + JSON.toJSONString(otherParams);
        resultRet.setResult(true);
        resultRet.setValue(url);
        return resultRet;
    }

}
