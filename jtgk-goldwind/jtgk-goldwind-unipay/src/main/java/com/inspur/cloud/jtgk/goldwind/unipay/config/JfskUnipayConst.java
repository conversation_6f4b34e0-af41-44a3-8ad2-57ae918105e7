package com.inspur.cloud.jtgk.goldwind.unipay.config;

public class JfskUnipayConst {
    // 待付池接收接口_款项性质映射
    public static final String ValueMapping_FundNature = "79e52ec1-e8c8-401a-b605-db40df86834d";

    // SFS安排系统标识
    public static final String SFS = "SFS-PAY";

    // SFS系统结算方式：财企直联
    public static final String SFS_FiPay = "电汇";

    // SFS系统结算方式：线下网银
    public static final String SFS_OfflinePay = "线下";

    // SFS系统结算方式：金风云信
    public static final String SFS_CredPay = "金风云信";

    // SFS系统结算方式：现金折扣-电汇支付
    public static final String SFS_FiDiscountTPay = "现金折扣-电汇支付";

    // SFS系统结算方式：抵账-电汇
    public static final String SFS_DZDHPay = "抵账-电汇";

    // 金风财务公司联行号
    public static final String FiBankCode = "************";
}
