package com.inspur.cloud.jtgk.goldwind.unipay.service;


import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.R;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.JfskException;
import com.inspur.gs.bf.df.fssp.pf.api.service.PFProcessEventListener;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.lockservice.api.*;
import io.iec.edp.caf.rpc.api.annotation.GspServiceBundle;
import io.iec.edp.caf.rpc.api.annotation.RpcServiceMethod;
import io.iec.edp.caf.rpc.api.service.RpcClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.net.ssl.HttpsURLConnection;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;


@Slf4j
@Service
@GspServiceBundle(applicationName = "TM", serviceUnitName = "CM", serviceName = "PayPlan2WorkItemRpcService")
public class PayPlan2WorkItemRpcService implements PFProcessEventListener {

    @Autowired
    private JfskPayBillService payBillService;
    @Autowired
    private ILockService lockService;
    @Autowired
    private LogService logService;

    @Override
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.goldwind.unipay.service.PayPlan2WorkItemRpcService.afterInstanceStart")
    @Transactional(rollbackFor = Exception.class)
    public void afterInstanceStart(Map params) {
        JSON.toJSONString(params);
    }

    @Override
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.goldwind.unipay.service.PayPlan2WorkItemRpcService.instanceEnd")
    @Transactional(rollbackFor = Exception.class)
    public void instanceEnd(Map params) {
        JSON.toJSONString(params);
    }

    @Override
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.goldwind.unipay.service.PayPlan2WorkItemRpcService.beforeActivitiCreate")
    @Transactional(rollbackFor = Exception.class)
    public void beforeActivitiCreate(Map params) {
        JSON.toJSONString(params);
    }


    @Override
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.goldwind.unipay.service.PayPlan2WorkItemRpcService.afterActivitiCreate")
    @Transactional(rollbackFor = Exception.class)
    public void afterActivitiCreate(Map params) {
        String billId = (String) params.get("billId");
        if (StringUtils.isBlank(billId)) {
            billId = (String) params.get("BILLID");
        }
        String DQHJBH = (String) params.get("DQHJBH");
        String FORMTYPE = (String) params.get("FORMTYPE");
        String PFRUTASKSID = (String) params.get("PFRUTASKSID");
        if (("OPERATION-1".equals(DQHJBH) || "DZBZF".equals(DQHJBH)) && "JFPAYPLAN2".equals(FORMTYPE)) {
            if ("OPERATION-1".equals(DQHJBH)) {
                checkPass(billId);
            }

            String updateSql = "update JTGKPAYPLANBILL2 set TXT09 =?1 where ID = ?2";
            int count = DBUtil.executeUpdateSQL(updateSql, PFRUTASKSID, billId);
            if (count == 0) {
                throw new JfskException("goldwind", "PayPlan2WorkItemRpcService-001", "付款安排审批更新失败", null);
            }
        }
    }


    @Override
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.goldwind.unipay.service.PayPlan2WorkItemRpcService.beforeActivitiComplete")
    @Transactional(rollbackFor = Exception.class)
    public void beforeActivitiComplete(Map params) {
        String billId = (String) params.get("billId");
        if (StringUtils.isBlank(billId)) {
            billId = (String) params.get("BILLID");
        }
        String DQHJBH = (String) params.get("DQHJBH");
        String FORMTYPE = (String) params.get("FORMTYPE");
        if ("OPERATION-1".equals(DQHJBH) && "JFPAYPLAN2".equals(FORMTYPE)) {
            // 查看是否不是从任务中心来的
            String querySql = "select distinct JTGKPAYPLANBILL2.TXT08 as TXT08 from JTGKPAYPLANBILL2 where JTGKPAYPLANBILL2.ID = ?1" ;
            List<Map<String, Object>> list =  DBUtil.querySql(querySql, billId);
            if (list.size() == 0) {
                throw new JfskException("goldwind", "PayPlan2WorkItemRpcService-001", "未找到票据补录对应的付款安排", null);
            }
            String TXT08 = list.get(0).get("TXT08").toString();
            if (!"1".equals(TXT08)) {
                throw new JfskException("goldwind", "PayPlan2WorkItemRpcService-001", "请到票据补录对应功能下进行处理！", null);
            }
        }else if ("DZBZF".equals(DQHJBH) && "JFPAYPLAN2".equals(FORMTYPE)){
            checkPass(billId);
        }else if ("ZJFH".equals(DQHJBH) && "JFPAYPLAN2".equals(FORMTYPE)) {
            String querySql = "select distinct JTGKPAYPLANBILL3.ID as BILLID, JTGKPAYPLANBILL3.DOCNO as DOCNO from JTGKPAYPLANBILL3\n" +
                    "LEFT JOIN JTGKPAYPLANDETAIL on JTGKPAYPLANDETAIL.PARENTID3 = JTGKPAYPLANBILL3.ID\n" +
                    "LEFT JOIN JTGKPAYPLANBILL2 on JTGKPAYPLANBILL2.ID = JTGKPAYPLANDETAIL.PARENTID2\n" +
                    "where JTGKPAYPLANBILL2.ID = ?1" ;

            List<Map<String, Object>> list = DBUtil.querySql(querySql, billId);
            if (list == null || list.size() == 0) {
                throw new JfskException("goldwind", "PayPlan2WorkItemRpcService-001", "未找到票据补录对应的付款安排", null);
            }
            String processId = list.get(0).get("BILLID").toString();
            String docNo = list.get(0).get("DOCNO").toString();

            R handlePassResult = this.handlePass(processId, docNo);

            if (!handlePassResult.getResult()) {
                throw new JfskException("goldwind", "PayPlan2WorkItemRpcService-001", "票据补录通过失败：" + handlePassResult.getMessage(), null);
            }
        }

    }



    @Override
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.goldwind.unipay.service.PayPlan2WorkItemRpcService.afterActivitiComplete")
    @Transactional(rollbackFor = Exception.class)
    public void afterActivitiComplete(Map params) {
        JSON.toJSONString(params);
    }


    @Override
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.goldwind.unipay.service.PayPlan2WorkItemRpcService.beforeActivitiReturn")
    @Transactional(rollbackFor = Exception.class)
    public void beforeActivitiReturn(Map params) {
        JSON.toJSONString(params);
    }


    @Override
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.goldwind.unipay.service.PayPlan2WorkItemRpcService.afterActivitiReturn")
    @Transactional(rollbackFor = Exception.class)
    public void afterActivitiReturn(Map params) {
        JSON.toJSONString(params);
    }


    @Override
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.goldwind.unipay.service.PayPlan2WorkItemRpcService.beforeActivitiRetract")
    @Transactional(rollbackFor = Exception.class)
    public void beforeActivitiRetract(Map params) {
        JSON.toJSONString(params);
    }



    @Override
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.goldwind.unipay.service.PayPlan2WorkItemRpcService.afterActivitiRetract")
    @Transactional(rollbackFor = Exception.class)
    public void afterActivitiRetract(Map params) {
        JSON.toJSONString(params);
    }


    public void checkPass(String docId){
        String lockId;
        String moduleId = "goldwind";
        String funcId = "planBillCheck";
        String categoryId = "JfskPayBillController";
        String dataID = docId;

        if (StringUtils.isBlank(docId)) {
            throw new JfskException("goldwind", "PayPlan2ProcessBillBaseEvent-007", "获取付款安排复核内码失败", null);
        }
        String querySql = "select DOCNO from JTGKPAYPLANBILL2 where ID = ?1";
        List<Map<String, Object>> list = DBUtil.querySql(querySql, docId);
        if (list.size() == 0) {
            throw new JfskException("goldwind", "PayPlan2ProcessBillBaseEvent-008", "获取付款安排复核单据号失败", null);
        }
        String docNo = list.get(0).get("DOCNO").toString();

        try {
            String comment = "付款安排复核通过防止并发操作";
            LockResult lockResult = lockService.addLock(moduleId, categoryId, dataID, new DataLockOptions(Duration.ofMinutes(30), ReplacedScope.Exclusive, LockedScope.AppInstance, Duration.ofMinutes(30)), funcId, comment);
            if (lockResult == null || !lockResult.isSuccess()) {
                log.error("付款安排复核通过加锁失败");
                throw new JfskException("goldwind", "PayPlan2ProcessBillBaseEvent-003", "付款安排复核通过加锁失败", null);
            }
            lockId = lockResult.getLockId();
            log.info("付款安排复核通过加锁结果：lockId=" + lockId);
        } catch (Throwable e) {
            log.error("付款安排复核通过加锁过程发生异常：{}", e.toString());
            throw new JfskException("goldwind", "PayPlan2ProcessBillBaseEvent-004", "付款安排复核通过加锁过程发生异常：" + e.getMessage(), null);
        }
        R result;
        logService.init("K0102");
        logService.info(docNo, "付款安排复核通过：id=" + docId);
        try {
            result = payBillService.checkPass(logService, docId, docNo, false);
        } catch (Throwable e) {
            log.error("付款安排复核通过发生异常：" + e.toString());
            logService.error(docNo, "付款安排复核通过发生异常：" + ExceptionUtils.getStackTrace(e));
            throw new JfskException("goldwind", "PayPlan2ProcessBillBaseEvent-005", "付款安排复核通过发生异常：" + e.getMessage(), null);
        }
        if (result == null || !result.getResult()) {
            throw new JfskException("goldwind", "PayPlan2ProcessBillBaseEvent-006", "付款安排复核通过发生异常：" + result == null ? "返回信息为空" : result.getMessage(), null);
        }

        logService.flush();
        try {
            lockService.removeLock(lockId);
            log.info("付款安排复核通过已解锁");
        } catch (Throwable e) {
            log.error("付款安排复核通过解锁过程发生异常：" + e.toString());
        }

    }


    public R handlePass(String docId, String docNo){
        log.info("票据背书办理完成：id=" + docId);

        String lockId;
        try {
            String moduleId = "goldwind";
            String funcId = "planBillHandle";
            String categoryId = "JfskPayBillController";
            String dataID = docId;
            String comment = "票据背书办理完成防止并发操作";
            LockResult lockResult = lockService.addLock(moduleId, categoryId, dataID, new DataLockOptions(Duration.ofMinutes(30), ReplacedScope.Exclusive, LockedScope.AppInstance, Duration.ofMinutes(30)), funcId, comment);
            if (lockResult == null || !lockResult.isSuccess()) {
                log.error("票据背书办理完成加锁失败");
                return R.error("票据背书办理完成加锁失败");
            }
            lockId = lockResult.getLockId();
            log.info("票据背书办理完成加锁结果：lockId=" + lockId);
        } catch (Throwable e) {
            log.error("票据背书办理完成加锁过程发生异常：{}", e.toString());
            return R.error("票据背书办理完成加锁过程发生异常：" + e.getMessage());
        }
        R result;
        logService.init("K0103");
        logService.info(docNo, "票据背书办理完成：id=" + docId);
        try {
            result = payBillService.handlePass(logService, docId, docNo);
        } catch (Throwable e) {
            log.error("票据背书办理完成发生异常：" + e.toString());
            logService.error(docNo, "票据背书办理完成发生异常：" + ExceptionUtils.getStackTrace(e));
            result = R.error("票据背书办理完成发生异常：" + e.getMessage());
        }
        logService.flush();
        try {
            lockService.removeLock(lockId);
            log.info("票据背书办理完成已解锁");
        } catch (Throwable e) {
            log.error("票据背书办理完成解锁过程发生异常：" + e.toString());
        }
        return result;
    }

}
