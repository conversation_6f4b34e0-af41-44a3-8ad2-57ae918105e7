package com.inspur.cloud.jtgk.goldwind.unipay.controller;

import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.unipay.api.JfskPayBillApi;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.AssignBillsParam;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.AssignBillsResult;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.R;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.RD;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskBankAccountEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPayPlanDetailEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.repository.JfskPayPlanDetailRepository;
import com.inspur.cloud.jtgk.goldwind.unipay.service.*;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.JfskException;
import com.inspur.gs.tm.tmfnd.fsjspub.api.dto.FsspResultRet;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.commons.transaction.JpaTransaction;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.lockservice.api.*;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;

import javax.persistence.Query;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.QueryParam;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 付款安排表
 */
@Controller
@Slf4j
public class JfskPayBillController implements JfskPayBillApi {
    @Autowired
    private ILockService lockService;
    @Autowired
    private LogService logService;
    @Autowired
    private JfskPayBillService payBillService;
    @Autowired
    private JfskPayPlanDetailRepository payPlanDetailRepository;
    @Autowired
    private JfskUnipayAssignBillsService assignBillsService;
    @Autowired
    private JfskDefaultPayAccountService defaultPayAccountService;
    @Autowired
    private PayPlan2ProcessBillBase payPlan2ProcessBillBase;

    /**
     * 内部接口：获取单据状态
     *
     * @param docId 单据ID
     * @return 单据状态 { result: true, message: null, data: 0 }
     */
    @Override
    public String getDocStatus(String docId) {
        log.info("查询单据状态：id=" + docId);
        String selectSql = "select ID,DOCNO,DOCSTATUS from JTGKPAYPLANBILL where ID=?1";
        log.info(selectSql + ", ?1=" + docId);
        List<Map<String, Object>> rows = DBUtil.querySql(selectSql, docId);
        log.info(JSON.toJSONString(rows));
        if (rows == null || rows.isEmpty()) {
            log.error("未找到指定的付款安排单：id=" + docId);
            return R.error("未找到指定的付款安排单：" + docId).toString();
        }
        String docStatus = rows.get(0).get("DOCSTATUS").toString();
        return RD.ok(null, docStatus).toString();
    }

    /**
     * 内部接口：获取帮助内容
     * @param name 帮助内容
     * @return 帮助相关内容
     */
    @Override
    public RD<Map<String, Object>> getHelpInfos(String name){
        RD result = new RD();
        result.setResult(false);
        result.setMessage("查询帮助内容失败");

        logService.init("K0106");
        log.info("查询帮助内容：name=" + name);

        String querySql = "select * from (\n" +
                "SELECT t.ID,\n" +
                "t.CODE AS SRCBIZSYS,\n" +
                "t.NAME AS CODE,\n" +
                "t.TXT01 AS NAME,\n" +
                "t.TREE_CODE,\n" +
                "d.TARCOL01 AS SETTLEMENTWAYID,\n" +
                "d.TARCOL01CODE AS SETTLEMENTWAYID_CODE,\n" +
                "d.TARCOL01NAME AS SETTLEMENTWAYID_NAME,\n" +
                "d.TARCOL02 AS BILLPAYWAY,\n" +
                "wt.IFACCEPTANCEBILL\n" +
                "FROM IDD_DATADICTIONARY t\n" +
                "LEFT JOIN IDD_VMDATA d ON t.ID=d.DATAID and d.VMTYPE='ebd2a42f-3f40-2e66-4805-5fabd1078342'\n" +
                "left join BFSETTLEMENTWAY w on d.TARCOL01=w.ID\n" +
                "left join BFSETTLEMENTWAYTYPE wt on wt.ID=w.SETTLEMENTWAYTYPE\n" +
                "WHERE t.CODE='SFS-PAY' and CATEGORYID='3a6b4bb2-2548-6bf3-c17b-3ce189e2081c'\n" +
                ") IDD_DATADICTIONARY\n" +
                "where 1=1 and NAME = ?1";
        List<Map<String, Object>> rows = DBUtil.querySql(querySql, name);
        if (rows == null || rows.isEmpty()) {
            log.error("未找到指定的帮助内容：name=" + name);
            return result;
        }
        Map<String, Object> helpInfo = rows.get(0);
        result.setResult(true);
        Map<String, Object> returnData = new HashMap<>();
        returnData.put("TXT01", helpInfo.get("ID"));
        returnData.put("TXT01_NAME", helpInfo.get("NAME"));
        returnData.put("TXT01_TXT01", helpInfo.get("CODE"));
        returnData.put("BILLPAYWAY", helpInfo.get("BILLPAYWAY"));
        returnData.put("SETTLEMENTWAYID", helpInfo.get("SETTLEMENTWAYID"));
        returnData.put("SETTLEMENTWAYID_CODE", helpInfo.get("SETTLEMENTWAYID_CODE"));
        returnData.put("SETTLEMENTWAYID_NAME$LANGUAGE$", helpInfo.get("SETTLEMENTWAYID_NAME"));
        returnData.put("IFACCEPTANCEBILL", helpInfo.get("IFACCEPTANCEBILL"));
        result.setData(returnData);
        return result;
    }

    /**
     * 内部接口：查询某一支付明细的默认付款账户
     *
     * @param payPlanDetail 支付明细
     * @return 查询结果
     */
    @Override
    public RD<Map<String, Object>> getDefaultAccount(Map<String, Object> payPlanDetail) {
        RD result = new RD();
        logService.init("K0105");
        log.info("查询某一支付明细的默认付款账户：" + JSON.toJSONString(payPlanDetail));
        String srcBizSys = (String) payPlanDetail.get("SRCBIZSYS");
        String srcDocNo = (String) payPlanDetail.get("SRCDOCNO");
        String payUnitCode = (String) payPlanDetail.get("PAYUNITCODE");
        String purchaseCode = (String) payPlanDetail.get("EXTPURCHASECODE");
        String payMethodCode = (String) payPlanDetail.get("SRCPAYMETHOD");
        RD<JfskBankAccountEntity> getDefaultPayAccount = defaultPayAccountService.getDefaultPayAccount(logService, srcBizSys, srcDocNo, payUnitCode, purchaseCode, payMethodCode);
        if (!getDefaultPayAccount.getResult()) {
            log.error("查询默认付款账户失败：" + getDefaultPayAccount.getMessage());
            logService.error(srcDocNo, "查询默认付款账户失败：" + getDefaultPayAccount.getMessage());
            result.setResult(false);
            result.setMessage(getDefaultPayAccount.getMessage());
        } else {
            Map<String, Object> account = new HashMap<>();
            if (getDefaultPayAccount.getData() != null) {
                account.put("PAYACCOUNTID", getDefaultPayAccount.getData().getId());
                account.put("PAYACCOUNTNO", getDefaultPayAccount.getData().getAccountNo());
                account.put("PAYACCOUNTNAME", getDefaultPayAccount.getData().getAccountName());
            }
            result.setResult(true);
            log.info("默认付款账户：" + JSON.toJSONString(account));
            logService.info(srcDocNo, "默认付款账户：" + JSON.toJSONString(account));
            result.setData(account);
        }
        logService.flush();
        return result;
    }

    /**
     * 内部接口：付款安排提交
     * @param docId 安排单据ID
     * @return 操作结果 { result: true, message: null }
     */
    @Override
    public R submit(String docId) {
        log.info("付款安排提交：id=" + docId);
        String selectSql = "select ID,DOCNO,DOCSTATUS from JTGKPAYPLANBILL where ID=?1";
        log.info(selectSql + ", ?1=" + docId);
        List<Map<String, Object>> rows = DBUtil.querySql(selectSql, docId);
        log.info(JSON.toJSONString(rows));
        if (rows == null || rows.isEmpty()) {
            log.error("未找到指定的付款安排单：id=" + docId);
            return R.error("未找到指定的付款安排单：" + docId);
        }
        String docStatus = rows.get(0).get("DOCSTATUS").toString();
        if (!("0".equals(docStatus) || "3".equals(docStatus))) {
            log.error("当前单据状态不允许提交：id=" + docId + ", docStatus=" + docStatus);
            return R.error("当前单据状态不允许提交");
        }
        List<JfskPayPlanDetailEntity> details = payPlanDetailRepository.findAllByParentId(docId);
        if (details.isEmpty()) {
            log.error("支付明细不能为空");
            return R.error("支付明细不能为空");
        } else {
            List<JfskPayPlanDetailEntity> wrongDetails = details.stream()
                    .filter(o -> o.getAmount().compareTo(o.getUnpayAmount()) > 0)
                    .collect(Collectors.toList());
            if (!wrongDetails.isEmpty()) {
                StringBuilder buffer = new StringBuilder("支付明细");
                for (JfskPayPlanDetailEntity detail : wrongDetails) {
                    buffer.append(detail.getSrcDocNo()).append(",");
                }
                buffer.deleteCharAt(buffer.length() - 1);
                buffer.append("本次支付金额超过待安排金额");
                log.error(buffer.toString());
                return R.error(buffer.toString());
            }
        }
        JpaTransaction transaction = JpaTransaction.getTransaction();
        try {
            transaction.begin();
            JfskPayBillWfEvent event = SpringBeanUtils.getBean(JfskPayBillWfEvent.class);
            R generateResult = event.generatePayPlanBill2(docId, false);
            if (!generateResult.getResult()) {
                log.error("付款安排表拆分失败：id=" + docId + ", message=" + generateResult.getMessage());
                throw new JfskException(generateResult.getMessage());
            }
            String updateSql = "update JTGKPAYPLANBILL set DOCSTATUS=2 where ID=?1";
            log.info(updateSql + ", ?1=" + docId);
            int count = DBUtil.executeUpdateSQL(updateSql, docId);
            log.info("受影响行数：" + count);
            if (count <= 0) {
                log.error("未正确更新付款安排单的单据状态");
                throw new JfskException("未正确更新付款安排单的单据状态");
            }
            transaction.commit();
            log.info("数据库事务已提交");
            return R.ok();
        } catch (Throwable ex) {
            transaction.rollback();
            log.error("付款安排提交失败：", ex);
            return R.error(ex.getMessage());
        }
    }


            /**
             * 内部接口：付款安排保存前校验 - 再次校验不要超过待付池的剩余金额
             *
             * @param checkInfos 待检查信息
             * @return 操作结果 { result: true, message: null }
             */
    @Override
    public R save(Map<String, Object> checkInfos){
        Map<String, Object> initInfos = (Map<String, Object>)checkInfos.get("initInfos");
        List<Map<String, Object>> checkInfosList = (List<Map<String, Object>>) checkInfos.get("checkInfos");
        if (checkInfosList == null || checkInfosList.isEmpty()) {
            log.error("入参参数不正确");
            return R.error("入参参数不正确");
        }
        
        for (Map<String, Object> checkInfo : checkInfosList) {
            String logInfoId = (String) checkInfo.get("LOGINFOID");
            String amount = String.valueOf(checkInfo.get("AMOUNT"));
            String initAmount = String.valueOf(initInfos.get(logInfoId));
            if ("null".equals(initAmount)) {
                initAmount = "0";
            }

            // 查询待付池的剩余金额
            String sql = "SELECT JTGKPAYMENTINFO.REQUESTAMOUNT - coalesce((select SUM(AMOUNT) from JTGKPAYPLANDETAIL where DOCSTATUS in (0,1,2) and LOGINFOID=JTGKPAYMENTINFO.ID),0.0) - coalesce((select SUM(AMOUNT) from JTGKPAYMENTDETAIL where DOCSTATUS in (0,1,5) and PARENTID=JTGKPAYMENTINFO.ID),0.0) AS UNPAYAMOUNT FROM JTGKPAYMENTINFO WHERE ID = ?1";
            List<Map<String, Object>> rows = DBUtil.querySql(sql, logInfoId);
            if (rows == null || rows.isEmpty()) {
                log.error("未找到指定的待付池单据：id=" + logInfoId);
                return R.error("未找到指定的待付池单据：" + logInfoId);
            }
            BigDecimal unpayAmount = ((BigDecimal)rows.get(0).get("UNPAYAMOUNT")).add(new BigDecimal(initAmount)).subtract(new BigDecimal(amount));
            if (unpayAmount.compareTo(BigDecimal.ZERO) < 0) {
                log.error("待付池单据的剩余金额少于本次支付金额：id=" + logInfoId + ", amount=" + amount + ", unpayAmount=" + unpayAmount);
                return R.error("待付池单据的剩余金额少于本次支付金额：剩余待付款金额" + unpayAmount + "，本次支付金额" + amount);
            }
        }
        return R.ok("检查通过");
    }


    @Override
    public FsspResultRet submit2New(@RequestBody Map<String, Object> maps){
        Map<String, Object> data = (Map<String, Object>) maps.get("data");
        FsspResultRet result = payPlan2ProcessBillBase.fsspsubmit(data,(Map<String, String>) maps.get("PFPROCINTERACTINFO"));
        return result;
    }

    @Override
    public FsspResultRet retract2New(@RequestBody Map<String, Object> maps){
        Map<String, Object> data = (Map<String, Object>) maps.get("data");
        data.put("DQHJBH", "STARTNODE");
        //流程设计中的当前环节编号，任务ID不穿的话，此处固定
        String billid = (String)maps.get("BILLID");
        FsspResultRet result = payPlan2ProcessBillBase.fsspretract(data);

        // 审批撤回制单状态
        String updateSql = "update JTGKPAYPLANBILL2 set DOCSTATUS = '0' where ID = ?1";
        int count = DBUtil.executeUpdateSQL(updateSql, billid);
        if (count == 0) {
            throw new JfskException("goldwind", "PayPlan2ProcessBillBaseEvent-002", "付款安排复核审批更新失败", null);
        }
        return result;
    }

    @Override
    public FsspResultRet view2New(@RequestBody Map<String, Object> maps){
        FsspResultRet fsspResultRet = payPlan2ProcessBillBase.viewPro(maps);
        return fsspResultRet;
    }


    @Override
    public FsspResultRet submit3New(@RequestBody Map<String, Object> maps){
        String billId = (String) maps.get("BILLID");
        
        String querySql = "select distinct JTGKPAYPLANBILL2.ID as BILLID, JTGKPAYPLANBILL2.TXT09 as TXT09, JTGKPAYPLANBILL3.DOCNO as DOCNO from JTGKPAYPLANBILL3\n" +
                "LEFT JOIN JTGKPAYPLANDETAIL on JTGKPAYPLANDETAIL.PARENTID3 = JTGKPAYPLANBILL3.ID\n" +
                "LEFT JOIN JTGKPAYPLANBILL2 on JTGKPAYPLANBILL2.ID = JTGKPAYPLANDETAIL.PARENTID2\n" +
                "where JTGKPAYPLANBILL3.ID = ?1" ;
        List<Map<String, Object>> rows = DBUtil.querySql(querySql, billId);
        if (rows == null || rows.isEmpty()) {
            log.error("未找到指定的付款安排单：id=" + billId);
            throw new JfskException("goldwind", "JfskPayBillController-101", "未找到指定的付款安排单：id=" + billId, null);
        }
        String billId2 = rows.get(0).get("BILLID").toString();
        String txt09 = rows.get(0).get("TXT09").toString();
//        String docNo = rows.get(0).get("DOCNO").toString();

        String updateSql = "update JTGKPAYPLANBILL2 set TXT08 = '1' where ID = ?1";
        int count = DBUtil.executeUpdateSQL(updateSql, billId2);
        if (count == 0) {
            throw new JfskException("goldwind", "JfskPayBillController-101", "付款安排复核通过失败", null);
        }

        Map<String, Object> mapParams = new HashMap<>();
        mapParams.put("BILLID", billId2);
        mapParams.put("FORMTYPE", "JFPAYPLAN2");
        mapParams.put("PFTASKID", txt09);
        mapParams.put("PFPROCINTERACTINFO", null);
        mapParams.put("IFAUTOPASS", null);
        FsspResultRet result = payPlan2ProcessBillBase.fsPass(mapParams);
        if (result.getCode() != 0) {
            throw new JfskException("goldwind", "PayPlan3ProcessBillBaseEvent-004", "催动流程通过失败", null);
        }

        // 更新单据状态
        String updateDocStatus = "update JTGKPAYPLANBILL3 set DOCSTATUS=1 where ID=?1";
        log.info(updateDocStatus);
        int countOfDocStatus = DBUtil.executeUpdateSQL(updateDocStatus, billId);
        log.info("受影响行数：" + countOfDocStatus);

        return result;
    }


    @Override
    public FsspResultRet view3New(@RequestBody Map<String, Object> maps){
        String docId = (String) maps.get("BILLID");
        // 根据资流程联查住流程
        String querySql = "select distinct JTGKPAYPLANBILL2.ID as BILLID from JTGKPAYPLANBILL3\n" +
                "LEFT JOIN JTGKPAYPLANDETAIL on JTGKPAYPLANDETAIL.PARENTID3 = JTGKPAYPLANBILL3.ID\n" +
                "LEFT JOIN JTGKPAYPLANBILL2 on JTGKPAYPLANBILL2.ID = JTGKPAYPLANDETAIL.PARENTID2\n" +
                "where JTGKPAYPLANBILL3.ID = ?1" ;

        List<Map<String, Object>> rows = DBUtil.querySql(querySql, docId);
        if (rows == null || rows.isEmpty()) {
            log.error("未找到指定的流程：id=" + docId);
            throw new JfskException("goldwind", "JfskPayBillController-101", "未找到指定的流程：id=" + docId, null);
        }
        String billId = rows.get(0).get("BILLID").toString();
        maps.put("BILLID", billId);

        FsspResultRet fsspResultRet = payPlan2ProcessBillBase.viewPro(maps);
        return fsspResultRet;
    }


    /**
     * 内部接口：获取复核状态
     *
     *
     * @param docId 单据ID
     * @return 复核状态 { result: true, message: null, data: 0 }
     */
    @Override
    public String getDetailStatus(String docId) {
        log.info("查询复核状态：id=" + docId);
        String selectSql = "select ID,DOCNO,DOCSTATUS from JTGKPAYPLANBILL2 where ID=?1";
        log.info(selectSql + ", ?1=" + docId);
        List<Map<String, Object>> rows = DBUtil.querySql(selectSql, docId);
        log.info(JSON.toJSONString(rows));
        if (rows == null || rows.isEmpty()) {
            log.error("未找到指定的付款安排复核单：id=" + docId);
            return R.error("未找到指定的付款安排复核单：" + docId).toString();
        }
        String docStatus = rows.get(0).get("DOCSTATUS").toString();
        return RD.ok(null, docStatus).toString();
    }

    /**
     * 内部接口：复核退回
     * @param docId 复核单据ID
     * @param docNo 单据编号
     * @return 处理结果
     */
    @Override
    public R checkBack(String docId, String docNo) {
        log.info("付款安排复核退回：parentId2=" + docId);
        String lockId;
        try {
            String moduleId = "goldwind";
            String funcId = "planBack";
            String categoryId = "JfskPayBillController";
            String dataID = docId;
            String comment = "付款安排复核退回加锁防止并发操作";
            LockResult lockResult = lockService.addLock(moduleId, categoryId, dataID, new DataLockOptions(Duration.ofMinutes(30), ReplacedScope.Exclusive, LockedScope.AppInstance, Duration.ofMinutes(30)), funcId, comment);
            if (lockResult == null || !lockResult.isSuccess()) {
                log.error("付款安排复核退回加锁失败");
                return R.error("付款安排复核退回加锁失败");
            }
            lockId = lockResult.getLockId();
            log.info("付款安排复核退回加锁结果：lockId=" + lockId);
        } catch (Throwable e) {
            log.error("付款安排复核退回加锁过程发生异常：{}", e.toString());
            return R.error("付款安排复核退回加锁过程发生异常：" + e.getMessage());
        }
        R result;
        try {
            result = payBillService.checkBack(docId, docNo);
        } catch (Throwable e) {
            log.error("付款安排复核退回发生异常：" + e.toString());
            result = R.error("付款安排复核退回发生异常：" + e.getMessage());
        }
        try {
            lockService.removeLock(lockId);
            log.info("付款安排复核退回已解锁");
        } catch (Throwable e) {
            log.error("付款安排复核退回解锁过程发生异常：" + e.toString());
        }
        return result;
    }


    /**
     * 内部接口：票据补录退回
     * @param docId 复核单据ID
     * @param docNo 单据编号
     * @return 处理结果
     */
    @Override
    public R checkBillBack(String docId, String docNo) {
        log.info("票据补录核退回：parentId2=" + docId);
        String lockId;
        try {
            String moduleId = "goldwind";
            String funcId = "planBillBack";
            String categoryId = "JfskPayBillController";
            String dataID = docId;
            String comment = "票据补录核退回加锁防止并发操作";
            LockResult lockResult = lockService.addLock(moduleId, categoryId, dataID, new DataLockOptions(Duration.ofMinutes(30), ReplacedScope.Exclusive, LockedScope.AppInstance, Duration.ofMinutes(30)), funcId, comment);
            if (lockResult == null || !lockResult.isSuccess()) {
                log.error("票据补录核退回加锁失败");
                return R.error("票据补录核退回加锁失败");
            }
            lockId = lockResult.getLockId();
            log.info("票据补录核退回加锁结果：lockId=" + lockId);
        } catch (Throwable e) {
            log.error("票据补录核退回加锁过程发生异常：{}", e.toString());
            return R.error("票据补录核退回加锁过程发生异常：" + e.getMessage());
        }
        R result;
        try {
            result = payBillService.checkBillBack(docId, docNo);
        } catch (Throwable e) {
            log.error("票据补录核退回发生异常：" + e.toString());
            result = R.error("票据补录核退回发生异常：" + e.getMessage());
        }
        try {
            lockService.removeLock(lockId);
            log.info("票据补录核退回已解锁");
        } catch (Throwable e) {
            log.error("票据补录核退回解锁过程发生异常：" + e.toString());
        }
        return result;
    }

    /**
     * 内部接口：复核通过
     * @param docId 复核单据ID
     * @param docNo 单据编号
     * @return 处理结果
     */
    @Override
    public R checkPass(String docId, String docNo, String onlyCheck) {
        log.info("付款安排复核通过：id=" + docId);

        String lockId;
        try {
            String moduleId = "goldwind";
            String funcId = "planBillCheck";
            String categoryId = "JfskPayBillController";
            String dataID = docId;
            String comment = "付款安排复核通过防止并发操作";
            LockResult lockResult = lockService.addLock(moduleId, categoryId, dataID, new DataLockOptions(Duration.ofMinutes(30), ReplacedScope.Exclusive, LockedScope.AppInstance, Duration.ofMinutes(30)), funcId, comment);
            if (lockResult == null || !lockResult.isSuccess()) {
                log.error("付款安排复核通过加锁失败");
                return R.error("付款安排复核通过加锁失败");
            }
            lockId = lockResult.getLockId();
            log.info("付款安排复核通过加锁结果：lockId=" + lockId);
        } catch (Throwable e) {
            log.error("付款安排复核通过加锁过程发生异常：{}", e.toString());
            return R.error("付款安排复核通过加锁过程发生异常：" + e.getMessage());
        }
        R result;
        logService.init("K0102");
        logService.info(docNo, "付款安排复核通过：id=" + docId);
        try {
            if (onlyCheck == null || onlyCheck.equals("")) { onlyCheck = "false"; }
            result = payBillService.checkPass(logService, docId, docNo, Boolean.parseBoolean(onlyCheck));
        } catch (Throwable e) {
            log.error("付款安排复核通过发生异常：" + e.toString());
            logService.error(docNo, "付款安排复核通过发生异常：" + ExceptionUtils.getStackTrace(e));
            result = R.error("付款安排复核通过发生异常：" + e.getMessage());
        }
        logService.flush();
        try {
            lockService.removeLock(lockId);
            log.info("付款安排复核通过已解锁");
        } catch (Throwable e) {
            log.error("付款安排复核通过解锁过程发生异常：" + e.toString());
        }
        return result;
    }

    /**
     * 内部接口：分配背书票据
     * @param args { docId: 背书补录单ID, billIds: [票据ID] }
     * @return 分配结果
     */
    @Override
    public String assignBills(AssignBillsParam args) {
        log.info("背书票据自动分配：" + JSON.toJSONString(args));
        if (args == null || StringUtil.isNullOrEmpty(args.getDocId()) || args.getBillIds() == null || args.getBillIds().isEmpty()) {
            log.error("入参参数不正确");
            return RD.error("入参参数不正确").toString();
        }
        RD<AssignBillsResult> result = assignBillsService.assignBills(args.getDocId(), args.getDocNo(), args.getBillIds());
        String jsonOfResult = JSON.toJSONString(result);
        log.info("背书票据分配结果：" + jsonOfResult);
        return jsonOfResult;
    }

    /**
     * 内部接口：获取票据背书办理状态
     * @param docId 票据背书办理ID
     * @return 办理状态 { result: true, message: null, data: 0 }
     */
    @Override
    public String getHandleStatus(String docId) {
        log.info("查询票据背书办理状态：id=" + docId);
        String selectSql = "select ID,DOCNO,DOCSTATUS from JTGKPAYPLANBILL3 where ID=?1";
        log.info(selectSql + ", ?1=" + docId);
        List<Map<String, Object>> rows = DBUtil.querySql(selectSql, docId);
        log.info(JSON.toJSONString(rows));
        if (rows == null || rows.isEmpty()) {
            log.error("未找到指定的票据背书办理单：id=" + docId);
            return R.error("未找到指定的票据背书办理单：" + docId).toString();
        }
        String docStatus = rows.get(0).get("DOCSTATUS").toString();
        return RD.ok(null, docStatus).toString();
    }

    /**
     * 内部接口：票据背书办理完成
     * @param docId 票据背书办理ID
     * @param docNo 单据编号
     * @return 处理结果 { result: true, message: null }
     */
    @Override
    public R handlePass(String docId, String docNo) {
        log.info("票据背书办理完成：id=" + docId);

        String lockId;
        try {
            String moduleId = "goldwind";
            String funcId = "planBillHandle";
            String categoryId = "JfskPayBillController";
            String dataID = docId;
            String comment = "票据背书办理完成防止并发操作";
            LockResult lockResult = lockService.addLock(moduleId, categoryId, dataID, new DataLockOptions(Duration.ofMinutes(30), ReplacedScope.Exclusive, LockedScope.AppInstance, Duration.ofMinutes(30)), funcId, comment);
            if (lockResult == null || !lockResult.isSuccess()) {
                log.error("票据背书办理完成加锁失败");
                return R.error("票据背书办理完成加锁失败");
            }
            lockId = lockResult.getLockId();
            log.info("票据背书办理完成加锁结果：lockId=" + lockId);
        } catch (Throwable e) {
            log.error("票据背书办理完成加锁过程发生异常：{}", e.toString());
            return R.error("票据背书办理完成加锁过程发生异常：" + e.getMessage());
        }
        R result;
        logService.init("K0103");
        logService.info(docNo, "票据背书办理完成：id=" + docId);
        try {
            result = payBillService.handlePass(logService, docId, docNo);
        } catch (Throwable e) {
            log.error("票据背书办理完成发生异常：" + e.toString());
            logService.error(docNo, "票据背书办理完成发生异常：" + ExceptionUtils.getStackTrace(e));
            result = R.error("票据背书办理完成发生异常：" + e.getMessage());
        }
        logService.flush();
        try {
            lockService.removeLock(lockId);
            log.info("票据背书办理完成已解锁");
        } catch (Throwable e) {
            log.error("票据背书办理完成解锁过程发生异常：" + e.toString());
        }
        return result;
    }

    /**
     * 内部接口：批量复核通过 - 支持并行处理以提高性能
     * @param batchCheckRequest 批量复核请求参数
     * @return 批量处理结果 { result: true, message: null, data: [处理结果列表] }
     */
    @Override
    public RD<List<Map<String, Object>>> batchCheckPass(@RequestBody Map<String, Object> batchCheckRequest) {
        log.info("批量付款安排复核通过：" + JSON.toJSONString(batchCheckRequest));
        
        @SuppressWarnings("unchecked") 
        List<Map<String, Object>> billInfos = (List<Map<String, Object>>) batchCheckRequest.get("billInfos");
        
        if (billInfos == null || billInfos.isEmpty()) {
            log.error("批量复核请求参数不正确");
            return RD.error("批量复核请求参数不正确");
        }

        // 初始化日志服务
        logService.init("K0104");
        
        try {
            // 调用Service层的批量处理方法
            RD<List<Map<String, Object>>> result = payBillService.batchCheckPassAndSubmit(logService, billInfos);
            return result;
            
        } catch (Exception e) {
            log.error("批量处理过程中发生异常：", e);
            logService.error("BATCH", "批量处理过程中发生异常：" + ExceptionUtils.getStackTrace(e));
            return RD.error("批量处理过程中发生异常：" + e.getMessage());
        } finally {
            logService.flush();
        }
    }

}
