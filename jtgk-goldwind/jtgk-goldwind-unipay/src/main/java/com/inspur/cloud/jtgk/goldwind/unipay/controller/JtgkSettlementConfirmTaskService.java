package com.inspur.cloud.jtgk.goldwind.unipay.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.SchedulerLoggerUtil;
import com.inspur.edp.cdf.component.api.annotation.GspComponent;
import com.inspur.gs.tm.tmfnd.fsjspub.api.dto.FsspResultRet;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.commons.utils.CollectionUtils;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.lockservice.api.*;
import io.iec.edp.caf.rpc.api.service.RpcClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;

/**
 * 京票-票据业务直接确认定时任务
 *
 * <AUTHOR>
 * @date:2024.07.16
 */

@GspComponent("jtgk-Payment-Auto-Confirm-Scheduler")
@Controller
@Slf4j
public class JtgkSettlementConfirmTaskService {
    @Autowired
    private EntityManager entityManager;
    @Autowired
    private LogService logService;
    @Autowired
    private ILockService lockService;

    public static String convert2String(Object o){
        String temp = String.valueOf(o);
        if(temp.equals("null")) return "";
        return temp;
    }

    public void settlementConfirmTask() {
        String jobId = "jtgk-settlement-confirm-task-001";
        String jobName = "京票结算确认定时任务";
        String lockId;
        
        try {
            String moduleId = "JtgkSettlementConfirmTaskService";
            String funcId = "settlementConfirmTask";
            String categoryId = "String";
            String dataID = "jtgk-Payment-Auto-Confirm-Scheduler";
            String comment = jobName;
            LockResult lockResult = lockService.addLock(moduleId, categoryId, dataID, 
                    new DataLockOptions(Duration.ofMinutes(30), ReplacedScope.Exclusive, 
                            LockedScope.AppInstance, Duration.ofMinutes(30)), funcId, comment);
            if (lockResult == null || !lockResult.isSuccess()) {
                SchedulerLoggerUtil.error(jobId, jobName, "加锁失败");
                log.error(jobName + "加锁失败");
                return;
            }
            lockId = lockResult.getLockId();
            log.info(jobName + "加锁结果：lockId=" + lockId);
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, "加锁过程发生异常：" + ex.toString());
            log.error(jobName + "加锁过程发生异常：", ex);
            return;
        }
        
        String logCode = "JtgkSettlementConfirmTask";
        logService.init(logCode);
        try {
            try {
                // 获取当前时间
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd"); // 创建格式化日期对象
                String formattedDate = sdf.format(new Date()); // 格式化日期

                logService.info(logCode, "结算确认定时任务开始获取数据!");
                String sql = "SELECT distinct TMJSXX.JSDNM,TMJSXX.JSDW,TMJSXX.JSDBH,TMJSDATA.TASKID AS PFRUTASKID \n" +
                        "FROM TMJSXX \n" +
                        "LEFT JOIN BFSETTLEMENTWAY ON TMJSXX.JSFS = BFSETTLEMENTWAY.ID\n" +
                        "LEFT JOIN BFCURRENCY ON TMJSXX.JYBZ=BFCURRENCY.ID\n" +
                        "INNER JOIN TMJSDATA ON TMJSDATA.DJNM  = TMJSXX.JSDNM AND TMJSDATA.RWLX ='JSQR'\n" +
                        "WHERE TMJSXX.JSZT=4\n" +
                        "AND BFSETTLEMENTWAY.CODE IN('04')";
                Query newQuery = entityManager.createNativeQuery(sql);
                newQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
                List<Map<String, Object>> dsList = newQuery.getResultList();
                if(!CollectionUtils.isEmpty(dsList)){
                    RpcClient rpcClient = SpringBeanUtils.getBean(RpcClient.class);
                    for (Map<String, Object> ds : dsList) {
                        // 构建RPC服务参数
                        Map<String, Object> map = new HashMap<>();
                        String jsdnm = convert2String(ds.get("jsdnm"));
                        String jsdbh = convert2String(ds.get("jsdbh"));
                        String jsdw = convert2String(ds.get("jsdw"));
                        String pftaskid = convert2String(ds.get("pfrutaskid"));
                        
                        // 按文档格式设置参数
                        map.put("BILLID", jsdnm);
                        map.put("FORMTYPE", "TM_QYFKD");
                        map.put("DJBH", jsdbh);
                        map.put("DWID", jsdw);
                        map.put("PFTASKID", pftaskid);
                        map.put("NEEDCHECK", "1");
                        map.put("JZRQ", formattedDate);
                        map.put("DJNM", jsdnm);
                        map.put("CZLX", "CHECK");
                        map.put("CZID", "HXQR");
                        
                        // 构建RPC调用参数
                        String srcSuCode = "CM";
                        String listenSvc = "com.inspur.gs.tm.cm.paymentsettlementidp.api.controller.PaymentSettlementIdpController.PaymentDocJSPTOperation";
                        
                        LinkedHashMap<String, Object> invokeParam = new LinkedHashMap<>();
                        invokeParam.put("param", map);
                        
                        logService.info(logCode, "调用RPC接口入参：" + JSONObject.toJSONString(invokeParam));
                        
                        // 执行RPC调用
                        try {
                            FsspResultRet rpcResult = rpcClient.invoke(FsspResultRet.class, listenSvc, srcSuCode, invokeParam, null);
                            logService.info(logCode, "调用RPC接口出参：【" + rpcResult + "】");
                            
                            if (rpcResult != null) {
                                JSONObject returnJson = JSON.parseObject(JSON.toJSONString(rpcResult));
                                Integer code = returnJson.getInteger("code");
                                if (code != null && code == 0) {
                                    logService.info(logCode, "结算确认成功！");
                                } else {
                                    logService.info(logCode, "结算确认失败！");
                                }
                            } else {
                                logService.info(logCode, "RPC接口出参为空！");
                            }
                        } catch (Throwable ex) {
                            logService.error(logCode, "调用RPC接口异常：" + ExceptionUtils.getStackTrace(ex));
                        }
                    }
                }
                else{
                    logService.info(logCode,"结算确认定时任务未获取到数据！");
                }
            } catch (Throwable throwable) {
                logService.error(logCode, "结算确认定时任务异常：" + ExceptionUtils.getStackTrace(throwable));
            }
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, ex.toString());
            log.error("结算确认定时任务过程发生异常：", ex);
        }
        
        logService.flush();
        
        try {
            lockService.removeLock(lockId);
            log.info(jobName + "已解锁");
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, "解锁过程发生异常：" + ex);
            log.error(jobName + "解锁过程发生异常：", ex);
        }
    }
}
