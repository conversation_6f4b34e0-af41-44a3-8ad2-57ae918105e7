package com.inspur.cloud.jtgk.goldwind.unipay.tm.bizpaymentrequest;

import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.R;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPayPlanBillEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPayPlanDetailEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPaymentInfoEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.tm.GenerateResultDto;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.JfskException;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.RpcUtils;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.common.JSONSerializer;
import io.iec.edp.caf.rpc.api.service.RpcClient;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 内部接口：业务支付申请
 */
@Service
@Slf4j
public class JfskBizPayRequestServiceImpl implements JfskBizPayRequestService {
    @Autowired
    private RpcClient rpcClient;

    /**
     * 由待付池生成业务支付申请
     * @param logService 接口日志
     * @param logEntity 待付池
     * @return 生单结果
     */
    @Override
    public GenerateResultDto generate(LogService logService, JfskPaymentInfoEntity logEntity) {
        GenerateResultDto resultDto = GenerateResultDto.error(null);
        R loadResult = loadOtherDatas(logService, logEntity);
        if (!loadResult.getResult()) {
            resultDto.setResult(false);
            resultDto.setMessage(loadResult.getMessage());
            return resultDto;
        }
        CreateBizPayReqInParam zjInParam = getZJInParam(logEntity);
        return rpcGenerate(logService, logEntity.getSrcDocNo(), zjInParam);
    }

    /**
     * 由付款安排表生成业务支付申请
     * @param logService 接口日志
     * @param docNo 单据编号
     * @param jtgkPayPlanDetail 付款安排明细
     * @return 生单结果
     */
    @Override
    public GenerateResultDto generate(LogService logService, String docNo, JfskPayPlanDetailEntity jtgkPayPlanDetail, String note) {
        CreateBizPayReqInParam zjInParam = getZJInParam(docNo, jtgkPayPlanDetail, note);
        return rpcGenerate(logService, docNo, zjInParam);
    }

    /**
     * 由票据补录表生成业务支付申请
     * @param logService 接口日志
     * @param docNo 单据编号
     * @param details 付款安排明细
     * @param bills 背书票据明细
     * @return 生单结果
     */
    @Override
    public GenerateResultDto generate(LogService logService, String docNo, List<JfskPayPlanDetailEntity> details, List<JfskPayPlanBillEntity> bills, String note) {
        CreateBizPayReqInParam zjInParam = getZJInParam(docNo, details, bills, note);
        return rpcGenerate(logService, docNo, zjInParam);
    }

    /**
     * 调用产品接口生成业务支付申请
     * @param logService 接口日志
     * @param docNo 单据编号
     * @param zjInParam 产品接口入参
     * @return 生单结果
     */
    private GenerateResultDto rpcGenerate(LogService logService, String docNo, CreateBizPayReqInParam zjInParam) {
        GenerateResultDto resultDto = GenerateResultDto.error(null);
        try {
            String jsonOfRpcInParam = JSONSerializer.serialize(zjInParam);
            log.info("产品业务支付申请生单接口入参：{}", jsonOfRpcInParam);
            logService.info(docNo, "产品业务支付申请生单接口入参：" + jsonOfRpcInParam);
            LinkedHashMap<String, Object> rpcParams = new LinkedHashMap<>();
            rpcParams.put("parameter", jsonOfRpcInParam);
            String rpcResult = rpcClient.invoke(String.class, "com.inspur.gs.bp.brpc.bizpaymentrequest.common.BizPaymentRequestComponentService.generateBizPaymentWithMultiReceivers", "Brpc", rpcParams, null);
            log.info("产品业务支付申请生单接口返回结果: {}", rpcResult);
            logService.info(docNo, "产品业务支付申请生单接口返回结果: " + rpcResult);
            if (!StringUtil.isNullOrEmpty(rpcResult)) {
                CreateBizPayReqOutParam zjOutParam = JSONSerializer.deserialize(rpcResult, CreateBizPayReqOutParam.class);
                if (zjOutParam.getSuccess()) {
                    resultDto.setResult(true);
                    resultDto.setDocId(zjOutParam.getDocID());
                    resultDto.setDocNo(zjOutParam.getDocNo());
                } else {
                    resultDto.setResult(false);
                    resultDto.setMessage(zjOutParam.getErrMsg());
                }
            } else {
                resultDto.setResult(false);
                resultDto.setMessage("产品业务支付申请生单接口未返回有效结果");
            }
        } catch (Throwable ex) {
            log.error("产品业务支付申请生单接口执行异常:", ex);
            logService.error(docNo, "产品业务支付申请生单接口执行异常：" + ExceptionUtils.getStackTrace(ex));
            String message = RpcUtils.getBizErrorInException(ex);
            if (StringUtil.isNullOrEmpty(message)) {
                message = ex.toString();
            }
            resultDto.setResult(false);
            resultDto.setMessage(message);
        }
        return resultDto;
    }

    /**
     * 待付池生成业务支付申请前补齐缺失字段
     * @param logService 接口日志
     * @param logEntity 接口日志记录
     * @return 处理后的接口日志记录
     */
    private static R loadOtherDatas(LogService logService, JfskPaymentInfoEntity logEntity) {
        log.info("代付数据补齐前：" + JSON.toJSONString(logEntity));
        if (StringUtil.isNullOrEmpty(logEntity.getRequestUnitName())) {
            String selectPayUnit = "select ID,CODE,NAME_CHS from BFADMINORGANIZATION where ID=?1";
            log.info(selectPayUnit + ", ?1=" + logEntity.getRequestUnitId());
            List<Map<String, Object>> rowsOfPayUnit = DBUtil.querySql(selectPayUnit, logEntity.getRequestUnitId());
            log.info(JSON.toJSONString(rowsOfPayUnit));
            if (rowsOfPayUnit == null || rowsOfPayUnit.isEmpty()) {
                log.error("付款公司ID无效：" + logEntity.getRequestUnitId());
                logService.error(logEntity.getSrcDocNo(), "付款公司ID无效：" + logEntity.getRequestUnitId());
                return R.error("付款公司ID无效");
            }
            String payUnitName = (String) rowsOfPayUnit.get(0).get("NAME_CHS");
            logEntity.setRequestUnitName(payUnitName);
        }
        if (StringUtil.isNullOrEmpty(logEntity.getPayAccountName())) {
            String selectOfPayAcct = "select ID,ACCOUNTNO,ACCOUNTNAME_CHS,OPENACCOUNTUNIT,ACCOUNTSTATUS,ONLINEBANKOPENSTATUS from BFBANKACCOUNTS where ID=?1 and ACCOUNTSTATUS='2'";
            log.info(selectOfPayAcct + ", ?1=" + logEntity.getPayAccountId());
            List<Map<String, Object>> rowsOfPayAcct = DBUtil.querySql(selectOfPayAcct, logEntity.getPayAccountId());
            log.info(JSON.toJSONString(rowsOfPayAcct));
            if (rowsOfPayAcct == null || rowsOfPayAcct.isEmpty()) {
                log.error("付款账户ID无效：" + logEntity.getPayAccountId());
                logService.error(logEntity.getSrcDocNo(), "付款账户ID无效：" + logEntity.getPayAccountId());
                return R.error("付款账户ID无效");
            }
            String payAccountNo = (String)rowsOfPayAcct.get(0).get("ACCOUNTNO");
            logEntity.setPayAccountNo(payAccountNo);
            String payAccountName = (String)rowsOfPayAcct.get(0).get("ACCOUNTNAME_CHS");
            logEntity.setPayAccountName(payAccountName);
            if ("3".equals(String.valueOf(rowsOfPayAcct.get(0).get("ONLINEBANKOPENSTATUS")))) {
                logEntity.setIsBankCommPay(true);
            }
        }
        if (StringUtil.isNullOrEmpty(logEntity.getReceivingBankNo())) {
            String selectOppBank = "select ID,CODE,NAME_CHS,COUNTRYORREGION,PROVINCE,CITY from BFBANK where ID=?1";
            log.info(selectOppBank + ", ?1=" + logEntity.getReceivingBankId());
            List<Map<String, Object>> rowsOfOppBank = DBUtil.querySql(selectOppBank, logEntity.getReceivingBankId());
            log.info(JSON.toJSONString(rowsOfOppBank));
            if (rowsOfOppBank == null || rowsOfOppBank.isEmpty()) {
                log.error("收款银行ID无效：" + logEntity.getReceivingBankId());
                logService.error(logEntity.getSrcDocNo(), "收款银行ID无效：" + logEntity.getReceivingBankId());
                return R.error("收款银行ID无效");
            }
            String oppBankCode = (String) rowsOfOppBank.get(0).get("CODE");
            String oppBankName = (String) rowsOfOppBank.get(0).get("NAME_CHS");
            logEntity.setReceivingBankNo(oppBankCode);
            logEntity.setReceivingBankName(oppBankName);
            String country = (String)rowsOfOppBank.get(0).get("COUNTRYORREGION");
            logEntity.setReciprocalCountry(country);
            String province = (String)rowsOfOppBank.get(0).get("PROVINCE");
            logEntity.setReciprocalProvinceName(province);
            String city = (String)rowsOfOppBank.get(0).get("CITY");
            logEntity.setReciprocalCityName(city);
        }
        if (StringUtil.isNullOrEmpty(logEntity.getReceivingBankAccountNo())) {
            String selectOppAccount = "select a.ID,a.ACCOUNTCODE,a.ACCOUNTNAME_CHS,b.ID as BANKID,b.BANKIDENTIFIER,b.NAME_CHS as BANKNAME from BFPARTNERBANKACCOUNTS a inner join BFBANK b on a.INBANK=b.ID where a.ID=?1";
            log.info(selectOppAccount + ", ?1=" + logEntity.getReceivingBankAccountId());
            List<Map<String, Object>> rowsOfOppAccount = DBUtil.querySql(selectOppAccount, logEntity.getReceivingBankAccountId());
            log.info(JSON.toJSONString(rowsOfOppAccount));
            if (rowsOfOppAccount == null || rowsOfOppAccount.isEmpty()) {
                log.error("供应商银行账户ID无效：" + logEntity.getReceivingBankAccountId());
                logService.error(logEntity.getSrcDocNo(), "供应商银行账户ID无效：" + logEntity.getReceivingBankAccountId());
                return R.error("供应商银行账户ID无效");
            }
            String oppAccountNo = (String) rowsOfOppAccount.get(0).get("ACCOUNTCODE");
            String oppAccountName = (String) rowsOfOppAccount.get(0).get("ACCOUNTNAME_CHS");
            logEntity.setReceivingBankAccountNo(oppAccountNo);
            logEntity.setReceivingBankAccountName(oppAccountName);
        }
        log.info("代付数据补齐后：" + JSON.toJSONString(logEntity));
        return R.ok();
    }

    /**
     * 待付池记录转换为资金产品接口入参形式
     * @param dto 待付池记录
     * @return 资金RPC接口入参
     */
    public static CreateBizPayReqInParam getZJInParam(JfskPaymentInfoEntity dto) {
        log.info("待封装的付款申请信息：{}", JSONSerializer.serialize(dto));
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //业务支付申请
        CreateBizPayReqInParam inParam = new CreateBizPayReqInParam();
        inParam.setRequestID(dto.getSrcBizSys() + "_" + dto.getSrcDocId());//请求ID
        inParam.setPaymentBasis(3);
        inParam.setRequestUnit(dto.getRequestUnitId());//单位编号内码
        inParam.setRequestUnitName(dto.getRequestUnitName());//单位编号名称
        //inParam.setPayerSwiftCode();//付款人SWIFT码
        inParam.setPayAccount(dto.getPayAccountId());//付款账号内码
        inParam.setPayAccountNo(dto.getPayAccountNo());//付款账号
        inParam.setPayAccountName(dto.getPayAccountName());//付款户名
        //if (dto.getCashFlowItemCode() != null && !dto.getCashFlowItemCode().isEmpty()) {
        //    inParam.setCashFlowItem(dto.getCashFlowItemId());//现金流量项目********
        //}
        inParam.setExpSettleWay(dto.getSettlementWayId());//期望结算方式
        //if (dto.getSettlewayCode() != null && !dto.getSettlewayCode().isEmpty()) {
        //    inParam.setExpSettleWay(dto.getExpSettleWay());//期望结算方式
        //}
        //if (dto.getBizItemCode() != null && !dto.getBizItemCode().isEmpty()) {
        //    inParam.setBizItem(dto.getBizItemId());//业务事项
        //}
        if (!StringUtil.isNullOrEmpty(dto.getExpectPayDate())) {
            try {
                Date expPayDate = sdf.parse(dto.getExpectPayDate());
                inParam.setExpPayDate(expPayDate);//期望付款日期
            } catch (Throwable ex) {
                log.error("期望付款日期无效", ex);
            }
        } else {
            inParam.setExpPayDate(new Date());
        }
        inParam.setIsUrgent(false);//是否加急
        if (StringUtils.isBlank(dto.getTransCurrencyId()) || StringUtils.equals(dto.getCurrencyId(), dto.getTransCurrencyId())) {
            inParam.setCurrency(dto.getCurrencyId());//币种
        }else{
            inParam.setCurrency(dto.getTransCurrencyId());//币种
        }
        if (StringUtils.isBlank(dto.getTransCurrencyId()) || StringUtils.equals(dto.getCurrencyId(), dto.getTransCurrencyId())) {
            inParam.setRequestAmount(dto.getRequestAmount());//申请金额
        }else{
            inParam.setRequestAmount(dto.getTransAmount());//申请金额
        }
        if (StringUtil.isNullOrEmpty(dto.getTransCurrencyId())) {
            inParam.setTransCurrency(dto.getCurrencyId());
            inParam.setTransExchangeRate(BigDecimal.ONE);
            inParam.setTransAmount(dto.getRequestAmount());
        } else {
            if (StringUtils.isBlank(dto.getTransCurrencyId()) || StringUtils.equals(dto.getCurrencyId(), dto.getTransCurrencyId())) {
                inParam.setTransCurrency(dto.getTransCurrencyId());//交易币种
            }else{
                inParam.setTransCurrency(dto.getCurrencyId());//交易币种
            }
            inParam.setTransExchangeRate(dto.getTransExchangeRate());//交易币种汇率
            if (StringUtils.isBlank(dto.getTransCurrencyId()) || StringUtils.equals(dto.getCurrencyId(), dto.getTransCurrencyId())) {
                inParam.setTransAmount(dto.getTransAmount());//交易币种金额
            }else{
                inParam.setTransAmount(dto.getRequestAmount());//交易币种金额
            }
        }
        inParam.setPrivateFlag(1);//对方性质	1：往来单位，2：内部员工
        inParam.setIsdeduction(false);//是否主扣
        inParam.setSummary(dto.getSummary());//摘要(去掉非法字符)
        inParam.setDescription(dto.getDescription());//详细说明
        inParam.setSrcBizSys(dto.getSrcBizSys());//来源业务系统
        inParam.setSrcBizType(null);
        inParam.setExpenseDocType(0);
        //主扣时(9：单位主扣；13：资金中心主扣)实际付款日期必填，设置为期望付款日期
        inParam.setActualPayDate(null);
//        inParam.setSrcDocType(dto.getSrcDocType());//来源单据类型主键(来源业务系统_共享单据类型)
        inParam.setSrcDocType("BizPaymentRequest");
        inParam.setRequestUser(dto.getApplicantId());//申请人
        inParam.setRequestUserName(dto.getApplicantName());//申请人名称
        if (!StringUtil.isNullOrEmpty(dto.getExpectPayDate())) {
            //申请日期
            try {
                Date applyDate = sdf.parse(dto.getExpectPayDate());
                inParam.setRequestDate(applyDate);
            } catch (Throwable ex) {
                log.error("期望付款日期无效", ex);
            }
        } else {
            inParam.setRequestDate(new Date());
        }
        //付款用途
        //inParam.setFundsUse(dto.getFundsUseId());
        inParam.setIsSharingProcess(false);
        inParam.setIsBRPCProcess(true);
        inParam.setStartProcess(true);
        //业务收付类型
//        inParam.setExtBizType("Nomal");
        inParam.setExtBizType("439eee73-52c4-b7a9-f6a7-2aab0e9373a5");
        inParam.setDocBizType("JYXFK");
//        inParam.setSrcDocID(dto.getSrcDocId());
        inParam.setSrcDocID(dto.getId());
        inParam.setSrcDocNo(dto.getSrcDocNo());
//        inParam.setAccountingUnit(dto.getRequestUnitId());
        inParam.setPayUnit(dto.getRequestUnitId());
        inParam.setPayUnitName(dto.getRequestUnitName());

        //业务明细
        List<BizPayReqDetail> reqDetails = new ArrayList<>();
        inParam.setBizPayReqDetails(reqDetails);
        BizPayReqDetail bizPayReqDetail = new BizPayReqDetail();
        reqDetails.add(bizPayReqDetail);
        bizPayReqDetail.setBizItem(null);
        bizPayReqDetail.setFundNature(dto.getFundnatureId());
        bizPayReqDetail.setFundPlanIds(null);
        bizPayReqDetail.setFundPlanNos(null);
        bizPayReqDetail.setExpSettleWay(dto.getSettlementWayId());
        bizPayReqDetail.setCashFlowItem(null);
        bizPayReqDetail.setCurrency(dto.getCurrencyId());
        bizPayReqDetail.setRequestAmount(dto.getRequestAmount());//申请金额
        bizPayReqDetail.setDescription(dto.getDescription());//详细说明
        bizPayReqDetail.setSrcDocID("C" + dto.getSrcDocId());//来源单据主键
        bizPayReqDetail.setSrcDocNo(dto.getSrcDocNo());//来源单据编号
//        bizPayReqDetail.setSrcBizID(dto.getSrcDocId());//来源业务主键
        bizPayReqDetail.setSrcBizID(dto.getId());//来源业务主键
        if (!StringUtil.isNullOrEmpty(dto.getReceivingUnitId())) {
            bizPayReqDetail.setReceivingUnit(dto.getReceivingUnitId());
            bizPayReqDetail.setReceivingUnitName(dto.getReceivingUnitName());//收款单位名称
        }

        //业务支付申请收款方信息
        List<BizPayReqReceiver> receivers = new ArrayList<>();
        inParam.setBizPayReqReceivers(receivers);
        BizPayReqReceiver bizPayReqReceiver = new BizPayReqReceiver();
        receivers.add(bizPayReqReceiver);
        bizPayReqReceiver.setID(UUID.randomUUID().toString());

        // 新增内容
        bizPayReqReceiver.setTXT05(dto.getSrcDocNo());
        bizPayReqReceiver.setTXT07(dto.getSrcBizSys());
//        bizPayReqReceiver.setTXT08();
        bizPayReqReceiver.setTXT09(dto.getSrcDocId());
//        bizPayReqReceiver.setTXT10(dto.getLinkUrl());
        bizPayReqReceiver.setTXT10(dto.getApplicantName());
        bizPayReqReceiver.setTXT11(dto.getId());

        bizPayReqReceiver.setPayAccount(dto.getPayAccountId());
        bizPayReqReceiver.setPayAccountName(dto.getPayAccountName());
        bizPayReqReceiver.setPayAccountNo(dto.getPayAccountNo());
        bizPayReqReceiver.setIsBankCommPay(dto.getIsBankCommPay());
        // ******** 未传入往来单位编号时不保存往来单位名称，因为退汇认领时做了校验
        if (!StringUtil.isNullOrEmpty(dto.getReceivingUnitId())) {
            bizPayReqReceiver.setReceivingUnit(dto.getReceivingUnitId());
            bizPayReqReceiver.setReceivingUnitName(dto.getReceivingUnitName());//收款单位名称
        }
        bizPayReqReceiver.setReceivingAccount(dto.getReceivingBankAccountId());//收款银行账号
        bizPayReqReceiver.setReceivingAccountNo(dto.getReceivingBankAccountNo());//收款银行账号
        bizPayReqReceiver.setReceivingAccountName(dto.getReceivingBankAccountName());//收款户名
        bizPayReqReceiver.setReceivingAccountBank(dto.getReceivingBankId());//收款银行
        bizPayReqReceiver.setReceivingAccountBankName(dto.getReceivingBankName());//收款银行名称
        bizPayReqReceiver.setReciprocalCountry(null);//对方国家
        bizPayReqReceiver.setReciprocalProvinceName(null);//对方省份名称
        bizPayReqReceiver.setReciprocalCityName(null);//对方城市名称
        if (!StringUtil.isNullOrEmpty(dto.getExpectPayDate())) {
            try {
                Date expPayDate = sdf.parse(dto.getExpectPayDate());
                bizPayReqReceiver.setExpPayDate(expPayDate);//期望付款日期
            } catch (Throwable ex) {
                log.error("期望付款日期无效", ex);
            }
        } else {
            bizPayReqReceiver.setExpPayDate(new Date());//期望付款日期
        }
        //对方性质	1：往来单位，2：内部员工
        bizPayReqReceiver.setPrivateFlag(1);
        //是否对私账户
        bizPayReqReceiver.setIsPrivateAccount(false);
        if (StringUtils.isBlank(dto.getTransCurrencyId()) || StringUtils.equals(dto.getCurrencyId(), dto.getTransCurrencyId())) {
            bizPayReqReceiver.setCurrency(dto.getCurrencyId());//币种
        }else{
            bizPayReqReceiver.setCurrency(dto.getTransCurrencyId());//币种
        }
        if (StringUtils.isBlank(dto.getTransCurrencyId()) || StringUtils.equals(dto.getCurrencyId(), dto.getTransCurrencyId())) {
            bizPayReqReceiver.setRequestAmount(dto.getRequestAmount());//申请金额
        }else{
            bizPayReqReceiver.setRequestAmount(dto.getTransAmount());//申请金额
        }
        if (StringUtil.isNullOrEmpty(dto.getTransCurrencyId())) {
            bizPayReqReceiver.setTransCurrency(dto.getCurrencyId());//币种
            bizPayReqReceiver.setTransExchangeRate(BigDecimal.ONE);//********
            bizPayReqReceiver.setTransAmount(dto.getRequestAmount());//********
        } else {
            if (StringUtils.isBlank(dto.getTransCurrencyId()) || StringUtils.equals(dto.getCurrencyId(), dto.getTransCurrencyId())) {
                bizPayReqReceiver.setTransCurrency(dto.getTransCurrencyId());
            }else{
                bizPayReqReceiver.setTransCurrency(dto.getCurrencyId());
            }
            if (StringUtils.isBlank(dto.getTransCurrencyId()) || StringUtils.equals(dto.getCurrencyId(), dto.getTransCurrencyId())) {
                bizPayReqReceiver.setTransAmount(dto.getTransAmount());
            }else{
                bizPayReqReceiver.setTransAmount(dto.getRequestAmount());
            }
            bizPayReqReceiver.setTransExchangeRate(dto.getTransExchangeRate());
        }
        bizPayReqReceiver.setSummary(dto.getSummary());//摘要
        bizPayReqReceiver.setDescription(dto.getDescription());//详细说明
        //********
//        bizPayReqReceiver.setSrcDocDetailID(dto.getSrcDocId());
        bizPayReqReceiver.setSrcDocDetailID(dto.getId());

        String billPayWaySQL = "SELECT distinct JTGKPAYPLANBILL.BILLPAYWAY FROM JTGKPAYPLANBILL inner join JTGKPAYPLANDETAIL on JTGKPAYPLANBILL.ID = JTGKPAYPLANDETAIL.PARENTID where JTGKPAYPLANDETAIL.ID = '" + dto.getId() + "'";
        List<Map<String, Object>> rowsOfBillPayWay = DBUtil.querySql(billPayWaySQL);
        if (rowsOfBillPayWay != null && !rowsOfBillPayWay.isEmpty()) {
            dto.setBillPayWay(Integer.parseInt(rowsOfBillPayWay.get(0).get("BILLPAYWAY").toString()));
        }

        bizPayReqReceiver.setBILLPAYWAY(dto.getBillPayWay());
        if (null != dto.getBillPayWay() && 1 == dto.getBillPayWay()) {
            bizPayReqReceiver.setNEWBILLFLAG('1');
        }
        bizPayReqReceiver.setSUPPLYCHAINPRODUCTS(dto.getSupplyChainProducts());
        bizPayReqReceiver.setExpSettleWay(dto.getSettlementWayId());
        bizPayReqReceiver.setIsUrgent(false);//是否加急
        bizPayReqReceiver.setBizItem(null);
        bizPayReqReceiver.setCashFlowItem(null);
        // 20250327自定义字段
        bizPayReqReceiver.setTXT01(dto.getTxt01());
        bizPayReqReceiver.setTXT02(dto.getTxt02());
        bizPayReqReceiver.setTXT03(dto.getTxt03());
        bizPayReqReceiver.setTXT04(dto.getSrcPayMethodName());
//        bizPayReqReceiver.setTXT05(dto.getTxt05());
        bizPayReqReceiver.setTXT06(dto.getTxt06());
//        bizPayReqReceiver.setTXT07(dto.getTxt07());
//        bizPayReqReceiver.setTXT08(dto.getTxt08());
//        bizPayReqReceiver.setTXT09(dto.getTxt09());
//        bizPayReqReceiver.setTXT10(dto.getTxt10());
//        bizPayReqReceiver.setTXT11(dto.getTxt11());
        bizPayReqReceiver.setTXT12(dto.getTxt12());
        bizPayReqReceiver.setTXT13(dto.getTxt13());
        bizPayReqReceiver.setTXT14(dto.getTxt14());
        bizPayReqReceiver.setTXT15(dto.getTxt15());
        bizPayReqReceiver.setTXT16(dto.getTxt16());
        bizPayReqReceiver.setTXT17(dto.getTxt17());
        bizPayReqReceiver.setTXT18(dto.getTxt18());
        bizPayReqReceiver.setTXT19(dto.getTxt19());
        bizPayReqReceiver.setTXT20(dto.getTxt20());
        bizPayReqReceiver.setFK02(dto.getExtPurchaseCode());
        bizPayReqReceiver.setFK03(dto.getExtCostCenter());
        bizPayReqReceiver.setFK04(dto.getExtProjectCode());
        bizPayReqReceiver.setFK05(dto.getExtInnerOrder());
        bizPayReqReceiver.setFK06(dto.getExtProfitCenter());
        String json = JSONSerializer.serialize(inParam);
        log.info("产品接口入参封装完成：{}", json);
        return inParam;
    }

    /**
     * 付款安排支付明细转换为资金产品接口入参形式
     * @param dto 付款安排支付明细
     * @return 资金RPC接口入参
     */
    private static CreateBizPayReqInParam getZJInParam(String docNo, JfskPayPlanDetailEntity dto, String note) {
        log.info("待封装的付款安排支付明细：{}", JSONSerializer.serialize(dto));
        String srcBizSys = "jtgkPayPlan";
        //业务支付申请
        CreateBizPayReqInParam inParam = new CreateBizPayReqInParam();
        inParam.setID(UUID.randomUUID().toString());
        inParam.setRequestID(srcBizSys + "_" + dto.getId());//请求ID
        inParam.setPaymentBasis(3);
        inParam.setRequestUnit(dto.getPayUnitId());//单位编号内码
        inParam.setRequestUnitName(dto.getPayUnitName());//单位编号名称
        //inParam.setPayerSwiftCode();//付款人SWIFT码
        inParam.setPayAccount(dto.getPayAccountId());//付款账号内码
        inParam.setPayAccountNo(dto.getPayAccountNo());//付款账号
        inParam.setPayAccountName(dto.getPayAccountName());//付款户名
        //if (dto.getCashFlowItemCode() != null && !dto.getCashFlowItemCode().isEmpty()) {
        //    inParam.setCashFlowItem(dto.getCashFlowItemId());//现金流量项目********
        //}
        inParam.setExpSettleWay(dto.getSettlementWayId());//期望结算方式
        //if (dto.getBizItemCode() != null && !dto.getBizItemCode().isEmpty()) {
        //    inParam.setBizItem(dto.getBizItemId());//业务事项
        //}
        if (null != dto.getExpectPayDate()) {
            inParam.setExpPayDate(dto.getExpectPayDate());//期望付款日期
        } else {
            inParam.setExpPayDate(new Date());
        }
        inParam.setIsUrgent(false);//是否加急
        if (StringUtils.isBlank(dto.getTransCurrencyId()) || StringUtils.equals(dto.getCurrencyId(), dto.getTransCurrencyId())) {
            inParam.setCurrency(dto.getCurrencyId());//币种
        }else{
            inParam.setCurrency(dto.getTransCurrencyId());//币种
        }
        if (StringUtils.isBlank(dto.getTransCurrencyId()) || StringUtils.equals(dto.getCurrencyId(), dto.getTransCurrencyId())) {
            inParam.setRequestAmount(dto.getAmount());//申请金额
        }else{
            inParam.setRequestAmount(dto.getTransAmount());
        }
        if (StringUtil.isNullOrEmpty(dto.getTransCurrencyId())) {
            inParam.setTransCurrency(dto.getCurrencyId());
            inParam.setTransExchangeRate(BigDecimal.ONE);
            inParam.setTransAmount(dto.getAmount());
        } else {
            if (StringUtils.isBlank(dto.getTransCurrencyId()) || StringUtils.equals(dto.getCurrencyId(), dto.getTransCurrencyId())) {
                inParam.setTransCurrency(dto.getTransCurrencyId());//交易币种
            }else{
                inParam.setTransCurrency(dto.getCurrencyId());//交易币种
            }
            inParam.setTransExchangeRate(dto.getTransExchangeRate());//交易币种汇率
            if (StringUtils.isBlank(dto.getTransCurrencyId()) || StringUtils.equals(dto.getCurrencyId(), dto.getTransCurrencyId())) {
                inParam.setTransAmount(dto.getTransAmount());//交易币种金额
            }else{
                inParam.setTransAmount(dto.getAmount());//交易币种金额
            }
        }
        inParam.setPrivateFlag(1);//对方性质	1：往来单位，2：内部员工
        inParam.setIsdeduction(false);//是否主扣
        // 20250328 拆分多次相同金额做企业付款单时会疑似重复付款提示
        inParam.setSummary(dto.getSummary());//摘要(去掉非法字符)
        inParam.setDescription(dto.getDescription());//详细说明
        inParam.setSrcBizSys(dto.getSrcBizSys());//来源业务系统
        inParam.setSrcBizType(null);
        inParam.setExpenseDocType(0);
        //主扣时(9：单位主扣；13：资金中心主扣)实际付款日期必填，设置为期望付款日期
        inParam.setActualPayDate(null);
//        inParam.setSrcDocType(dto.getSrcDocType());//来源单据类型主键(来源业务系统_共享单据类型)
        inParam.setSrcDocType("BizPaymentRequest");
        // String userId = CAFContext.current.getUserId();
        // String userName = CAFContext.current.getCurrentSession().getUserName();

        // 获取申请人
        String userId = "";
        String userName = "";
        String sqlOfUser = "SELECT GSPUSER.ID as USERID, JTGKPAYMENTINFO.APPLICANTNAME as USERNAME FROM JTGKPAYMENTINFO LEFT JOIN JTGKPAYPLANDETAIL on JTGKPAYMENTINFO.ID = JTGKPAYPLANDETAIL.LOGINFOID LEFT JOIN GSPUSER on JTGKPAYMENTINFO.APPLICANTCODE = GSPUSER.CODE WHERE JTGKPAYPLANDETAIL.ID = '" + dto.getId() + "'";
        List<Map<String, Object>> rowsOfUser = DBUtil.querySql(sqlOfUser);
        if (rowsOfUser != null && !rowsOfUser.isEmpty()) {
            userId = rowsOfUser.get(0).get("USERID").toString();
            userName = rowsOfUser.get(0).get("USERNAME").toString();
        }else{
            throw new JfskException("goldwind-unipay-tm", "CreateBizPayReqInParam", "获取申请人信息失败", null);
        }

        inParam.setRequestUser(userId);//申请人
        inParam.setRequestUserName(userName);//申请人名称
        if (null != dto.getExpectPayDate()) {
            inParam.setRequestDate(dto.getExpectPayDate());
        } else {
            inParam.setRequestDate(new Date());
        }
        //付款用途
        //inParam.setFundsUse(dto.getFundsUseId());
        inParam.setIsSharingProcess(false);
        inParam.setIsBRPCProcess(true);
        inParam.setStartProcess(true);
        //业务收付类型
//        inParam.setExtBizType("Nomal");
        inParam.setExtBizType("439eee73-52c4-b7a9-f6a7-2aab0e9373a5");
        inParam.setDocBizType("JYXFK");
        inParam.setSrcDocID(dto.getId());
        inParam.setSrcDocNo(dto.getSrcDocNo());
//        inParam.setAccountingUnit(dto.getPayUnitId());
        inParam.setPayUnit(dto.getPayUnitId());
        inParam.setPayUnitName(dto.getPayUnitName());

        //业务明细
        List<BizPayReqDetail> reqDetails = new ArrayList<>();
        inParam.setBizPayReqDetails(reqDetails);
        BizPayReqDetail bizPayReqDetail = new BizPayReqDetail();
        reqDetails.add(bizPayReqDetail);
        bizPayReqDetail.setBizItem(null);
        bizPayReqDetail.setFundNature(dto.getFundnatureId());
        bizPayReqDetail.setFundPlanIds(null);
        bizPayReqDetail.setFundPlanNos(null);
        bizPayReqDetail.setExpSettleWay(dto.getSettlementWayId());
        bizPayReqDetail.setCashFlowItem(null);
        bizPayReqDetail.setCurrency(dto.getCurrencyId());
        bizPayReqDetail.setRequestAmount(dto.getAmount());//申请金额
        bizPayReqDetail.setDescription(dto.getDescription());//详细说明
        bizPayReqDetail.setSrcDocID(dto.getId());//来源单据主键
        bizPayReqDetail.setSrcDocNo(dto.getSrcDocNo());//来源单据编号
        bizPayReqDetail.setSrcBizID(dto.getId());//来源业务主键
        if (!StringUtil.isNullOrEmpty(dto.getReceivingUnitId())) {
            bizPayReqDetail.setReceivingUnit(dto.getReceivingUnitId());
            bizPayReqDetail.setReceivingUnitName(dto.getReceivingUnitName());//收款单位名称
        }

        //业务支付申请收款方信息
        List<BizPayReqReceiver> receivers = new ArrayList<>();
        inParam.setBizPayReqReceivers(receivers);
        BizPayReqReceiver bizPayReqReceiver = new BizPayReqReceiver();
        receivers.add(bizPayReqReceiver);
        bizPayReqReceiver.setID(UUID.randomUUID().toString());

        // 新增内容
        bizPayReqReceiver.setTXT05(dto.getSrcDocNo());
        bizPayReqReceiver.setTXT07(dto.getSrcBizSys());
        bizPayReqReceiver.setTXT08(docNo);
        bizPayReqReceiver.setTXT09(dto.getSrcDocId());
        bizPayReqReceiver.setTXT10(userName);

        // 获取linkurl
        String sql = "SELECT JTGKPAYPLANBILL.ID as ID FROM JTGKPAYPLANBILL LEFT JOIN JTGKPAYPLANDETAIL on JTGKPAYPLANBILL.ID = JTGKPAYPLANDETAIL.PARENTID WHERE JTGKPAYPLANDETAIL.ID = '" + dto.getId() + "'";
        List<Map<String, Object>> rowsOfLinkUrl = DBUtil.querySql(sql);
        if (rowsOfLinkUrl != null && !rowsOfLinkUrl.isEmpty()) {
//            bizPayReqReceiver.setTXT10(rowsOfLinkUrl.get(0).get("LINKURL").toString());
            bizPayReqReceiver.setTXT11(rowsOfLinkUrl.get(0).get("ID").toString());
        }

        bizPayReqReceiver.setPayAccount(dto.getPayAccountId());
        bizPayReqReceiver.setPayAccountName(dto.getPayAccountName());
        bizPayReqReceiver.setPayAccountNo(dto.getPayAccountNo());
        bizPayReqReceiver.setIsBankCommPay(null);
        // ******** 未传入往来单位编号时不保存往来单位名称，因为退汇认领时做了校验
        if (!StringUtil.isNullOrEmpty(dto.getReceivingUnitId())) {
            bizPayReqReceiver.setReceivingUnit(dto.getReceivingUnitId());
            bizPayReqReceiver.setReceivingUnitName(dto.getReceivingUnitName());//收款单位名称
        }
        bizPayReqReceiver.setReceivingAccount(dto.getReceivingBankAccountId());//收款银行账号
        bizPayReqReceiver.setReceivingAccountNo(dto.getReceivingBankAccountNo());//收款银行账号
        bizPayReqReceiver.setReceivingAccountName(dto.getReceivingBankAccountName());//收款户名
        bizPayReqReceiver.setReceivingAccountBank(dto.getReceivingBankId());//收款银行
        bizPayReqReceiver.setReceivingAccountBankName(dto.getReceivingBankName());//收款银行名称
        bizPayReqReceiver.setReciprocalCountry(null);//对方国家
        bizPayReqReceiver.setReciprocalProvinceName(null);//对方省份名称
        bizPayReqReceiver.setReciprocalCityName(null);//对方城市名称
        if (null != dto.getExpectPayDate()) {
            bizPayReqReceiver.setExpPayDate(dto.getExpectPayDate());//期望付款日期
        } else {
            bizPayReqReceiver.setExpPayDate(new Date());//期望付款日期
        }
        //对方性质	1：往来单位，2：内部员工
        bizPayReqReceiver.setPrivateFlag(1);
        //是否对私账户
        bizPayReqReceiver.setIsPrivateAccount(false);
        if (StringUtils.isBlank(dto.getTransCurrencyId()) || StringUtils.equals(dto.getCurrencyId(), dto.getTransCurrencyId())) {
            bizPayReqReceiver.setCurrency(dto.getCurrencyId());//币种
        }else{
            bizPayReqReceiver.setCurrency(dto.getTransCurrencyId());//币种
        }
        if (StringUtils.isBlank(dto.getTransCurrencyId()) || StringUtils.equals(dto.getCurrencyId(), dto.getTransCurrencyId())) {
            bizPayReqReceiver.setRequestAmount(dto.getAmount());//申请金额
        }else{
            bizPayReqReceiver.setRequestAmount(dto.getTransAmount());//申请金额
        }
        if (!StringUtil.isNullOrEmpty(dto.getTransCurrencyId())) {
            bizPayReqReceiver.setTransCurrency(dto.getTransCurrencyId());//********
            bizPayReqReceiver.setTransAmount(dto.getTransAmount());//********
            bizPayReqReceiver.setTransExchangeRate(dto.getTransExchangeRate());//********
        } else {
            if (StringUtils.isBlank(dto.getTransCurrencyId()) || StringUtils.equals(dto.getCurrencyId(), dto.getTransCurrencyId())) {
                bizPayReqReceiver.setTransCurrency(dto.getTransCurrencyId());
            }else{
                bizPayReqReceiver.setTransCurrency(dto.getCurrencyId());
            }
            if (StringUtils.isBlank(dto.getTransCurrencyId()) || StringUtils.equals(dto.getCurrencyId(), dto.getTransCurrencyId())) {
                bizPayReqReceiver.setTransAmount(dto.getTransAmount());
            }else{
                bizPayReqReceiver.setTransAmount(dto.getAmount());
            }
            bizPayReqReceiver.setTransExchangeRate(dto.getTransExchangeRate());
        }
//        bizPayReqReceiver.setSummary(dto.getSummary());//摘要
        bizPayReqReceiver.setSummary("SFS-" + dto.getSrcDocNo() + "-" + docNo);
//        bizPayReqReceiver.setDescription(dto.getDescription());//详细说明
        bizPayReqReceiver.setDescription(note);
        //********
        bizPayReqReceiver.setSrcDocDetailID(dto.getId());
        //******** 由产品保存票据支付明细

        String billPayWaySQL = "SELECT distinct JTGKPAYPLANBILL.BILLPAYWAY FROM JTGKPAYPLANBILL inner join JTGKPAYPLANDETAIL on JTGKPAYPLANBILL.ID = JTGKPAYPLANDETAIL.PARENTID where JTGKPAYPLANDETAIL.ID = '" + dto.getId() + "'";
        List<Map<String, Object>> rowsOfBillPayWay = DBUtil.querySql(billPayWaySQL);
        if (rowsOfBillPayWay != null && !rowsOfBillPayWay.isEmpty() && !StringUtil.isNullOrEmpty(String.valueOf(rowsOfBillPayWay.get(0).get("BILLPAYWAY"))) && !"null".equals(String.valueOf(rowsOfBillPayWay.get(0).get("BILLPAYWAY")))) {
            dto.setBillPayWay(Integer.parseInt(String.valueOf(rowsOfBillPayWay.get(0).get("BILLPAYWAY"))));
        }

        bizPayReqReceiver.setBILLPAYWAY(dto.getBillPayWay());
        if (null != dto.getBillPayWay() && 1 == dto.getBillPayWay()) {
            bizPayReqReceiver.setNEWBILLFLAG('1');
        }
        bizPayReqReceiver.setSUPPLYCHAINPRODUCTS(null);
        bizPayReqReceiver.setExpSettleWay(dto.getSettlementWayId());
        bizPayReqReceiver.setIsUrgent(false);//是否加急
        bizPayReqReceiver.setBizItem(null);
        bizPayReqReceiver.setCashFlowItem(null);
        // 20250327自定义字段
        bizPayReqReceiver.setTXT01(dto.getTxt01());
        bizPayReqReceiver.setTXT02(dto.getTxt02());
        bizPayReqReceiver.setTXT03(dto.getTxt03());
        bizPayReqReceiver.setTXT04(dto.getSrcPayMethodName());
        // bizPayReqReceiver.setTXT05(dto.getTxt05());
        bizPayReqReceiver.setTXT06(dto.getTxt06());
//        bizPayReqReceiver.setTXT07(dto.getTxt07());
//        bizPayReqReceiver.setTXT08(dto.getTxt08());
//        bizPayReqReceiver.setTXT09(dto.getTxt09());
//         bizPayReqReceiver.setTXT10(dto.getTxt10());
        // bizPayReqReceiver.setTXT11(dto.getTxt11());
        bizPayReqReceiver.setTXT12(dto.getTxt12());
        bizPayReqReceiver.setTXT13(dto.getTxt13());
        bizPayReqReceiver.setTXT14(dto.getTxt14());
        bizPayReqReceiver.setTXT15(dto.getTxt15());
        bizPayReqReceiver.setTXT16(dto.getTxt16());
        bizPayReqReceiver.setTXT17(dto.getTxt17());
        bizPayReqReceiver.setTXT18(dto.getTxt18());
        bizPayReqReceiver.setTXT19(dto.getTxt19());
        bizPayReqReceiver.setTXT20(dto.getTxt20());
        bizPayReqReceiver.setFK02(dto.getExtPurchaseCode());
        bizPayReqReceiver.setFK03(dto.getExtCostCenter());
        bizPayReqReceiver.setFK04(dto.getExtProjectCode());
        bizPayReqReceiver.setFK05(dto.getExtInnerOrder());
        bizPayReqReceiver.setFK06(dto.getExtProfitCenter());
        bizPayReqReceiver.setTime01(dto.getBillDueDate());

        String json = JSONSerializer.serialize(inParam);
        log.info("产品接口入参封装完成：{}", json);
        return inParam;
    }

    /**
     * 票据补录支付明细转换为资金产品接口入参形式
     * @param details 付款安排支付明细
     * @param bills 票据明细
     * @return 资金RPC接口入参
     */
    private static CreateBizPayReqInParam getZJInParam(String docNo, List<JfskPayPlanDetailEntity> details, List<JfskPayPlanBillEntity> bills, String note) {
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        log.info("待封装的付款安排支付明细：{}", JSONSerializer.serialize(details));
        log.info("带封装的付款安排票据明细：{}", JSONSerializer.serialize(bills));
        String srcBizSys = "jtgkPayPlan";
        BigDecimal totalAmount = BigDecimal.ZERO;
        StringBuilder description = new StringBuilder();
        description.append("来源单据：");
        for (int i=0; i<details.size(); i++) {
            JfskPayPlanDetailEntity detail = details.get(i);
            totalAmount = totalAmount.add(detail.getUseAmount());
            description.append(detail.getSrcDocNo()).append(":")
                    .append(detail.getUseAmount().setScale(2).toPlainString())
                    .append("元");
            if (i<details.size()-1) {
                description.append(",");
            }
        }
        JfskPayPlanDetailEntity dto = details.get(0);
        //业务支付申请
        CreateBizPayReqInParam inParam = new CreateBizPayReqInParam();
        inParam.setID(dto.getId());
        inParam.setRequestID(srcBizSys + "_" + dto.getId());//请求ID
        inParam.setPaymentBasis(3);
        inParam.setRequestUnit(dto.getPayUnitId());//单位编号内码
        inParam.setRequestUnitName(dto.getPayUnitName());//单位编号名称
        //inParam.setPayerSwiftCode();//付款人SWIFT码
        inParam.setPayAccount(dto.getPayAccountId());//付款账号内码
        inParam.setPayAccountNo(dto.getPayAccountNo());//付款账号
        inParam.setPayAccountName(dto.getPayAccountName());//付款户名
        //if (dto.getCashFlowItemCode() != null && !dto.getCashFlowItemCode().isEmpty()) {
        //    inParam.setCashFlowItem(dto.getCashFlowItemId());//现金流量项目********
        //}
        inParam.setExpSettleWay(dto.getSettlementWayId());//期望结算方式
        //if (dto.getBizItemCode() != null && !dto.getBizItemCode().isEmpty()) {
        //    inParam.setBizItem(dto.getBizItemId());//业务事项
        //}
        if (null != dto.getExpectPayDate()) {
            inParam.setExpPayDate(dto.getExpectPayDate());//期望付款日期
        } else {
            inParam.setExpPayDate(new Date());
        }
        inParam.setIsUrgent(false);//是否加急
        inParam.setCurrency(dto.getCurrencyId());//币种
        inParam.setRequestAmount(totalAmount);//申请金额
        inParam.setTransCurrency(dto.getCurrencyId());
        inParam.setTransExchangeRate(BigDecimal.ONE);
        inParam.setTransAmount(totalAmount);
        inParam.setPrivateFlag(1);//对方性质	1：往来单位，2：内部员工
        inParam.setIsdeduction(false);//是否主扣
        inParam.setSummary(dto.getSummary());//摘要(去掉非法字符)
        inParam.setDescription(description.toString());//详细说明
        inParam.setSrcBizSys(dto.getSrcBizSys());//来源业务系统
        inParam.setSrcBizType(null);
        inParam.setExpenseDocType(0);
        //主扣时(9：单位主扣；13：资金中心主扣)实际付款日期必填，设置为期望付款日期
        inParam.setActualPayDate(null);
//        inParam.setSrcDocType(dto.getSrcDocType());//来源单据类型主键(来源业务系统_共享单据类型)
        inParam.setSrcDocType("BizPaymentRequest");
//        String userId = CAFContext.current.getUserId();
//        String userName = CAFContext.current.getCurrentSession().getUserName();

        String userId = "";
        String userName = "";
        String sqlOfUser = "SELECT GSPUSER.ID as USERID, JTGKPAYMENTINFO.APPLICANTNAME as USERNAME FROM JTGKPAYMENTINFO LEFT JOIN JTGKPAYPLANDETAIL on JTGKPAYMENTINFO.ID = JTGKPAYPLANDETAIL.LOGINFOID LEFT JOIN GSPUSER on JTGKPAYMENTINFO.APPLICANTCODE = GSPUSER.CODE WHERE JTGKPAYPLANDETAIL.ID = '" + dto.getId() + "'";
        List<Map<String, Object>> rowsOfUser = DBUtil.querySql(sqlOfUser);
        if (rowsOfUser != null && !rowsOfUser.isEmpty()) {
            userId = rowsOfUser.get(0).get("USERID").toString();
            userName = rowsOfUser.get(0).get("USERNAME").toString();
        }else{
            throw new JfskException("goldwind-unipay-tm", "CreateBizPayReqInParam", "获取申请人信息失败", null);
        }

        inParam.setRequestUser(userId);//申请人
        inParam.setRequestUserName(userName);//申请人名称
        if (null != dto.getExpectPayDate()) {
            inParam.setRequestDate(dto.getExpectPayDate());
        } else {
            inParam.setRequestDate(new Date());
        }
        //付款用途
        //inParam.setFundsUse(dto.getFundsUseId());
        inParam.setIsSharingProcess(false);
        inParam.setIsBRPCProcess(true);
        inParam.setStartProcess(true);
        //业务收付类型
//        inParam.setExtBizType("Nomal");
        inParam.setExtBizType("439eee73-52c4-b7a9-f6a7-2aab0e9373a5");
        inParam.setDocBizType("JYXFK");
        inParam.setSrcDocID(dto.getId());
        inParam.setSrcDocNo(dto.getSrcDocNo());
//        inParam.setAccountingUnit(dto.getPayUnitId());
        inParam.setPayUnit(dto.getPayUnitId());
        inParam.setPayUnitName(dto.getPayUnitName());

        //业务明细
        List<BizPayReqDetail> reqDetails = new ArrayList<>();
        inParam.setBizPayReqDetails(reqDetails);
        for (JfskPayPlanDetailEntity detail : details) {
            BizPayReqDetail bizPayReqDetail = new BizPayReqDetail();
            reqDetails.add(bizPayReqDetail);
            bizPayReqDetail.setID(detail.getId());
            bizPayReqDetail.setBizItem(null);
            bizPayReqDetail.setFundNature(detail.getFundnatureId());
            bizPayReqDetail.setFundPlanIds(null);
            bizPayReqDetail.setFundPlanNos(null);
            bizPayReqDetail.setExpSettleWay(detail.getSettlementWayId());
            bizPayReqDetail.setCashFlowItem(null);
            bizPayReqDetail.setCurrency(detail.getCurrencyId());
            bizPayReqDetail.setRequestAmount(detail.getUseAmount());//申请金额
            bizPayReqDetail.setDescription(detail.getSummary());//详细说明
            bizPayReqDetail.setSrcDocID(detail.getId());//来源单据主键
            bizPayReqDetail.setSrcDocNo(detail.getSrcDocNo());//来源单据编号
            bizPayReqDetail.setSrcBizID(detail.getId());//来源业务主键
            if (!StringUtil.isNullOrEmpty(detail.getReceivingUnitId())) {
                bizPayReqDetail.setReceivingUnit(detail.getReceivingUnitId());
                bizPayReqDetail.setReceivingUnitName(detail.getReceivingUnitName());//收款单位名称
            }
        }

        //业务支付申请收款方信息
        List<BizPayReqReceiver> receivers = new ArrayList<>();
        inParam.setBizPayReqReceivers(receivers);
        BizPayReqReceiver bizPayReqReceiver = new BizPayReqReceiver();
        receivers.add(bizPayReqReceiver);
        bizPayReqReceiver.setID(dto.getId());

        // 新增内容
        bizPayReqReceiver.setTXT05(dto.getSrcDocNo());
        bizPayReqReceiver.setTXT07(dto.getSrcBizSys());
        bizPayReqReceiver.setTXT08(docNo);
        bizPayReqReceiver.setTXT09(dto.getSrcDocId());
        bizPayReqReceiver.setTXT10(userName);

        // 获取linkurl
        String sql = "SELECT JTGKPAYPLANBILL.ID as ID FROM JTGKPAYPLANBILL LEFT JOIN JTGKPAYPLANDETAIL on JTGKPAYPLANBILL.ID = JTGKPAYPLANDETAIL.PARENTID WHERE JTGKPAYPLANDETAIL.ID = '" + dto.getId() + "'";
        List<Map<String, Object>> rowsOfLinkUrl = DBUtil.querySql(sql);
        if (rowsOfLinkUrl != null && !rowsOfLinkUrl.isEmpty()) {
//            bizPayReqReceiver.setTXT10(rowsOfLinkUrl.get(0).get("LINKURL").toString());
            bizPayReqReceiver.setTXT11(rowsOfLinkUrl.get(0).get("ID").toString());
        }


        bizPayReqReceiver.setPayAccount(dto.getPayAccountId());
        bizPayReqReceiver.setPayAccountName(dto.getPayAccountName());
        bizPayReqReceiver.setPayAccountNo(dto.getPayAccountNo());
        bizPayReqReceiver.setIsBankCommPay(null);
        // ******** 未传入往来单位编号时不保存往来单位名称，因为退汇认领时做了校验
        if (!StringUtil.isNullOrEmpty(dto.getReceivingUnitId())) {
            bizPayReqReceiver.setReceivingUnit(dto.getReceivingUnitId());
            bizPayReqReceiver.setReceivingUnitName(dto.getReceivingUnitName());//收款单位名称
        }
        bizPayReqReceiver.setReceivingAccount(dto.getReceivingBankAccountId());//收款银行账号
        bizPayReqReceiver.setReceivingAccountNo(dto.getReceivingBankAccountNo());//收款银行账号
        bizPayReqReceiver.setReceivingAccountName(dto.getReceivingBankAccountName());//收款户名
        bizPayReqReceiver.setReceivingAccountBank(dto.getReceivingBankId());//收款银行
        bizPayReqReceiver.setReceivingAccountBankName(dto.getReceivingBankName());//收款银行名称
        bizPayReqReceiver.setReciprocalCountry(null);//对方国家
        bizPayReqReceiver.setReciprocalProvinceName(null);//对方省份名称
        bizPayReqReceiver.setReciprocalCityName(null);//对方城市名称
        if (null != dto.getExpectPayDate()) {
            bizPayReqReceiver.setExpPayDate(dto.getExpectPayDate());//期望付款日期
        } else {
            bizPayReqReceiver.setExpPayDate(new Date());//期望付款日期
        }
        //对方性质	1：往来单位，2：内部员工
        bizPayReqReceiver.setPrivateFlag(1);
        //是否对私账户
        bizPayReqReceiver.setIsPrivateAccount(false);
        bizPayReqReceiver.setCurrency(dto.getCurrencyId());//币种
        bizPayReqReceiver.setRequestAmount(totalAmount);//申请金额
        bizPayReqReceiver.setTransCurrency(dto.getCurrencyId());
        bizPayReqReceiver.setTransAmount(totalAmount);
        bizPayReqReceiver.setTransExchangeRate(BigDecimal.ONE);
//        bizPayReqReceiver.setSummary(dto.getSummary());//摘要
        bizPayReqReceiver.setSummary("SFS-" + dto.getSrcDocNo() + "-" + docNo);
//        bizPayReqReceiver.setDescription(dto.getDescription());//详细说明
        bizPayReqReceiver.setDescription(note);//详细说明
        //********
        bizPayReqReceiver.setSrcDocDetailID(dto.getId());
        //******** 由产品保存票据支付明细
        String billPayWaySQL = "SELECT distinct JTGKPAYPLANBILL.BILLPAYWAY FROM JTGKPAYPLANBILL inner join JTGKPAYPLANDETAIL on JTGKPAYPLANBILL.ID = JTGKPAYPLANDETAIL.PARENTID where JTGKPAYPLANDETAIL.ID = '" + dto.getId() + "'";
        List<Map<String, Object>> rowsOfBillPayWay = DBUtil.querySql(billPayWaySQL);
        if (rowsOfBillPayWay != null && !rowsOfBillPayWay.isEmpty()) {
            dto.setBillPayWay(Integer.parseInt(rowsOfBillPayWay.get(0).get("BILLPAYWAY").toString()));
        }

        bizPayReqReceiver.setBILLPAYWAY(dto.getBillPayWay());
        if (null != dto.getBillPayWay() && 1 == dto.getBillPayWay()) {
            bizPayReqReceiver.setNEWBILLFLAG('1');
        }
        bizPayReqReceiver.setSUPPLYCHAINPRODUCTS(null);
        bizPayReqReceiver.setExpSettleWay(dto.getSettlementWayId());
        bizPayReqReceiver.setIsUrgent(false);//是否加急
        bizPayReqReceiver.setBizItem(null);
        bizPayReqReceiver.setCashFlowItem(null);
        // 20250327自定义字段
        bizPayReqReceiver.setTXT01(dto.getTxt01());
        bizPayReqReceiver.setTXT02(dto.getTxt02());
        bizPayReqReceiver.setTXT03(dto.getTxt03());
        bizPayReqReceiver.setTXT04(dto.getSrcPayMethodName());
        // bizPayReqReceiver.setTXT05(dto.getTxt05());
        bizPayReqReceiver.setTXT06(dto.getTxt06());
//        bizPayReqReceiver.setTXT07(dto.getTxt07());
//        bizPayReqReceiver.setTXT08(dto.getTxt08());
//        bizPayReqReceiver.setTXT09(dto.getTxt09());
//         bizPayReqReceiver.setTXT10(dto.getTxt10());
        // bizPayReqReceiver.setTXT11(dto.getTxt11());
        bizPayReqReceiver.setTXT12(dto.getTxt12());
        bizPayReqReceiver.setTXT13(dto.getTxt13());
        bizPayReqReceiver.setTXT14(dto.getTxt14());
        bizPayReqReceiver.setTXT15(dto.getTxt15());
        bizPayReqReceiver.setTXT16(dto.getTxt16());
        bizPayReqReceiver.setTXT17(dto.getTxt17());
        bizPayReqReceiver.setTXT18(dto.getTxt18());
        bizPayReqReceiver.setTXT19(dto.getTxt19());
        bizPayReqReceiver.setTXT20(dto.getTxt20());
        bizPayReqReceiver.setFK02(dto.getExtPurchaseCode());
        bizPayReqReceiver.setFK03(dto.getExtCostCenter());
        bizPayReqReceiver.setFK04(dto.getExtProjectCode());
        bizPayReqReceiver.setFK05(dto.getExtInnerOrder());
        bizPayReqReceiver.setFK06(dto.getExtProfitCenter());

        //20250311 背书票据明细
        List<BizPaymentBillDetail> billDetails = new ArrayList<>();
        inParam.setBizPaymentBillDetails(billDetails);
        for (JfskPayPlanBillEntity bill : bills) {
            BizPaymentBillDetail bizPaymentBillDetail = new BizPaymentBillDetail();
            billDetails.add(bizPaymentBillDetail);
            bizPaymentBillDetail.setBillpayway("" + dto.getBillPayWay());
            bizPaymentBillDetail.setId(bill.getId());
            bizPaymentBillDetail.setPayabledetailid(bizPayReqReceiver.getID());
            bizPaymentBillDetail.setParentid(inParam.getID());
            bizPaymentBillDetail.setReceiverid(bizPayReqReceiver.getID());
            bizPaymentBillDetail.setBillid(bill.getBillId());
            bizPaymentBillDetail.setBillno(bill.getBillNo());
            bizPaymentBillDetail.setUseamount(bill.getRequestAmount());
            bizPaymentBillDetail.setSubbillstartsn(bill.getSubbillStartsn());
            bizPaymentBillDetail.setSubbillendsn(bill.getSubbillEndSn());
            String selectBill = "SELECT ID,BILLTYPE,BILLFORM,BILLNO,BILLOPENDATE,BILLDUEDATE,BILLAMT,RECEIVERUNIT,RECEIVERUNITNAME,DRAWERNAME,DRAWERACCOUNTNO,DRAWERBANKNAME,ACCEPTORNAME,AVAENDORSE,NEWBILLFLAG,SPLITFLAG,LASTINDORSERID,LASTINDORSERNAME\n" +
                    "FROM TMBILLRECEIVABLEINVENTORY where ID='" + bill.getBillId() + "'";
            log.info(selectBill);
            List<Map<String, Object>> rowsOfBill = DBUtil.querySql(selectBill);
            log.info(JSON.toJSONString(rowsOfBill));
            if (rowsOfBill != null && !rowsOfBill.isEmpty()) {
                Map<String, Object> rowOfBill = rowsOfBill.get(0);
                bizPaymentBillDetail.setBalanceamount(null);
                bizPaymentBillDetail.setBilltype((rowOfBill.get("BILLTYPE") != null) ? rowOfBill.get("BILLTYPE").toString() : null);
                bizPaymentBillDetail.setBillform((rowOfBill.get("BILLFORM") != null) ? (Integer)rowOfBill.get("BILLFORM") : null);
                bizPaymentBillDetail.setDraweraccountno((rowOfBill.get("DRAWERACCOUNTNO") != null) ? rowOfBill.get("DRAWERACCOUNTNO").toString() : null);
                bizPaymentBillDetail.setDrawername((rowOfBill.get("DRAWERNAME") != null) ? rowOfBill.get("DRAWERNAME").toString() : null);
                bizPaymentBillDetail.setAcceptorname((rowOfBill.get("ACCEPTORNAME") != null) ? rowOfBill.get("ACCEPTORNAME").toString() : null);
                bizPaymentBillDetail.setAvaendorse((rowOfBill.get("AVAENDORSE") != null) ? rowOfBill.get("AVAENDORSE").toString() : null);
                bizPaymentBillDetail.setNewbillflag((rowOfBill.get("NEWBILLFLAG") != null) ? (Character)rowOfBill.get("NEWBILLFLAG") : null);
                bizPaymentBillDetail.setSplitflag((rowOfBill.get("SPLITFLAG") != null) ? (Character)rowOfBill.get("SPLITFLAG") : null);
                bizPaymentBillDetail.setReceivingunit((rowOfBill.get("RECEIVERUNIT") != null) ? rowOfBill.get("RECEIVERUNIT").toString() : null);
                bizPaymentBillDetail.setReceivingunitname((rowOfBill.get("RECEIVERUNITNAME") != null) ? rowOfBill.get("RECEIVERUNITNAME").toString() : null);
                bizPaymentBillDetail.setBillopendate((rowOfBill.get("BILLOPENDATE") != null) ? (Date)rowOfBill.get("BILLOPENDATE") : null);
                if (rowOfBill.get("BILLDUEDATE") != null) {
                    String billDueDate = sdf.format((Date)rowOfBill.get("BILLDUEDATE"));
                    bizPaymentBillDetail.setBillduedate(billDueDate);
                }
                bizPaymentBillDetail.setBillamount((rowOfBill.get("BILLAMT") != null) ? (BigDecimal)rowOfBill.get("BILLAMT") : null);
                bizPaymentBillDetail.setLastindorserid((rowOfBill.get("LASTINDORSERID") != null) ? rowOfBill.get("LASTINDORSERID").toString() : null);
                bizPaymentBillDetail.setLastindorsername((rowOfBill.get("LASTINDORSERNAME") != null) ? rowOfBill.get("LASTINDORSERNAME").toString() : null);
            }
            bizPaymentBillDetail.setCreatedby(userId);
            bizPaymentBillDetail.setCreatedon(new Date());
            bizPaymentBillDetail.setLastChangedby(userId);
            bizPaymentBillDetail.setLastChangedon(new Date());
        }

        String json = JSONSerializer.serialize(inParam);
        log.info("产品接口入参封装完成：{}", json);
        return inParam;
    }
}
