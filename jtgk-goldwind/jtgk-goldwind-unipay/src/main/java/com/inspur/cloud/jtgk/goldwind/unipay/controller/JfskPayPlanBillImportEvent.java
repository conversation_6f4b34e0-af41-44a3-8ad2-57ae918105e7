package com.inspur.cloud.jtgk.goldwind.unipay.controller;

import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.JfskException;
import com.inspur.fastdweb.mgrcard.FilterEvent;
import com.inspur.fastdweb.model.excel.ExcelMXInfo;
import com.inspur.fastdweb.model.qry.Qry;
import com.inspur.fastdweb.service.excel.IExcelImportEvent;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.rpc.api.service.RpcClient;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 票据补录导出excel事件
 */
@Controller
@Slf4j
public class JfskPayPlanBillImportEvent implements IExcelImportEvent {

    public void beforeExcelExport(List<List<Object>> dataList, ExcelMXInfo mxInfo, Map<String, Object> mapCustom) {
        log.info("导出Excel事件：dataList=" + JSON.toJSONString(dataList));
        log.info("导出Excel事件：mxInfo=" + JSON.toJSONString(mxInfo));
        log.info("导出Excel事件：mapCustom=" + JSON.toJSONString(mapCustom));
        Map<String, String> enumData = mxInfo.enumData;
        if (enumData == null || enumData.isEmpty()) {

            String sqlString = "SELECT * FROM (\n" +
                    "    SELECT \n" +
                    "        CREATOR.NAME_CHS AS APPLICANT, -- 申请人\n" +
                    "        PD.REQUESTDEPTNAME AS APPLY_DEPT, -- 申请部门\n" +
                    "        TO_CHAR(PD.TIMESTAMPS_CREATEDON, 'YYYY-MM-DD HH24:MI:SS') AS APPLY_DATE, -- 申请日期\n" +
                    "        PB1.DOCNO AS PAYMENT_PLAN_NO, -- 付款安排编号\n" +
                    "        SRCPAYMETHOD.TXT02 AS PAYMENT_METHOD_CODE, -- 付款方式编号-头表\n" +
                    "        SRCPAYMETHOD.TXT01 AS PAYMENT_METHOD_NAME, -- 付款方式-头表\n" +
                    "        SRCPAYMETHOD.TXT03 AS BILL_ACCEPTANCE_SUBJECT, -- 汇票承兑科目\n" +
                    "        PD.SRCDOCNO AS PAYMENT_REQUEST_NO, -- 付款申请编号\n" +
                    "        PD.RECEIVINGUNITCODE AS SUPPLIER_CODE, -- 供应商编号\n" +
                    "        PD.RECEIVINGUNITNAME AS SUPPLIER_NAME, -- 供应商名称\n" +
                    "        ORG.CODE AS COMPANY_CODE, -- 公司代码\n" +
                    "        ORG.NAME_CHS AS COMPANY_NAME, -- 公司名称\n" +
                    "        PD.REQUESTAMOUNT AS PURCHASE_PAYABLE_AMOUNT, -- 采购付款应付金额\n" +
                    "        PD.AMOUNT AS CURRENT_PAYMENT_AMOUNT, -- 本次支付金额\n" +
                    "        PI.REQUESTAMOUNT, -- 申请金额\n" +
                    "        coalesce((select SUM(AMOUNT) from JTGKPAYMENTDETAIL where DOCSTATUS in (1,5) and PARENTID=PI.ID),0.0) as PAIDAMOUNT, -- 已付款金额\n" +
                    "        coalesce((select SUM(AMOUNT) from JTGKPAYMENTDETAIL where DOCSTATUS=0 and PARENTID=PI.ID),0.0) AS PAYINGAMOUNT, -- 付款中金额\n" +
                    "        PI.REQUESTAMOUNT - coalesce((select SUM(AMOUNT) from JTGKPAYPLANDETAIL where DOCSTATUS in (0,1,2) and LOGINFOID=PI.ID),0.0) - coalesce((select SUM(AMOUNT) from JTGKPAYMENTDETAIL where DOCSTATUS in (0,1,5) and PARENTID=PI.ID),0.0) AS UNPAYAMOUNT, -- 剩余待付款金额\n" +
                    "        CUR.CODE AS CURRENCY, -- 币种\n" +
                    "        COALESCE(PD.SUMMARY, PB1.NOTE) AS REMARK, -- 备注\n" +
                    "        PD.EXTPURCHASECODE AS PURCHASE_ORG, -- 采购组织\n" +
                    "        PD.EXTPROFITCENTER AS BUSINESS_CENTER, -- 业务中心\n" +
                    "        PI.TXT01 AS FUND_PLAN_NO, -- 资金计划编号\n" +
                    "        ST.NAME_CHS AS PAYMENT_METHOD_DESC, -- 付款方式-说明\n" +
                    "        CASE PD.DOCSTATUS \n" +
                    "            WHEN 0 THEN '制单'\n" +
                    "            WHEN 1 THEN '待复核'\n" +
                    "            WHEN 2 THEN '待办理'\n" +
                    "            WHEN 3 THEN '已完成'\n" +
                    "            WHEN -1 THEN '退回'\n" +
                    "            ELSE ''\n" +
                    "        END AS AUDIT_STATUS, -- 审核状态\n" +
                    "        CASE PB1.DOCSTATUS \n" +
                    "            WHEN 0 THEN '制单'\n" +
                    "            WHEN 1 THEN '审批中'\n" +
                    "            WHEN 2 THEN '审批通过'\n" +
                    "            WHEN 3 THEN '审批退回'\n" +
                    "            WHEN -1 THEN '退回或废弃'\n" +
                    "            ELSE ''\n" +
                    "        END AS PAYMENT_PLAN_STATUS, -- 付款安排表状态\n" +
                    "        CASE PB2.DOCSTATUS \n" +
                    "            WHEN 0 THEN '待复核'\n" +
                    "            WHEN 1 THEN '复核中'\n" +
                    "            WHEN 2 THEN '复核通过'\n" +
                    "            WHEN 3 THEN '复核退回'\n" +
                    "            WHEN -1 THEN '退回或废弃'\n" +
                    "            WHEN 4 THEN '审批退回'\n" +
                    "            ELSE ''\n" +
                    "        END AS PAYMENT_PLAN_CHECK_STATUS, -- 付款安排复核表状态\n" +
                    "        CASE PB3.DOCSTATUS \n" +
                    "            WHEN 0 THEN '待办理'\n" +
                    "            WHEN 1 THEN '办理中'\n" +
                    "            WHEN 2 THEN '办理完成'\n" +
                    "            WHEN 3 THEN '办理退回'\n" +
                    "            WHEN -1 THEN '退回或废弃'\n" +
                    "            ELSE ''\n" +
                    "        END AS BILL_SUPPLEMENT_STATUS -- 票据补录表状态\n" +
                    "    FROM JTGKPAYPLANDETAIL PD\n" +
                    "    INNER JOIN JTGKPAYPLANBILL PB1 ON PD.PARENTID = PB1.ID\n" +
                    "    LEFT JOIN JTGKPAYPLANBILL2 PB2 ON PD.PARENTID2 = PB2.ID\n" +
                    "    LEFT JOIN JTGKPAYPLANBILL3 PB3 ON PD.PARENTID3 = PB3.ID\n" +
                    "    LEFT JOIN BFADMINORGANIZATION ORG ON PD.PAYUNITID = ORG.ID\n" +
                    "    LEFT JOIN BFCURRENCY CUR ON PD.CURRENCYID = CUR.ID\n" +
                    "    LEFT JOIN BFSETTLEMENTWAY ST ON PD.SETTLEMENTWAYID = ST.ID\n" +
                    "    LEFT JOIN IDD_DATADICTIONARY SRCPAYMETHOD ON PB1.TXT01 = SRCPAYMETHOD.ID \n" +
                    "        AND SRCPAYMETHOD.CATEGORYID = '3a6b4bb2-2548-6bf3-c17b-3ce189e2081c'\n" +
                    "    LEFT JOIN GSPUSER CREATOR ON PD.TIMESTAMPS_CREATEDBY = CREATOR.ID\n" +
                    "    LEFT JOIN JTGKPAYMENTINFO PI ON PD.LOGINFOID = PI.ID\n" +
                    "    WHERE PB1.DOCSTATUS IS NOT NULL\n" +
                    ") AS T WHERE 1=1 /*sqlwhere*/ LIMIT " + mxInfo.pageSize + " OFFSET " + (mxInfo.page - 1) * mxInfo.pageSize;

            List<Map<String, Object>> list = DBUtil.querySql(sqlString);
            mapCustom.put("data", list);
        }else {
            String docId = enumData.get("DOCID");
            String isEBC = enumData.get("ISEBC");
            String type = enumData.get("TYPE");
            if (StringUtils.equals(type, "BILL")) {
                if (StringUtil.isNullOrEmpty(docId)) {
                    log.error("票据补录导出Excel时未传入当前单据ID");
                    throw new JfskException("票据补录导出Excel时未传入当前单据ID");
                }
                String sqlStatement;
                try {
                    LinkedHashMap<String, Object> rpcGetQryPams = new LinkedHashMap<>();
                    rpcGetQryPams.put("qryId", "dbdf5f86-e3b9-e9a4-99a5-4b232dbe35bc");
                    Qry qryEntity;
                    try {
                        RpcClient rpcClient = SpringBeanUtils.getBean(RpcClient.class);
                        qryEntity = rpcClient.invoke(Qry.class, "idp.rpc.common.data.service.getQry", "Idp", rpcGetQryPams, null);
                    } catch (Throwable ex) {
                        log.error("没有找到指定的IDP查询");
                        throw new JfskException("没有找到指定的IDP查询", ex);
                    }
                    sqlStatement = qryEntity.qSql.replace("_CHS", "_CHS");
                } catch (Throwable ex) {
                    log.error("获取取数SQL时发生异常：", ex);
                    throw new JfskException("获取取数SQL时发生异常", ex);
                }
                mxInfo.page = 1;
                mxInfo.pageSize = 5000;
                String filter = " and PPB.PARENTID='" + docId + "'";
                Map paramObject = new HashMap();
                StringBuilder selectSql = new StringBuilder();
                selectSql.append(sqlStatement.replace("/*sqlwhere*/", filter));
                if ("false".equals(isEBC)) {
                    selectSql.append(" and (COALESCE(INV.ISEBC, '0') <> '1' or COALESCE(PAA.ISEBDIRECT, '0') <> '1') order by PPB.ROWNO");
                } else {
                    selectSql.append(" order by PPB.ROWNO");
                }
                log.info(selectSql.toString());
                mapCustom.put("sql", selectSql.toString());
                mapCustom.put("params", paramObject);
            }else if (StringUtils.equals(type, "PAYDETAIL")) {

                mxInfo.page = 1;
                mxInfo.pageSize = 5000;

                String sqlString = "select\n" +
                        "\tJTGKPAYPLANDETAIL.TIMESTAMPS_CREATEDON,\n" +
                        "\tJTGKPAYPLANDETAIL.RECIPROCALCOUNTRY,\n" +
                        "\tJTGKPAYPLANDETAIL.TIMESTAMPS_CREATEDBY,\n" +
                        "\tJTGKPAYPLANDETAIL.ISPRIVATEACCOUNT,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT01,\n" +
                        "\tJTGKPAYPLANDETAIL.AMT01,\n" +
                        "\tJTGKPAYPLANDETAIL.EXPECTPAYDATE,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT09,\n" +
                        "\tJTGKPAYPLANDETAIL.RECEIVINGUNITNAME,\n" +
                        "\tJTGKPAYPLANDETAIL.RECEIVINGBANKNAME,\n" +
                        "\tJTGKPAYPLANDETAIL.PAYUNITID,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT05,\n" +
                        "\tJTGKPAYPLANDETAIL.SUBBILLSTARTSN,\n" +
                        "\tJTGKPAYPLANDETAIL.SRCBIZSYS,\n" +
                        "\tJTGKPAYPLANDETAIL.BILLPAYWAY,\n" +
                        "\tJTGKPAYPLANDETAIL.AMT02,\n" +
                        "\tJTGKPAYPLANDETAIL.AMT03,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT03,\n" +
                        "\tJTGKPAYPLANDETAIL.AMT04,\n" +
                        "\tJTGKPAYPLANDETAIL.DATE01,\n" +
                        "\tJTGKPAYPLANDETAIL.LOGINFOID,\n" +
                        "\tJTGKPAYPLANDETAIL.BILLNO,\n" +
                        "\tJTGKPAYPLANDETAIL.RECEIVINGBANKACCOUNTNAME,\n" +
                        "\tJTGKPAYPLANDETAIL.SRCDOCTYPE,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT04,\n" +
                        "\tJTGKPAYPLANDETAIL.TRANSCURRENCYID,\n" +
                        "\tJTGKPAYPLANDETAIL.SRCDOCID,\n" +
                        "\tJTGKPAYPLANDETAIL.TRANSEXCHANGERATE,\n" +
                        "\tJTGKPAYPLANDETAIL.RECEIVINGBANKID,\n" +
                        "\tJTGKPAYPLANDETAIL.BILLID,\n" +
                        "\tJTGKPAYPLANDETAIL.TRANSAMOUNT,\n" +
                        "\tJTGKPAYPLANDETAIL.EXCELID,\n" +
                        "\tJTGKPAYPLANDETAIL.BILLDUEDATE,\n" +
                        "\tJTGKPAYPLANDETAIL.RECEIVINGBANKNO,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT06,\n" +
                        "\tJTGKPAYPLANDETAIL.REQUESTDEPTID,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT07,\n" +
                        "\tJTGKPAYPLANDETAIL.SETTLEMENTWAYID,\n" +
                        "\tJTGKPAYPLANDETAIL.RECIPROCALPROVINCE,\n" +
                        "\tJTGKPAYPLANDETAIL.PRIVATEFLAG,\n" +
                        "\tJTGKPAYPLANDETAIL.PAYACCOUNTNO,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT02,\n" +
                        "\tJTGKPAYPLANDETAIL.PAYACCOUNTNAME,\n" +
                        "\tJTGKPAYPLANDETAIL.PAYACCOUNTID,\n" +
                        "\tJTGKPAYPLANDETAIL.IFACCEPTANCEBILL,\n" +
                        "\tJTGKPAYPLANDETAIL.RECEIVINGBANKACCOUNTNO,\n" +
                        "\tJTGKPAYPLANDETAIL.PARENTID,\n" +
                        "\tJTGKPAYPLANDETAIL.PAYUNITNAME,\n" +
                        "\tJTGKPAYPLANDETAIL.SUMMARY,\n" +
                        "\tJTGKPAYPLANDETAIL.PARENTID3,\n" +
                        "\tJTGKPAYPLANDETAIL.TIMESTAMPS_LASTCHANGEDON,\n" +
                        "\tJTGKPAYPLANDETAIL.DATE03,\n" +
                        "\tJTGKPAYPLANDETAIL.DATE05,\n" +
                        "\tJTGKPAYPLANDETAIL.RECEIVINGBANKACCOUNTID,\n" +
                        "\tJTGKPAYPLANDETAIL.AMT05,\n" +
                        "\tJTGKPAYPLANDETAIL.SRCDOCNO,\n" +
                        "\tJTGKPAYPLANDETAIL.DESCRIPTION,\n" +
                        "\tJTGKPAYPLANDETAIL.CASHFLOWITEMID,\n" +
                        "\tJTGKPAYPLANDETAIL.PARENTID2,\n" +
                        "\tJTGKPAYPLANDETAIL.FUNDNATUREID,\n" +
                        "\tJTGKPAYPLANDETAIL.TIMESTAMPS_LASTCHANGEDBY,\n" +
                        "\tJTGKPAYPLANDETAIL.DATE02,\n" +
                        "\tJTGKPAYPLANDETAIL.REQUESTAMOUNT,\n" +
                        "\tJTGKPAYPLANDETAIL.RECEIVINGUNITCODE,\n" +
                        "\tJTGKPAYPLANDETAIL.DATE04,\n" +
                        "\tJTGKPAYPLANDETAIL.RECEIVINGUNITID,\n" +
                        "\tJTGKPAYPLANDETAIL.REQUESTDEPTNAME,\n" +
                        "\tJTGKPAYPLANDETAIL.SUBBILLENDSN,\n" +
                        "\tJTGKPAYPLANDETAIL.CURRENCYID,\n" +
                        "\tJTGKPAYPLANDETAIL.DOCSTATUS,\n" +
                        "\tJTGKPAYPLANDETAIL.ID,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT08,\n" +
                        "\tJTGKPAYPLANDETAIL.RECIPROCALCITY,\n" +
                        "\tJTGKPAYPLANDETAIL.USEAMOUNT,\n" +
                        "\tJTGKPAYPLANDETAIL.ROWNO,\n" +
                        "\tJTGKPAYPLANDETAIL.REFBILLID,\n" +
                        "\tJTGKPAYPLANDETAIL.BILLAMT,\n" +
                        "\tJTGKPAYPLANDETAIL.EXTPROFITCENTER,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT10,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT19,\n" +
                        "\tJTGKPAYPLANDETAIL.EXTINNERORDER,\n" +
                        "\tJTGKPAYPLANDETAIL.EXTPROJECTCODE,\n" +
                        "\tJTGKPAYPLANDETAIL.EXTCOSTCENTER,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT11,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT12,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT13,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT14,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT15,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT16,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT17,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT18,\n" +
                        "\tJTGKPAYPLANDETAIL.TXT20,\n" +
                        "\tJTGKPAYPLANDETAIL.EXTPURCHASECODE,\n" +
                        "\tJTGKPAYPLANDETAIL.AMT08,\n" +
                        "\tJTGKPAYPLANDETAIL.AMT09,\n" +
                        "\tJTGKPAYPLANDETAIL.AMOUNT,\n" +
                        "\tJTGKPAYPLANDETAIL.AMT06,\n" +
                        "\tJTGKPAYPLANDETAIL.AMT07,\n" +
                        "\tJTGKPAYPLANDETAIL.SRCPAYMETHODNAME,\n" +
                        "\tJTGKPAYPLANDETAIL.UNPAYAMOUNT,\n" +
                        "\tJTGKPAYPLANDETAIL.PAYINGAMOUNT,\n" +
                        "\tJTGKPAYPLANDETAIL.AMT10,\n" +
                        "\tJTGKPAYPLANDETAIL.DATE10,\n" +
                        "\tJTGKPAYPLANDETAIL.SRCPAYMETHODCODE,\n" +
                        "\tJTGKPAYPLANDETAIL.PAIDAMOUNT,\n" +
                        "\tJTGKPAYPLANDETAIL.DATE06,\n" +
                        "\tJTGKPAYPLANDETAIL.DATE07,\n" +
                        "\tJTGKPAYPLANDETAIL.DATE08,\n" +
                        "\tJTGKPAYPLANDETAIL.DATE09,\n" +
                        "\tBFCURRENCY.CODE as CURRENCYID_CODE,\n" +
                        "\tBFCURRENCY.NAME_CHS as CURRENCYID_NAME_CHS,\n" +
                        "\tTRANSCURRENCY.CODE as TRANSCURRENCYID_CODE,\n" +
                        "\tTRANSCURRENCY.NAME_CHS as TRANSCURRENCYID_NAME_CHS,\n" +
                        "\tBFFUNDSPROP.NAME_CHS as FUNDNATUREID_NAME_CHS,\n" +
                        "\tIDD_DATADICTIONARY.NAME as SRCBIZSYS_NAME,\n" +
                        "\tTMBILLRECEIVABLEINVENTORY.NEWBILLFLAG as BILLID_NEWBILLFLAG,\n" +
                        "\tTMBILLRECEIVABLEINVENTORY.BILLFORM as BILLID_BILLFORM,\n" +
                        "\tTMBILLRECEIVABLEINVENTORY.BILLAMT as BILLID_BILLAMT,\n" +
                        "\tTMBILLRECEIVABLEINVENTORY.BILLTYPE as BILLID_BILLTYPE,\n" +
                        "\tBFSETTLEMENTWAY.NAME_CHS as SETTLEMENTWAYID_NAME_CHS\n" +
                        "from\n" +
                        "\tJTGKPAYPLANDETAIL\n" +
                        "left join BFCURRENCY BFCURRENCY on\n" +
                        "\tJTGKPAYPLANDETAIL.CURRENCYID = BFCURRENCY.ID\n" +
                        "left join BFCURRENCY TRANSCURRENCY on\n" +
                        "\tJTGKPAYPLANDETAIL.TRANSCURRENCYID = TRANSCURRENCY.ID\n" +
                        "left join BFFUNDSPROP BFFUNDSPROP on\n" +
                        "\tJTGKPAYPLANDETAIL.FUNDNATUREID = BFFUNDSPROP.ID\n" +
                        "left join IDD_DATADICTIONARY IDD_DATADICTIONARY on\n" +
                        "\tJTGKPAYPLANDETAIL.SRCBIZSYS = IDD_DATADICTIONARY.CODE\n" +
                        "\tand IDD_DATADICTIONARY.CATEGORYID = '25c76693-a046-de5a-c474-49a8af20f2a8'\n" +
                        "left join TMBILLRECEIVABLEINVENTORY TMBILLRECEIVABLEINVENTORY on\n" +
                        "\tJTGKPAYPLANDETAIL.BILLID = TMBILLRECEIVABLEINVENTORY.ID\n" +
                        "left join BFSETTLEMENTWAY BFSETTLEMENTWAY on\n" +
                        "\tJTGKPAYPLANDETAIL.SETTLEMENTWAYID = BFSETTLEMENTWAY.ID\n" +
                        "inner join JTGKPAYPLANBILL3 on JTGKPAYPLANDETAIL.PARENTID3 = JTGKPAYPLANBILL3.ID\n" +
                        "where\n" +
                        "\t1 = 1 /*sqlwhere*/ and JTGKPAYPLANBILL3.id ='" + docId + "'";

                List<Map<String, Object>> list = DBUtil.querySql(sqlString);
                mapCustom.put("data", list);
            }
        }
    }
}
