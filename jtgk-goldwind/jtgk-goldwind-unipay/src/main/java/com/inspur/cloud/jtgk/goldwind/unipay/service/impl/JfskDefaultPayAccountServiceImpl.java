package com.inspur.cloud.jtgk.goldwind.unipay.service.impl;

import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.unipay.config.JfskUnipayConst;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.R;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.RD;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskBankAccountEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.service.JfskDefaultPayAccountService;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.idd.log.api.controller.LogService;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 内部接口：默认付款账户
 */
@Service
@Slf4j
public class JfskDefaultPayAccountServiceImpl implements JfskDefaultPayAccountService {
    /**
     * SFS系统：根据付款公司、支付方式、采购组织获取默认付款账户
     * @param logService 接口日志
     * @param srcBizSys 来源系统标识
     * @param srcDocNo 来源单据编号
     * @param payUnitCode 付款公司编号
     * @param purchaseCode 采购组织编号
     * @param payMethod 支付方式编号
     * @return 默认付款账户
     */
    @Override
    public RD<JfskBankAccountEntity> getDefaultPayAccount(LogService logService, String srcBizSys, String srcDocNo, String payUnitCode, String purchaseCode, String payMethod) {
        if (JfskUnipayConst.SFS_FiPay.equals(payMethod) || JfskUnipayConst.SFS_FiDiscountTPay.equals(payMethod) || JfskUnipayConst.SFS_CredPay.equals(payMethod) || JfskUnipayConst.SFS_OfflinePay.equals(payMethod) || JfskUnipayConst.SFS_DZDHPay.equals(payMethod)) {
            log.info("1、SFS系统传入支付方式（财企直联、金风云信、线下网银）时：");
            logService.info(srcDocNo, "SFS系统传入支付方式（财企直联、金风云信）时：");
            logService.info(srcDocNo, "单位+付款方式+采购组织/单位+付款方式");
            if (JfskUnipayConst.SFS_CredPay.equals(payMethod)){
                String selectGetFiAccounts = "select A.ID,A.ACCOUNTNO,A.ACCOUNTNAME_CHS,A.OPENACCOUNTUNIT from BFBANKACCOUNTS A\n" +
                        "inner join BFBANK B on A.BANK=B.ID inner join BFADMINORGANIZATION O on A.OPENACCOUNTUNIT=O.ID\n" +
                        "where B.BANKIDENTIFIER='" + JfskUnipayConst.FiBankCode + "' and O.CODE='" + payUnitCode + "'";
                log.info(selectGetFiAccounts);
                List<Map<String, Object>> rowsOfFiAccounts = DBUtil.querySql(selectGetFiAccounts);
                log.info(JSON.toJSONString(rowsOfFiAccounts));
                if (rowsOfFiAccounts == null || rowsOfFiAccounts.isEmpty()) {
                    log.error("查询默认财司账户失败（兜底操作）：openAccountUnit=" + payUnitCode + ", bankCode=" + JfskUnipayConst.FiBankCode);
                    logService.error(srcDocNo, "查询默认财司账户失败（兜底操作）：openAccountUnit=" + payUnitCode + ", bankCode=" + JfskUnipayConst.FiBankCode);
                    return RD.error("查询默认财司账户失败（兜底操作）");
                }
                JfskBankAccountEntity account = new JfskBankAccountEntity() {
                    {
                        setId((String) rowsOfFiAccounts.get(0).get("ID"));
                        setAccountNo((String) rowsOfFiAccounts.get(0).get("ACCOUNTNO"));
                        setAccountName((String) rowsOfFiAccounts.get(0).get("ACCOUNTNAME_CHS"));
                        setOpenAccountUnit((String) rowsOfFiAccounts.get(0).get("OPENACCOUNTUNIT"));
                    }
                };
                return RD.ok(null, account);
            }else{
                RD<JfskBankAccountEntity> getDefaultPayAccount = getDefaultPayAccountByPayMethod(logService, srcBizSys, srcDocNo, payUnitCode, purchaseCode, payMethod);
                if (!getDefaultPayAccount.getResult()) {
                    logService.info(srcDocNo, "获取单位开立的财司户");
                    String selectGetFiAccounts = "select A.ID,A.ACCOUNTNO,A.ACCOUNTNAME_CHS,A.OPENACCOUNTUNIT from BFBANKACCOUNTS A\n" +
                            "inner join BFBANK B on A.BANK=B.ID inner join BFADMINORGANIZATION O on A.OPENACCOUNTUNIT=O.ID\n" +
                            "where B.BANKIDENTIFIER='" + JfskUnipayConst.FiBankCode + "' and O.CODE='" + payUnitCode + "'";
                    log.info(selectGetFiAccounts);
                    List<Map<String, Object>> rowsOfFiAccounts = DBUtil.querySql(selectGetFiAccounts);
                    log.info(JSON.toJSONString(rowsOfFiAccounts));
                    if (rowsOfFiAccounts == null || rowsOfFiAccounts.isEmpty()) {
                        log.error("查询默认财司账户失败（兜底操作）：openAccountUnit=" + payUnitCode + ", bankCode=" + JfskUnipayConst.FiBankCode);
                        logService.error(srcDocNo, "查询默认财司账户失败（兜底操作）：openAccountUnit=" + payUnitCode + ", bankCode=" + JfskUnipayConst.FiBankCode);
                        return RD.error("查询默认财司账户失败（兜底操作）");
                    }
                    JfskBankAccountEntity account = new JfskBankAccountEntity() {
                        {
                            setId((String) rowsOfFiAccounts.get(0).get("ID"));
                            setAccountNo((String) rowsOfFiAccounts.get(0).get("ACCOUNTNO"));
                            setAccountName((String) rowsOfFiAccounts.get(0).get("ACCOUNTNAME_CHS"));
                            setOpenAccountUnit((String) rowsOfFiAccounts.get(0).get("OPENACCOUNTUNIT"));
                        }
                    };
                    return RD.ok(null, account);
                }else{
                    return getDefaultPayAccount;
                }
            }


        }else {
            log.info("3、SFS系统票据类、信用类等支付方式：允许为空");
            logService.info(srcDocNo, "3、SFS系统票据类、信用类等支付方式：允许为空");
            return RD.ok("SFS系统票据类、信用类等支付方式时付款账户允许为空", null);
        }
    }

    private RD<JfskBankAccountEntity> getDefaultPayAccountByPayMethod(
            LogService logService, String srcBizSys, String srcDocNo, String payUnitCode, String purchaseCode, String payMethod
    ) {
        String selectConfig = "select ID,SRCBIZSYS,PAYUNIT,PURCHASEORG,PAYMENTMETHOD,BANKACCOUNT,VOUCHERACCOUNT from JFKJPAYMETHODSETTING where SRCBIZSYS='" + srcBizSys + "' and PAYUNIT='" + payUnitCode + "'";
        log.info(selectConfig);
        List<Map<String, Object>> rowsOfConfig = DBUtil.querySql(selectConfig);
        log.info(JSON.toJSONString(rowsOfConfig));
        if (rowsOfConfig == null || rowsOfConfig.isEmpty()) {
            String error = "未找到默认账户配置：srcBizSys=" + srcBizSys + ", payUnit=" + payUnitCode;
            log.error(error);
            logService.error(srcDocNo, error);
            return RD.error(error);
        }
        RD<String> getAccount = getDefaultAccountByPayMethodCore(logService, rowsOfConfig, srcBizSys, srcDocNo, payUnitCode, payMethod, purchaseCode);
        if (getAccount.getResult()) {
            String accountNo = getAccount.getData();
            String selectOfPayAcct = "select ID,ACCOUNTNO,ACCOUNTNAME_CHS,OPENACCOUNTUNIT,ACCOUNTSTATUS,ONLINEBANKOPENSTATUS from BFBANKACCOUNTS where ACCOUNTSTATUS=2 and ACCOUNTNO=?1";
            log.info(selectOfPayAcct + ", ?1=" + accountNo);
            List<Map<String, Object>> rowsOfPayAcct = DBUtil.querySql(selectOfPayAcct, accountNo);
            log.info(JSON.toJSONString(rowsOfPayAcct));
            if (rowsOfPayAcct == null || rowsOfPayAcct.isEmpty()) {
                log.error("付款账号无效：" + accountNo);
                logService.error(srcDocNo, "付款账号无效：" + accountNo);
                return RD.error("付款账号无效");
            }
            JfskBankAccountEntity entity = new JfskBankAccountEntity();
            entity.setId((String)rowsOfPayAcct.get(0).get("ID"));
            entity.setAccountNo((String)rowsOfPayAcct.get(0).get("ACCOUNTNO"));
            entity.setAccountName((String)rowsOfPayAcct.get(0).get("ACCOUNTNAME_CHS"));
            entity.setAccountStatus((Integer) rowsOfPayAcct.get(0).get("ACCOUNTSTATUS"));
            entity.setOpenAccountUnit((String)rowsOfPayAcct.get(0).get("OPENACCOUNTUNIT"));
            return RD.ok(null, entity);
        }
        return RD.error(getAccount.getMessage());
    }

    /**
     * 按照支付方式、采购组织从配置表查询默认付款账号
     * @param logService 接口日志
     * @param rowsOfConfig 按来源系统和付款单位过滤的配置表
     * @param srcBizSys 来源系统标识
     * @param srcDocNo 来源单据编号
     * @param payUnitCode 付款单位编号
     * @param payMethod 支付方式编号
     * @param purchaseCode 采购组织编号
     * @return 匹配的付款账户
     */
    public static RD<String> getDefaultAccountByPayMethodCore(LogService logService, List<Map<String, Object>> rowsOfConfig, String srcBizSys, String srcDocNo, String payUnitCode, String payMethod, String purchaseCode) {
        // 付款单位只有一个付款账户
        if (rowsOfConfig.size() == 1) {
            String accountNo = (String)rowsOfConfig.get(0).get("BANKACCOUNT");
            return RD.ok(null, accountNo);
        }
        // 按支付方式区分
        List<Map<String, Object>> rowsOfPayMethod = getDefaultAccountByPayMethod(rowsOfConfig, payMethod);
        if (rowsOfPayMethod == null || rowsOfPayMethod.isEmpty()) {
            String error = "未根据支付方式找到默认账户：srcBizSys=" + srcBizSys + ", payUnit=" + payUnitCode + ", payMethod=" + payMethod + ", purchaseCode=" + purchaseCode;
            log.error(error);
            logService.error(srcDocNo, error);
            return RD.error(error);
        }

        // 是否按采购组织区分
        List<Map<String, Object>> getConfigByPurchase = getDefaultAccountByPurchase(rowsOfPayMethod, purchaseCode);
        if (getConfigByPurchase == null || getConfigByPurchase.isEmpty()) {
            String accountNo = (String)rowsOfPayMethod.get(0).get("BANKACCOUNT");
            return RD.ok(null, accountNo);
        }else{
            String accountNo = (String)getConfigByPurchase.get(0).get("BANKACCOUNT");
            return RD.ok(null, accountNo);
        }
    }

    /**
     * 根据支付方式过滤配置数据
     * @param rows 要过滤的配置数据
     * @param payMethod 支付方式
     * @return 过滤后的配置数据
     */
    private static List<Map<String, Object>> getDefaultAccountByPayMethod(List<Map<String, Object>> rows, String payMethod) {
        if (!StringUtil.isNullOrEmpty(payMethod)) {
            // 根据支付方式匹配
            List<Map<String, Object>> rowsOfFilter = rows.stream().filter(o -> o.get("PAYMENTMETHOD") != null && payMethod.equals(o.get("PAYMENTMETHOD"))).collect(Collectors.toList());
            if (!rowsOfFilter.isEmpty()) {
                return rowsOfFilter;
            }
        }
        // 未匹配支付方式 or 未定义支付方式
        List<Map<String, Object>> rowsOfNull = rows.stream().filter(o -> StringUtil.isNullOrEmpty((String)o.get("PAYMENTMETHOD"))).collect(Collectors.toList());
        if (!rowsOfNull.isEmpty()) {
            return rowsOfNull;
        }
        return null;
    }

    /**
     * 根据采购组织过滤配置数据
     * @param rows 要过滤的配置数据
     * @param purchaseCode 采购组织编号
     * @return 过滤后的配置数据
     */
    private static List<Map<String, Object>> getDefaultAccountByPurchase(List<Map<String, Object>> rows, String purchaseCode) {
        if (!StringUtil.isNullOrEmpty(purchaseCode)) {
            // 根据采购组织匹配
            List<Map<String, Object>> rowsOfFilter = rows.stream().filter(o -> o.get("PURCHASEORG") != null && purchaseCode.equals(o.get("PURCHASEORG"))).collect(Collectors.toList());
            if (!rowsOfFilter.isEmpty()) {
                return rowsOfFilter;
            }
        }
        // 未匹配采购组织 or 未定义采购组织
        List<Map<String, Object>> rowsOfNull = rows.stream().filter(o -> StringUtil.isNullOrEmpty((String)o.get("PURCHASEORG"))).collect(Collectors.toList());
        if (!rowsOfNull.isEmpty()) {
            return rowsOfNull;
        }
        return null;
    }

    /**
     * CES、HLY：根据付款公司、会计科目编号获取默认付款账户
     * @param logService 接口日志
     * @param srcBizSys 来源系统标识
     * @param srcDocNo 来源单据编号
     * @param payUnitCode 付款公司编号
     * @param subjectCode 会计科目编号
     * @return 默认付款账户
     */
    @Override
    public RD<JfskBankAccountEntity> getDefaultPayAccount(LogService logService, String srcBizSys, String srcDocNo, String payUnitCode, String subjectCode) {
        String selectConfig = "select ID,SRCBIZSYS,PAYUNIT,PURCHASEORG,PAYMENTMETHOD,BANKACCOUNT,VOUCHERACCOUNT from JFKJPAYMETHODSETTING where SRCBIZSYS='" + srcBizSys + "' and PAYUNIT='" + payUnitCode + "'";
        log.info(selectConfig);
        List<Map<String, Object>> rowsOfConfig = DBUtil.querySql(selectConfig);
        log.info(JSON.toJSONString(rowsOfConfig));
        RD<String> getDefaultAccount = getDefaultPayAccountBySubjectCore(logService, srcBizSys, srcDocNo, payUnitCode, subjectCode, rowsOfConfig);
        if (!getDefaultAccount.getResult()) {
            return RD.error(getDefaultAccount.getMessage());
        }
        String accountNo = getDefaultAccount.getData();
        String selectOfPayAcct = "select ID,ACCOUNTNO,ACCOUNTNAME_CHS,OPENACCOUNTUNIT,ACCOUNTSTATUS,ONLINEBANKOPENSTATUS from BFBANKACCOUNTS where ACCOUNTSTATUS=2 and ACCOUNTNO=?1";
        log.info(selectOfPayAcct + ", ?1=" + accountNo);
        List<Map<String, Object>> rowsOfPayAcct = DBUtil.querySql(selectOfPayAcct, accountNo);
        log.info(JSON.toJSONString(rowsOfPayAcct));
        if (rowsOfPayAcct == null || rowsOfPayAcct.isEmpty()) {
            log.error("付款账号无效：" + accountNo);
            logService.error(srcDocNo, "付款账号无效：" + accountNo);
            return RD.error("付款账号无效");
        }
        JfskBankAccountEntity entity = new JfskBankAccountEntity();
        entity.setId((String)rowsOfPayAcct.get(0).get("ID"));
        entity.setAccountNo((String)rowsOfPayAcct.get(0).get("ACCOUNTNO"));
        entity.setAccountName((String)rowsOfPayAcct.get(0).get("ACCOUNTNAME_CHS"));
        entity.setAccountStatus((Integer) rowsOfPayAcct.get(0).get("ACCOUNTSTATUS"));
        entity.setOpenAccountUnit((String)rowsOfPayAcct.get(0).get("OPENACCOUNTUNIT"));
        log.info("默认付款账号：" + JSON.toJSONString(entity));
        return RD.ok(null, entity);
    }

    public static RD<String> getDefaultPayAccountBySubjectCore(
            LogService logService, String srcBizSys, String srcDocNo, String payUnitCode, String subjectCode, List<Map<String, Object>> rowsOfConfig
    ) {
        if (rowsOfConfig == null || rowsOfConfig.isEmpty()) {
            log.error("1.1、未找到默认账户配置：srcBizSys=" + srcBizSys + ", payUnit=" + payUnitCode);
            logService.error(srcDocNo, "未找到默认账户配置：srcBizSys=" + srcBizSys + ", payUnit=" + payUnitCode);
            return RD.error("未找到默认账户配置");
        } else {
            if (rowsOfConfig.size() == 1) {
                String accountNo = (String) rowsOfConfig.get(0).get("BANKACCOUNT");
                log.info("1.2、只有一条默认付款账户：accountNo=");
                logService.info(srcDocNo, "只有一条默认付款账户：accountNo=");
                return RD.ok(null, accountNo);
            } else {
                log.info("1.3、多条默认付款账户时：根据科目编号再匹配");
                if (StringUtil.isNullOrEmpty(subjectCode)) {
                    log.error("1.3.1、会计科目编号不能为空");
                    logService.error(srcDocNo, "会计科目编号不能为空");
                    return RD.error("会计科目编号不能为空");
                } else {
                    // 根据科目编号匹配
                    List<Map<String, Object>> rowsOfFilter = rowsOfConfig.stream()
                            .filter(o -> o.get("VOUCHERACCOUNT") != null && subjectCode.equals(o.get("VOUCHERACCOUNT")))
                            .collect(Collectors.toList());
                    if (!rowsOfFilter.isEmpty()) {
                        String accountNo = (String) rowsOfFilter.get(0).get("BANKACCOUNT");
                        log.info("1.3.2.1、根据科目编号找到默认账户：accountNo=" + accountNo);
                        logService.info(srcDocNo, "根据科目编号找到默认账户：accountNo=" + accountNo);
                        return RD.ok(null, accountNo);
                    } else {
                        log.error("1.3.2.2、未根据科目编号找到默认账户：srcBizSys=" + srcBizSys + ", payUnit=" + payUnitCode + ", subjectCode=" + subjectCode);
                        logService.error(srcDocNo, "未根据科目编号找到默认账户：srcBizSys=" + srcBizSys + ", payUnit=" + payUnitCode + ", subjectCode=" + subjectCode);
                        return RD.error("未根据科目编号找到默认账户");
                    }
                }
            }
        }
    }
}
