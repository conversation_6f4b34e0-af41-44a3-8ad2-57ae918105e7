package com.inspur.cloud.jtgk.goldwind.unipay.service;

import com.inspur.cloud.jtgk.goldwind.unipay.dto.R;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.RD;
import com.inspur.idd.log.api.controller.LogService;

import java.util.List;
import java.util.Map;

/**
 * 付款安排单
 */
public interface JfskPayBillService {
    /**
     * 内部接口：复核退回
     * @param docId 复核单据ID
     * @param docNo 单据编号
     * @return 处理结果
     */
    R checkBack(String docId, String docNo);

    /**
     * 内部接口：票据退回
     * @param docId 复核单据ID
     * @param docNo 单据编号
     * @return 处理结果
     */
    R checkBillBack(String docId, String docNo);

    /**
     * 内部接口：复核通过
     * @param logService 接口日志
     * @param docId 复核单据ID
     * @param docNo 单据编号
     * @return 处理结果
     */
    R checkPass(LogService logService, String docId, String docNo, boolean onlyCheck);

    /**
     * 内部接口：票据背书办理完成
     * @param logService 接口日志
     * @param docId 票据背书办理表ID
     * @param docNo 单据编号
     * @return 处理结果
     */
    R handlePass(LogService logService, String docId, String docNo);

    /**
     * 内部接口：批量复核通过 - 支持并行处理以提高性能
     * @param logService 接口日志
     * @param billInfos 批量单据信息列表
     * @return 批量处理结果
     */
    RD<List<Map<String, Object>>> batchCheckPassAndSubmit(LogService logService, List<Map<String, Object>> billInfos);
}
