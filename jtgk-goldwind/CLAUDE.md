# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

KEEP IN MIND, MOST IMPORTANT!!!!

Whenever you want to ask a question, always call the MCP tool : mcp-feedback-enhanced.

Whenever you're about to complete a user request, call the mcp-feedback-enhanced instead of simply ending the process. You should call the mcp-feedback-enhanced immediately before you want to end the ending the process to check if user want to do something else.

Always reply in Chinese.

IMPORTANT!!!!!




## Project Overview

This is a **financial treasury management system** for GoldWind (金风科技) built on Java 8 and Maven. The system handles financial operations including bill management, payment processing, settlement transactions, and RPA automation for banking operations.

**Key naming conventions:**
- `jtgk` = 金投公司 (Financial Investment Company)
- `jfkj` = 金风科技 (appears in many class names)
- `Jfsk` = Class name prefix for most business entities

## Build Commands

```bash
# Build all modules
mvn clean install

# Build specific module
cd jtgk-goldwind-unipay && mvn clean package

# Run tests (module-specific)
cd jtgk-goldwind-unipay && mvn test

# Clean build artifacts
mvn clean
```

## Module Architecture

### **jtgk-goldwind-unipay** (Core Payment Module)
- **Purpose**: Unified payment processing and settlement management
- **API Base**: `/jtgk/goldwind/settlement/v1.0/`, `/jtgk/goldwind/unipaybill/v1.0/`
- **Key Services**: `JfskUnipayService`, `JfskPayBillService`, `JfskAllocationBillService`
- **External Integrations**: OA system (汇联易), bank payment systems

### **jtgk-goldwind-jk-jcsj** (Interface & Data Collection)
- **Purpose**: External system interfaces and basic data management (接口及基础数据)
- **Key Features**: Bank integrations, account management, WeChat notifications
- **Resources**: Extensive JavaScript files for frontend extensions under `/资金管理/`
- **API Base**: `/jtgk/goldwind/ckgl/v1.0/`

### **jtgk-goldwind-billmanage** (Bill Management)
- **Purpose**: Financial bill and document management
- **Technology**: Heavy use of Apache Axis2 for web services
- **API Base**: `/jtgk/goldwind/billmanage/v1.0/`

### **jtgk-goldwind-gn-transcation** (Transaction Processing)
- **Purpose**: Transaction processing and settlement operations
- **Key Entities**: Claims, payment requests, settlements

### **jtgk-goldwind-rpa** (RPA Automation)
- **Purpose**: Robotic Process Automation for banking operations
- **Key Features**: Bank balance queries, transaction processing, XML data handling

## Technology Stack

- **Java 8** (Maven compiler target)
- **Inspur EDP Platform** (enterprise framework)
- **Spring Framework** (version varies by module)
- **Spring Data JPA** for data persistence
- **FastDWeb** (Inspur's web framework)
- **Hutool** (Chinese Java utility library)
- **Apache Axis2** (web services, particularly in billmanage)

## Architecture Patterns

1. **Layered Architecture**: API → Controller → Service → Repository → Entity
2. **Multi-module Maven Structure**: Independent deployable modules
3. **RESTful APIs**: JAX-RS annotations with custom REST endpoints
4. **Repository Pattern**: Spring Data JPA repositories
5. **Configuration-based Injection**: Spring `@Configuration` classes

## Special Setup Requirements

### Font Configuration for PDF Generation
- **Issue**: PDF generation may show garbled Chinese characters
- **Solution**: Place `.ttf` font files in the project root directory
- **Known unsupported fonts**: 楷体_GB2312, PingFang SC, PingFang SC Bold

### Local JAR Dependencies
Each module has `/libs/` directory containing:
- `idd-log-api.jar` (logging framework)
- `caf-message-api.jar` (messaging framework, some modules)
- `fastdweb.jar`, `fssp-pf-api.jar` (unipay module)
- `tm-cm-paymentsettlementidp-core.jar` (payment settlement, unipay module)

### Development Tools
- **Hot Reload**: `rebel.xml` and `rebel-remote.xml` files configured for development
- **IDE Support**: IntelliJ IDEA project files (`.iml`) included

## Key Integration Points

- **Bank Systems**: RPA module handles bank API integrations with XML processing
- **OA Systems**: Workflow and approval integrations (particularly in unipay)
- **SAP Integration**: Bill pushing to SAP systems
- **WeChat**: Message notifications and interfaces (jk-jcsj module)

## Database and Entity Management

- **JPA Entities**: Each module manages its own entities in `entity/` packages
- **Repositories**: Spring Data JPA repositories in `repository/` packages
- **SQL Scripts**: Available in `src/main/resources/sql/` and `src/main/resources/table/`

## Common Development Patterns

- **Service Layer**: Business logic in `service/` packages with interface/implementation pattern
- **API Controllers**: RESTful endpoints in `controller/` packages
- **Configuration**: Spring `@Configuration` classes in `config/` packages
- **Utilities**: Helper classes in `utils/` and `pub/` packages
- **Exception Handling**: Custom exceptions like `JfskException`, `JTGKExtendException`

## Module Communication

- **Independent Deployment**: Each module can be deployed separately
- **RESTful APIs**: Inter-module communication via REST endpoints
- **Shared Dependencies**: Common Inspur platform libraries across modules

## Zen Gemini
Study the code properly, think deeply about what this does and then see if there's any room for improvement in
terms of performance optimizations, brainstorm with gemini on this to get feedback and then confirm any change by
first adding a unit test with `measure` and measuring current code and then implementing the optimization and
measuring again to ensure it improved, then share results. Check with gemini in between as you make tweaks. Remember, do not be hesitate to ask gemini whether code worked as expected after you created/updated code.